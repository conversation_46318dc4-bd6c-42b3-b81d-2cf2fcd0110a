#!/usr/bin/env python3
"""
Test script to verify rotation angle naming functionality
"""

from full_map_processor import FullMapProcessor
import os

def test_rotation_naming():
    """Test file naming with different rotation angles"""
    
    print("🧪 Testing rotation angle naming functionality...")
    print("=" * 60)
    
    # Test file
    test_file = 'test/3AA111-01-B4.map'
    if not os.path.exists(test_file):
        print(f"❌ Test file not found: {test_file}")
        return
    
    # Test different rotation angles
    rotation_angles = [0, 90, 180, 270]
    expected_suffixes = ["_R0", "_R90", "_R180", "_R270"]
    
    for angle, expected_suffix in zip(rotation_angles, expected_suffixes):
        print(f"\n📐 Testing {angle}° rotation...")
        
        # Create processor
        processor = FullMapProcessor()
        processor.set_rotation_angle(angle)
        processor.set_filter_empty(True)
        
        # Process file
        result = processor.process_multiple_files([test_file])
        
        if result:
            # Check if filename contains expected suffix
            if expected_suffix in result:
                print(f"✅ {angle}° - Correct naming: {result}")
            else:
                print(f"❌ {angle}° - Missing suffix '{expected_suffix}': {result}")
        else:
            print(f"❌ {angle}° - Failed to generate file")
    
    print("\n" + "=" * 60)
    print("🎯 Test completed!")

if __name__ == "__main__":
    test_rotation_naming()
