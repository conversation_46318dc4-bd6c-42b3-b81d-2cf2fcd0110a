#!/usr/bin/env python3
"""
测试Bump Map Advanced Tool GUI
验证新的动态配置界面和功能

Author: Yuribytes
Company: Chipone TE development Team
"""

import tkinter as tk
from tkinter import ttk
import sys
import os

# 添加父目录到路径
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, parent_dir)

from bump_map_advanced_frame import BumpMapAdvancedFrame

class TestController:
    """测试控制器"""
    def __init__(self, root):
        self.root = root
    
    def return_to_selector_with_confirmation(self, tool_name, has_unsaved):
        print(f"Would return to selector from {tool_name}, unsaved: {has_unsaved}")
        return True

def test_advanced_gui():
    """测试Advanced GUI"""
    print("🧪 测试Bump Map Advanced Tool GUI")
    print("=" * 50)
    
    root = tk.Tk()
    root.title("Bump Map Advanced Tool - GUI Test")
    root.geometry("1000x800")
    
    # 设置样式
    style = ttk.Style()
    style.theme_use('clam')
    
    controller = TestController(root)
    advanced_tool = BumpMapAdvancedFrame(root, controller)
    advanced_tool.show()
    
    # 创建测试控制面板
    test_frame = ttk.Frame(root)
    test_frame.pack(side=tk.BOTTOM, fill=tk.X, padx=10, pady=10)
    
    ttk.Label(test_frame, text="GUI测试控制:", font=("Arial", 12, "bold")).pack(anchor=tk.W)
    
    def set_test_files():
        """设置测试文件"""
        advanced_tool.bump_map_file_path.set("D97127.09")
        advanced_tool.dummy_map_file_path.set("009.NNS157-09-E4")
        print("✅ 测试文件已设置")
    
    def set_custom_values():
        """设置自定义值"""
        advanced_tool.pass_value.set(100)
        advanced_tool.fail_value.set(200)
        print("✅ 自定义值已设置: Pass=100, Fail=200")
    
    def test_format_preview():
        """测试格式预览"""
        advanced_tool.pass_value.set(255)
        advanced_tool.fail_value.set(128)
        print("✅ 格式预览测试: Pass=255, Fail=128")
    
    def reset_values():
        """重置为默认值"""
        advanced_tool.pass_value.set(63)
        advanced_tool.fail_value.set(59)
        print("✅ 重置为默认值: Pass=63, Fail=59")
    
    # 测试按钮
    button_frame = ttk.Frame(test_frame)
    button_frame.pack(fill=tk.X, pady=(5, 0))
    
    ttk.Button(button_frame, text="设置测试文件", 
              command=set_test_files).pack(side=tk.LEFT, padx=(0, 10))
    ttk.Button(button_frame, text="设置自定义值", 
              command=set_custom_values).pack(side=tk.LEFT, padx=(0, 10))
    ttk.Button(button_frame, text="测试格式预览", 
              command=test_format_preview).pack(side=tk.LEFT, padx=(0, 10))
    ttk.Button(button_frame, text="重置默认值", 
              command=reset_values).pack(side=tk.LEFT, padx=(0, 10))
    
    print("✅ Advanced GUI测试界面已启动")
    print("   - 测试动态配置功能")
    print("   - 测试格式预览")
    print("   - 测试文件选择")
    print("   - 测试处理功能")
    
    root.mainloop()

def main():
    """主测试函数"""
    print("🧪 Bump Map Advanced Tool GUI测试")
    print("=" * 70)
    
    # 切换到测试目录
    os.chdir(os.path.dirname(os.path.abspath(__file__)))
    
    try:
        test_advanced_gui()
        return True
    except Exception as e:
        print(f"❌ GUI测试出错: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
