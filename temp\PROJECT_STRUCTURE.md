# TSK/MAP Tool - 项目文件结构说明

## 📁 当前项目结构

### 主项目文件夹 (开发和编译文件)
```
tsk_map_develop_08072025/
├── main.py                     # 主程序入口
├── tool_selector.py            # 工具选择界面
├── ab_map_tool.py             # AB Map 工具主界面
├── ab_comparison_analyzer.py   # AB 对比分析核心
├── tsk_map_gui.py             # TSK/MAP GUI 界面
├── tsk_map_processor.py       # TSK/MAP 文件处理器
├── full_map_gui.py            # Full Map 工具界面
├── full_map_processor.py      # Full Map 处理器
├── launcher_gui.py            # 启动器界面
├── excel_output.py            # Excel 输出模块
├── config_reader.py           # 配置读取模块
├── build_gui_exe.py           # GUI 版本构建脚本
├── requirements.txt           # Python 依赖列表
├── dist/                      # 编译输出目录
│   ├── TSK_MAP_Tool.exe      # 最新 GUI 版本 ⭐
│   └── [其他构建版本...]
├── SOFTWARE_USER_MANUAL.md   # 详细使用说明 📖
├── QUICK_START_GUIDE.md      # 快速入门指南 🚀
└── PROJECT_STRUCTURE.md      # 本文件
```

### temp/ 文件夹 (非必要文件)
```
temp/
├── docs/                      # 构建文档
│   ├── BUILD_INSTRUCTIONS.md
│   ├── EXE_BUILD_SUMMARY.md
│   └── GUI_BUILD_FINAL_SUMMARY.md
├── build_scripts/             # 构建脚本
│   ├── build_exe.py
│   ├── build_simple_exe.py
│   ├── create_distribution_package.py
│   └── create_gui_distribution.py
├── specs/                     # PyInstaller 规格文件
│   ├── TSK_MAP_Tool.spec
│   └── version_info.py
├── archives/                  # 分发包存档
│   └── TSK_MAP_Tool_GUI_Complete_*.zip
├── FEATURES_OVERVIEW.md       # 功能概述
├── QUICK_START_GUIDE.md       # 旧版快速指南
├── USER_MANUAL.md            # 旧版用户手册
└── build_simple.bat          # 批处理构建脚本
```

## 🎯 核心文件说明

### 开发文件
- **main.py**: 程序主入口，处理工具选择和启动
- **tool_selector.py**: 工具选择界面，支持窗口关闭处理
- **ab_comparison_analyzer.py**: AB 对比分析的核心算法
- **excel_output.py**: Excel 报告生成模块

### 编译文件
- **build_gui_exe.py**: GUI 版本构建脚本（无控制台窗口）
- **dist/TSK_MAP_Tool.exe**: 最终的 GUI 版本可执行文件

### 用户文档
- **SOFTWARE_USER_MANUAL.md**: 完整的软件使用说明
- **QUICK_START_GUIDE.md**: 5分钟快速入门指南

## 📋 文件整理原则

### 保留在主目录的文件
✅ **Python 源代码文件** (.py)
✅ **当前构建脚本** (build_gui_exe.py)
✅ **依赖配置** (requirements.txt)
✅ **编译输出** (dist/ 文件夹)
✅ **用户文档** (使用说明和快速指南)

### 移动到 temp/ 的文件
📦 **历史构建脚本** → temp/build_scripts/
📦 **构建文档** → temp/docs/
📦 **PyInstaller 规格文件** → temp/specs/
📦 **分发包存档** → temp/archives/
📦 **旧版文档** → temp/

## 🚀 使用建议

### 对于最终用户
1. 只需要 `dist/TSK_MAP_Tool.exe` 文件
2. 参考 `SOFTWARE_USER_MANUAL.md` 了解详细使用方法
3. 查看 `QUICK_START_GUIDE.md` 快速上手

### 对于开发人员
1. 主目录包含所有开发必需文件
2. 使用 `build_gui_exe.py` 重新构建 GUI 版本
3. temp/ 文件夹包含历史文件和参考资料

### 对于维护人员
1. 项目结构清晰，便于维护
2. 非必要文件已分类存放
3. 构建过程文档完整保存

## 📊 文件大小统计

### 主要文件
- **TSK_MAP_Tool.exe**: 11.9 MB (最终产品)
- **Python 源代码**: ~200 KB (所有 .py 文件)
- **用户文档**: ~50 KB (说明文档)

### temp/ 文件夹
- **构建脚本**: ~100 KB
- **文档资料**: ~200 KB  
- **分发包**: ~12 MB (压缩包)

## 🔄 维护建议

### 定期清理
- 清理 dist/ 中的旧版本文件
- 整理 temp/archives/ 中的历史包
- 更新用户文档

### 版本管理
- 保持主目录的简洁性
- 重要的历史版本移至 temp/archives/
- 及时更新文档版本信息

### 备份策略
- 主目录：完整备份（开发环境）
- temp/ 文件夹：选择性备份（参考资料）
- dist/TSK_MAP_Tool.exe：重点备份（最终产品）

---

**整理完成**: 项目文件夹已按开发需求优化，主目录保持简洁，非必要文件已妥善归档。
