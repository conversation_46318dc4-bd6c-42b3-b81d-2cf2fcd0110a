任务流程：
针对Bump map To Tsk map Tool工具进行完善:
    1，你创建了增强版的UI界面，请检查之前的UI设计，需要完全复用之前的功能，我查看clear memory按钮不存在，Back 按钮不返回主界面。请检查之前的设计内容，clear memory和back和exit都会清理内存，并且弹出界面也要保持统一化，这部分在新的UI界面下，请检查是否都实现，如果没有，请设计代码。
    2，Map version detect显示部分，不用显示NEPES compatible，只显示合理的map version就可以。之前的设计就不错
    3，Dynamic Configuration，设定Pass 默认是0，fail 默认是59，如果修改再执行修改部分，如果不修改，维持默认执行（pass和fail的格式展示，请维持之前的设计模式，
例如：如果设定pass 是63，mapversion=2/3 Special，003F0000, mapversion =4, big-endian, 0000003F  

上述功能开发注意事项：
     --- 主要功能不要破坏，代码功能不要破坏，架构不要变动
     --- GUI尽量统一，不要占用太多空间
     --- 复用代码部分，尽量做到代码统一

要求：
1，测试脚本和文件请放到test文件夹
2，生成的md说明文档，请放到temp文件夹，不能在主项目文件夹下生成
3，程序架构统一，框架统一，代码尽量简洁

测试文件说明：
1，可以使用test文件夹下的D97127.09+D99970.01作为bump map的load
2，可以使用test文件夹下的009.NNS157-09-E4作为dummy map的文件