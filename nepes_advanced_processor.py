#!/usr/bin/env python3
"""
NEPES Advanced Processor - Enhanced version with Map Version 2/3/4 support and dynamic value configuration
Supports all map versions with configurable pass/fail values

Author: Yuribytes
Company: Chipone TE development Team
"""

import os
import re
import struct
from typing import Optional, Dict, Any
from datetime import datetime
from tsk_map_processor import TSKMapProcessor


class NEPESAdvancedProcessor:
    """Advanced NEPES processor with multi-version support and dynamic configuration"""
    
    def __init__(self):
        self.tsk_processor = TSKMapProcessor()
        self.bump_data = []
        self.header_info = {}
        self.rows = 0
        self.cols = 0
        self.total_dies = 0
        
        # TSK format variables
        self.columnsize = 0
        self.rowsize = 0
        self.map_version = 0
        self.TestResultStartPos = 0
        self.TestResultCategory = 0
        
        # Dynamic configuration (new feature)
        self.pass_value = 63  # Default: 63 for "00" positions
        self.fail_value = 59  # Default: 59 for "XX" positions
        
        # Processing statistics
        self.processing_stats = {
            'total_positions': 0,
            'unchanged_positions': 0,    # "__" positions (no modification)
            'pass_positions': 0,         # "00" positions → configurable pass value
            'fail_positions': 0,         # "XX" positions → configurable fail value
            'binary_modifications': 0,
            'errors': 0
        }
    
    def set_pass_value(self, value: int):
        """Set the decimal value for pass positions ("00")"""
        if 0 <= value <= 255:
            self.pass_value = value
            print(f"✅ Pass value set to: {value}")
        else:
            raise ValueError("Pass value must be between 0 and 255")
    
    def set_fail_value(self, value: int):
        """Set the decimal value for fail positions ("XX")"""
        if 0 <= value <= 255:
            self.fail_value = value
            print(f"✅ Fail value set to: {value}")
        else:
            raise ValueError("Fail value must be between 0 and 255")
    
    def load_dummy_map(self, file_path: str) -> bool:
        """Load dummy map and extract TSK format variables"""
        try:
            print(f"📖 Loading dummy map: {file_path}")
            
            # Use TSK processor to read and parse file
            success = self.tsk_processor.read_file(file_path)
            if not success:
                print("❌ Failed to read dummy map file")
                return False
            
            # Parse file header to get required variables
            header_success = self.tsk_processor.parse_file_header()
            if not header_success:
                print("❌ Failed to parse dummy map header")
                return False
            
            # Extract required variables
            self.columnsize = self.tsk_processor.columnsize
            self.rowsize = self.tsk_processor.rowsize
            self.map_version = self.tsk_processor.May_version
            self.TestResultStartPos = self.tsk_processor.TestResultStartPos
            self.TestResultCategory = self.tsk_processor.TestResultCategory
            self.total_dies = self.columnsize * self.rowsize
            
            print(f"✅ Loaded dummy map successfully:")
            print(f"   File size: {len(self.tsk_processor.filearray)} bytes")
            print(f"   Dimensions: {self.rowsize} × {self.columnsize}")
            print(f"   Total dies: {self.total_dies}")
            print(f"   Map version: {self.map_version}")
            print(f"   TestResultStartPos: {self.TestResultStartPos}")
            print(f"   TestResultCategory: {self.TestResultCategory}")
            
            return True
            
        except Exception as e:
            print(f"❌ Error loading dummy map: {e}")
            return False
    
    def parse_bump_map(self, file_path: str) -> bool:
        """Parse NEPES bump map file"""
        try:
            print(f"📖 Parsing NEPES bump map: {file_path}")
            
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
            
            # Extract header information
            self._extract_header_info(content)
            
            # Find and parse RowData lines
            row_data_lines = re.findall(r'RowData:(.*)', content)
            
            if not row_data_lines:
                print("❌ Error: No RowData found in bump map file")
                return False
            
            # Parse each row of data
            self.bump_data = []
            for i, line in enumerate(row_data_lines):
                values = [v.strip() for v in line.strip().split() if v.strip()]
                self.bump_data.append(values)
                
                if i == 0:
                    print(f"   Sample row data: {values[:10]}...")
            
            self.rows = len(self.bump_data)
            self.cols = len(self.bump_data[0]) if self.bump_data else 0
            bump_total = self.rows * self.cols
            
            print(f"✅ Parsed NEPES bump map:")
            print(f"   Dimensions: {self.rows} × {self.cols}")
            print(f"   Total positions: {bump_total}")
            print(f"   Header info: {self.header_info}")
            
            # Verify dimensions match dummy map
            if bump_total != self.total_dies:
                print(f"⚠️  Warning: Bump map total ({bump_total}) != Dummy map total ({self.total_dies})")
            
            return True
            
        except Exception as e:
            print(f"❌ Error parsing NEPES bump map: {e}")
            return False
    
    def _extract_header_info(self, content: str):
        """Extract header information from bump map content"""
        header_patterns = {
            'DEVICE': r'DEVICE\s*:\s*([^\r\n]+)',
            'LOT': r'LOT\s*:\s*([^\r\n]+)',
            'WAFER': r'WAFER\s*:\s*([^\r\n]+)',
            'ROWCT': r'ROWCT\s*:\s*([^\r\n]+)',
            'COLCT': r'COLCT\s*:\s*([^\r\n]+)',
            'REFPX': r'REFPX\s*:\s*([^\r\n]+)',
            'REFPY': r'REFPY\s*:\s*([^\r\n]+)',
            'FNLOC': r'FNLOC\s*:\s*([^\r\n]+)',
            'BCEQU': r'BCEQU\s*:\s*([^\r\n]+)'
        }
        
        self.header_info = {}
        for key, pattern in header_patterns.items():
            match = re.search(pattern, content)
            if match:
                self.header_info[key] = match.group(1).strip()
    
    def get_binary_modification_pattern(self, bump_value: str) -> Optional[bytes]:
        """Get binary modification pattern based on bump map value and map version"""
        
        if bump_value == "__":
            # "__" positions: NO MODIFICATION - return None to indicate no change
            return None
        elif bump_value == "00":
            # Pass: use configurable pass value
            target_value = self.pass_value
        else:
            # Fail (XX, 0C, etc.): use configurable fail value
            target_value = self.fail_value
        
        # Map version specific formatting
        if self.map_version == 4:
            # Map Version 4: Big-Endian format (0000003F)
            return struct.pack('>I', target_value)
        elif self.map_version in [2, 3]:
            # Map Version 2/3: Special format (003F0000) - value in 2nd byte
            return bytes([0x00, target_value, 0x00, 0x00])
        else:
            # Default to Map Version 4 format for unknown versions
            print(f"⚠️  Unknown map version {self.map_version}, using Version 4 format")
            return struct.pack('>I', target_value)
    
    def generate_output_filename(self, bump_path: str, dummy_path: str, custom_output: str = None) -> str:
        """Generate output filename with new format: lot-01 (不包含dummy map名字、时间戳和文件扩展名)"""
        if custom_output:
            return custom_output

        # 获取bump文件的basename（不使用splitext，因为.09等不是真正的扩展名）
        bump_basename = os.path.basename(bump_path)

        # 将点号替换为连字符 (例如: D97127.09 -> D97127-09)
        output_base = bump_basename.replace('.', '-')

        # Create new filename: 只使用bump map名字，格式为lot-01（不包含扩展名）
        output_filename = output_base

        return output_filename

    def apply_advanced_conversion(self):
        """Apply advanced NEPES conversion with multi-version support"""
        if not self.bump_data or not self.tsk_processor.filearray:
            print("❌ Error: Bump data or dummy map not loaded")
            return False

        try:
            print("🔄 Applying advanced NEPES conversion...")
            print(f"   Map version: {self.map_version}")
            print(f"   Pass value: {self.pass_value} (for '00' positions)")
            print(f"   Fail value: {self.fail_value} (for 'XX' positions)")

            if self.map_version == 4:
                print(f"   Format: Big-Endian (0000{self.pass_value:04X})")
            elif self.map_version in [2, 3]:
                print(f"   Format: Special (00{self.pass_value:02X}0000)")

            # Reset statistics
            self.processing_stats = {
                'total_positions': 0,
                'unchanged_positions': 0,
                'pass_positions': 0,
                'fail_positions': 0,
                'binary_modifications': 0,
                'errors': 0
            }

            # Process each position using TSK processor's 1-based indexing logic
            for i in range(1, self.rowsize + 1):  # VB uses 1-based indexing
                for j in range(1, self.columnsize + 1):  # VB uses 1-based indexing
                    # Calculate die index using TSK processor logic
                    die_index = (i - 1) * self.columnsize + j - 1

                    # Check bounds for bump data (2D array)
                    row_index = i - 1  # Convert to 0-based for array access
                    col_index = j - 1  # Convert to 0-based for array access

                    if row_index >= len(self.bump_data) or col_index >= len(self.bump_data[row_index]):
                        continue  # Skip this position if out of bounds

                    # Get bump map value from 2D array
                    bump_value = self.bump_data[row_index][col_index]

                    # Calculate category position using TSK processor logic
                    # TSK processor calls get_binary(filearray, pos, pos+3) which reads filearray[pos-1:pos+4]
                    # So to match TSK logic, we need to write at pos-1
                    category_pos = self.TestResultCategory + 4 * die_index - 1

                    # Convert to 0-based coordinates for display (x = j-1, y = i-1)
                    display_x = j - 1
                    display_y = i - 1

                    # Verify position is within file bounds (need 4 bytes: category_pos to category_pos+3)
                    if category_pos + 4 <= len(self.tsk_processor.filearray):
                            self.processing_stats['total_positions'] += 1

                            # Get binary modification pattern
                            binary_pattern = self.get_binary_modification_pattern(bump_value)

                            if binary_pattern is None:
                                # "__" position - NO MODIFICATION
                                self.processing_stats['unchanged_positions'] += 1
                                # Log first few unchanged positions for verification
                                if self.processing_stats['unchanged_positions'] <= 3:
                                    original_bytes = self.tsk_processor.filearray[category_pos:category_pos+4]
                                    print(f"   Unchanged position x={display_x}, y={display_y}, pos={category_pos}: {original_bytes.hex()}")
                            else:
                                # Apply modification based on map version
                                for k in range(4):
                                    self.tsk_processor.filearray[category_pos + k] = binary_pattern[k]
                                    self.processing_stats['binary_modifications'] += 1

                                # Update statistics
                                if bump_value == "00":
                                    self.processing_stats['pass_positions'] += 1
                                    # Log first few pass positions for verification
                                    if self.processing_stats['pass_positions'] <= 3:
                                        print(f"   Modified position x={display_x}, y={display_y}, pos={category_pos}: {binary_pattern.hex()} ({self.pass_value})")
                                else:
                                    self.processing_stats['fail_positions'] += 1
                                    # Log first few fail positions for verification
                                    if self.processing_stats['fail_positions'] <= 3:
                                        print(f"   Modified position x={display_x}, y={display_y}, pos={category_pos}: {binary_pattern.hex()} ({self.fail_value})")
                    else:
                        # Position is out of bounds due to file truncation
                        if bump_value == "__":
                            # This is an "__" position that should remain unchanged
                            # Since file is truncated, we can't access it, but it would remain unchanged anyway
                            self.processing_stats['unchanged_positions'] += 1
                            print(f"   ⚠️  '__' position out of bounds (file truncated): x={display_x}, y={display_y}, pos={category_pos}")
                            print(f"       Position would remain unchanged anyway - no data loss")
                        else:
                            # This is a real error - a position that needs modification is out of bounds
                            self.processing_stats['errors'] += 1
                            print(f"   ❌ Critical: Position out of bounds: x={display_x}, y={display_y}, pos={category_pos}, value='{bump_value}'")
                            print(f"       This position needs modification but file is truncated!")

            print(f"✅ Advanced conversion completed:")
            print(f"   Total positions: {self.processing_stats['total_positions']}")
            print(f"   Unchanged positions (__): {self.processing_stats['unchanged_positions']}")
            print(f"   Pass positions (00): {self.processing_stats['pass_positions']}")
            print(f"   Fail positions (XX): {self.processing_stats['fail_positions']}")
            print(f"   Binary modifications: {self.processing_stats['binary_modifications']}")
            print(f"   Errors: {self.processing_stats['errors']}")

            return True

        except Exception as e:
            print(f"❌ Error applying advanced conversion: {e}")
            return False

    def save_output_map(self, output_path: str) -> bool:
        """Save the processed map to output file"""
        try:
            print(f"💾 Saving processed map: {output_path}")

            with open(output_path, 'wb') as f:
                f.write(self.tsk_processor.filearray)

            print(f"✅ Saved processed map: {len(self.tsk_processor.filearray)} bytes")
            return True

        except Exception as e:
            print(f"❌ Error saving processed map: {e}")
            return False

    def process_advanced_nepes(self, bump_map_path: str, dummy_map_path: str,
                              output_path: str = None, pass_value: int = 63,
                              fail_value: int = 59) -> bool:
        """Complete advanced NEPES processing pipeline"""
        print("🚀 Starting Advanced NEPES Processing...")
        print("=" * 70)

        try:
            # Set configurable values
            self.set_pass_value(pass_value)
            self.set_fail_value(fail_value)

            # Step 1: Load dummy map
            print("📋 Step 1: Loading dummy map...")
            if not self.load_dummy_map(dummy_map_path):
                return False

            # Step 2: Parse bump map
            print("\n📋 Step 2: Parsing bump map...")
            if not self.parse_bump_map(bump_map_path):
                return False

            # Step 3: Generate output filename if not provided
            if not output_path:
                output_path = self.generate_output_filename(bump_map_path, dummy_map_path)
                print(f"\n📋 Generated output filename: {output_path}")

            # Step 4: Apply advanced conversion
            print("\n📋 Step 4: Applying advanced conversion...")
            if not self.apply_advanced_conversion():
                return False

            # Step 5: Save output
            print("\n📋 Step 5: Saving output map...")
            if not self.save_output_map(output_path):
                return False

            print("\n" + "=" * 70)
            print("🎉 Advanced NEPES Processing completed successfully!")
            self._print_final_summary(bump_map_path, dummy_map_path, output_path)

            return True

        except Exception as e:
            print(f"❌ Error in advanced NEPES processing: {e}")
            return False

    def _print_final_summary(self, bump_path: str, dummy_path: str, output_path: str):
        """Print final processing summary"""
        print(f"📊 Processing Summary:")
        print(f"   Input bump map: {bump_path}")
        print(f"   Input dummy map: {dummy_path}")
        print(f"   Output map: {output_path}")
        print(f"   Bump dimensions: {self.rows} × {self.cols}")
        print(f"   Map dimensions: {self.rowsize} × {self.columnsize}")
        print(f"   Map version: {self.map_version}")
        print(f"   Pass value: {self.pass_value}")
        print(f"   Fail value: {self.fail_value}")
        print(f"   Unchanged positions (__): {self.processing_stats['unchanged_positions']}")
        print(f"   Pass positions (00): {self.processing_stats['pass_positions']}")
        print(f"   Fail positions (XX): {self.processing_stats['fail_positions']}")
        print(f"   Binary modifications: {self.processing_stats['binary_modifications']}")

    def get_processing_stats(self) -> Dict[str, Any]:
        """Get detailed processing statistics"""
        return {
            'test_house': 'NEPES Corporation (Advanced)',
            'bump_dimensions': f"{self.rows} × {self.cols}",
            'map_dimensions': f"{self.rowsize} × {self.columnsize}",
            'map_version': self.map_version,
            'pass_value': self.pass_value,
            'fail_value': self.fail_value,
            'processing_stats': self.processing_stats,
            'header_info': self.header_info
        }


def main():
    """Test function for NEPES advanced processor"""
    bump_file = "test/D97127.09"
    dummy_file = "test/009.NNS157-09-E4"

    processor = NEPESAdvancedProcessor()

    if os.path.exists(bump_file) and os.path.exists(dummy_file):
        # Test with default values
        success = processor.process_advanced_nepes(bump_file, dummy_file, pass_value=63, fail_value=59)

        if success:
            stats = processor.get_processing_stats()
            print(f"\n📊 Final Statistics:")
            for key, value in stats.items():
                print(f"   {key}: {value}")

        return success
    else:
        print("❌ Test files not found")
        return False


if __name__ == "__main__":
    main()
