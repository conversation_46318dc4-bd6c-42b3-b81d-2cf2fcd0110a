任务流程：
1，Full Map Tool 功能增加：
      Bin_Summary sheet功能设计：
          ---A1& B1合并单元格显示Device Name, C1& D1合并单元格(显示内容为选择的setup excel中的D2单元格内容（需要代码设计））
          ---E1单元格显示Lot No,F1&G1合并单元格显示（第一个map中“-”前的内容，这部分在代码中已经有实现，可以复用代码）
          ---H1单元格显示Total pcs，I1单元格需要统计一共多少个map选择了。
          ---J1单元格显示VENDOR，K1&L1合并单元格显示（选择的setup excel中的F2单元格内容（需要代码设计））
          ---A2& B2合并单元格显示Total Tested, C2&D2合并单元格显示（所有加载的map中的生成excel的B8的和，或者是每个map中加载的tested 芯片的总数和，按照你的理解，你来决定哪个代码好开发，最好选择直接相加）
           ---E2单元格显示Pass Dice，F2&G2合并单元格显示（所有加载的map中的生成excel的B9的和）
           ---H2单元格显示Yield%，I2单元格显示良率（百分数保留2位小数，计算方式是F2&G2合并单元格内容/ C2&D2合并单元格的内容）
           ---J2单元格显示Fail Dice，K2&L2合并单元格显示（C2&D2合并单元格内容-F2&G2合并单元格内容）
2, 上述功能开发注意事项：
     --- bin别数量和良率统计不要破坏，代码功能不要破坏   
     --- GUI尽量统一，不要占用太多空间。

如果代码生效后要求：
1，测试脚本和文件请放到test文件夹
2，生成的md说明文档，请放到temp文件夹
3，程序架构统一，框架统一，代码尽量简洁