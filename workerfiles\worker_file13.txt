任务流程：
针对Bump map To Tsk map Tool现在针对工具代码实际验证修改完善:
   -----Map Version= 2或者3，
   按照例子是：Modified position x=79, y=0, pos=15365: 003F0000 (63)
              Modified position x=149, y=2, pos=18085: 003B0000 (59) 
   -----Map Version= 4，
   按照例子是：Modified position x=79, y=0, pos=15365: 0000003F (63)
              Modified position x=149, y=2, pos=18085: 0000003B (59)   			  
	
根据描述分开代码执行map version=2或者3和map version =4的两种改写逻辑，完成全部修改的代码设计。

上述功能开发注意事项：
     --- 其他两个功能不要破坏，代码功能不要破坏
     --- GUI尽量统一，不要占用太多空间
     --- 复用代码部分，尽量做到代码统一

要求：
1，测试脚本和文件请放到test文件夹
2，生成的md说明文档，请放到temp文件夹
3，程序架构统一，框架统一，代码尽量简洁

测试文件说明：
1，可以使用test文件夹下的D97127.09作为bump map的load
2，可以使用test文件夹下的009.NNS157-09-E4作为dummy map的文件