#!/usr/bin/env python3
"""
Full Map GUI - Process multiple MAP files and generate Excel with multiple sheets
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import os
from typing import List, Dict, Any


class FullMapGUI:
    """GUI for Full Map Tool - processes multiple MAP files"""
    
    def __init__(self, root):
        self.root = root
        self.root.title("Full Map Tool - Process Multiple MAP Files")
        self.root.geometry("800x920")
        self.root.resizable(True, True)
        self.root.minsize(750, 880)
        
        # Center the window
        self.center_window()
        
        # Variables
        self.config_file_path = tk.StringVar()
        self.map_files = []  # List of selected MAP files
        self.rotation_angle = tk.IntVar(value=0)
        self.filter_empty = tk.BooleanVar(value=True)
        self.sort_by_quantity = tk.BooleanVar(value=True)  # New: Sort bins by quantity (default: True)
        
        # Initialize processors and config reader
        self.processors = {}  # Dict: filename -> processor
        self.config_reader = None

        # Memory management
        self.processing_count = 0
        self.last_memory_usage = 0.0

        # Create GUI elements
        self.create_widgets()

        # Set up window close protocol
        self.root.protocol("WM_DELETE_WINDOW", self.on_window_close)
    
    def center_window(self):
        """Center the window on screen"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f"{width}x{height}+{x}+{y}")
    
    def create_widgets(self):
        """Create the GUI elements"""
        # Main frame with scrollbar
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Configure grid weights
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        
        # Configuration file selection section
        config_frame = ttk.LabelFrame(main_frame, text="Configuration File", padding="10")
        config_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        config_frame.columnconfigure(1, weight=1)
        
        ttk.Label(config_frame, text="Config Excel File:").grid(row=0, column=0, sticky=tk.W, padx=(0, 5))
        
        config_entry = ttk.Entry(config_frame, textvariable=self.config_file_path, width=60)
        config_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 5))
        
        ttk.Button(config_frame, text="Browse...", command=self.browse_config_file).grid(row=0, column=2)
        
        # MAP files selection section
        files_frame = ttk.LabelFrame(main_frame, text="MAP Files Selection", padding="10")
        files_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        files_frame.columnconfigure(0, weight=1)
        
        # Buttons for file selection
        buttons_frame = ttk.Frame(files_frame)
        buttons_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        
        ttk.Button(buttons_frame, text="Add MAP Files...", 
                  command=self.add_map_files).grid(row=0, column=0, padx=(0, 5))
        ttk.Button(buttons_frame, text="Add Folder...", 
                  command=self.add_folder).grid(row=0, column=1, padx=(0, 5))
        ttk.Button(buttons_frame, text="Clear All", 
                  command=self.clear_files).grid(row=0, column=2, padx=(0, 5))
        
        # File list with scrollbar
        list_frame = ttk.Frame(files_frame)
        list_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        list_frame.columnconfigure(0, weight=1)
        list_frame.rowconfigure(0, weight=1)
        
        # Listbox with scrollbar (reduced height for better space utilization)
        self.files_listbox = tk.Listbox(list_frame, height=6, selectmode=tk.EXTENDED)
        self.files_listbox.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        files_scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.files_listbox.yview)
        files_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        self.files_listbox.configure(yscrollcommand=files_scrollbar.set)
        
        # File management buttons
        file_buttons_frame = ttk.Frame(files_frame)
        file_buttons_frame.grid(row=2, column=0, pady=(5, 0), sticky=(tk.W, tk.E))

        ttk.Button(file_buttons_frame, text="Move Up",
                  command=self.move_file_up).grid(row=0, column=0, padx=(0, 5))
        ttk.Button(file_buttons_frame, text="Move Down",
                  command=self.move_file_down).grid(row=0, column=1, padx=(0, 5))
        ttk.Button(file_buttons_frame, text="Remove Selected",
                  command=self.remove_selected_files).grid(row=0, column=2, padx=(0, 5))

        # Preview filename label
        self.filename_preview = tk.StringVar(value="Output filename will be shown here")
        preview_label = ttk.Label(files_frame, textvariable=self.filename_preview,
                                 font=("Arial", 9, "italic"), foreground="blue")
        preview_label.grid(row=3, column=0, pady=(5, 0), sticky=tk.W)
        
        # Rotation angle selection section
        rotation_frame = ttk.LabelFrame(main_frame, text="Rotation Angle", padding="10")
        rotation_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        ttk.Radiobutton(rotation_frame, text="0° (Original)", variable=self.rotation_angle, 
                       value=0).grid(row=0, column=0, sticky=tk.W, padx=(0, 20))
        ttk.Radiobutton(rotation_frame, text="90° (Clockwise)", variable=self.rotation_angle, 
                       value=90).grid(row=0, column=1, sticky=tk.W, padx=(0, 20))
        ttk.Radiobutton(rotation_frame, text="180°", variable=self.rotation_angle, 
                       value=180).grid(row=1, column=0, sticky=tk.W, padx=(0, 20))
        ttk.Radiobutton(rotation_frame, text="270° (Counter-clockwise)", variable=self.rotation_angle, 
                       value=270).grid(row=1, column=1, sticky=tk.W)
        
        # Output options section
        filter_frame = ttk.LabelFrame(main_frame, text="Output Options", padding="10")
        filter_frame.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        ttk.Checkbutton(filter_frame, text="Filter empty areas (recommended for large files)",
                       variable=self.filter_empty).grid(row=0, column=0, sticky=tk.W)

        # Bin sorting option
        ttk.Checkbutton(filter_frame, text="Sort bins by quantity (descending order)",
                       variable=self.sort_by_quantity).grid(row=1, column=0, sticky=tk.W)
        
        # Processing information section
        info_frame = ttk.LabelFrame(main_frame, text="Processing Information", padding="10")
        info_frame.grid(row=4, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        info_frame.columnconfigure(0, weight=1)
        info_frame.rowconfigure(0, weight=1)
        
        self.info_text = tk.Text(info_frame, height=4, width=80, state=tk.DISABLED)
        self.info_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        info_scrollbar = ttk.Scrollbar(info_frame, orient=tk.VERTICAL, command=self.info_text.yview)
        info_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        self.info_text.configure(yscrollcommand=info_scrollbar.set)
        
        # Buttons section
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=5, column=0, columnspan=2, pady=(10, 0))
        
        ttk.Button(button_frame, text="Process All Files",
                  command=self.process_all_files).grid(row=0, column=0, padx=(0, 10))
        ttk.Button(button_frame, text="Clear Memory",
                  command=self.clear_memory).grid(row=0, column=1, padx=(0, 10))
        ttk.Button(button_frame, text="Exit",
                  command=self.exit_application).grid(row=0, column=2)
        
        # Status bar
        self.status_var = tk.StringVar(value="Ready - Select MAP files to begin")
        status_bar = ttk.Label(main_frame, textvariable=self.status_var, relief=tk.SUNKEN)
        status_bar.grid(row=6, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(10, 0))
    
    def browse_config_file(self):
        """Open file dialog to select configuration Excel file"""
        filename = filedialog.askopenfilename(
            title="Select Configuration Excel File",
            filetypes=[
                ("Excel files", "*.xlsx"),
                ("Excel files", "*.xls"),
                ("All files", "*.*")
            ]
        )
        if filename:
            self.config_file_path.set(filename)
            self.status_var.set(f"Configuration file selected: {os.path.basename(filename)}")
    
    def add_map_files(self):
        """Add MAP files to the processing list"""
        filenames = filedialog.askopenfilenames(
            title="Select MAP Files",
            filetypes=[
                ("All supported files", "*.*"),
                ("MAP files", "*.map"),
                ("TSK files", "*.tsk")
            ]
        )
        
        if filenames:
            added_count = 0
            for filename in filenames:
                if filename not in self.map_files:
                    self.map_files.append(filename)
                    self.files_listbox.insert(tk.END, os.path.basename(filename))
                    added_count += 1
            
            self.status_var.set(f"Added {added_count} MAP files. Total: {len(self.map_files)} files")
            self.update_info_display()
            self.update_filename_preview()
    
    def add_folder(self):
        """Add all MAP files from a selected folder"""
        folder_path = filedialog.askdirectory(title="Select Folder Containing MAP Files")
        
        if folder_path:
            added_count = 0
            map_extensions = ['.map', '.tsk']
            
            for filename in os.listdir(folder_path):
                file_path = os.path.join(folder_path, filename)
                if (os.path.isfile(file_path) and 
                    any(filename.lower().endswith(ext) for ext in map_extensions)):
                    
                    if file_path not in self.map_files:
                        self.map_files.append(file_path)
                        self.files_listbox.insert(tk.END, filename)
                        added_count += 1
            
            if added_count > 0:
                self.status_var.set(f"Added {added_count} MAP files from folder. Total: {len(self.map_files)} files")
                self.update_info_display()
                self.update_filename_preview()
            else:
                messagebox.showinfo("No Files Found", "No MAP or TSK files found in the selected folder.")
    
    def clear_files(self):
        """Clear all selected files and free memory"""
        # Clear processors with memory cleanup
        for processor in self.processors.values():
            if hasattr(processor, 'clear_memory'):
                processor.clear_memory()

        self.map_files.clear()
        self.files_listbox.delete(0, tk.END)
        self.processors.clear()

        self.status_var.set("All files and memory cleared")
        self.update_info_display()
        self.update_filename_preview()
    
    def remove_selected_files(self):
        """Remove selected files from the list"""
        selected_indices = self.files_listbox.curselection()
        
        if not selected_indices:
            messagebox.showwarning("No Selection", "Please select files to remove.")
            return
        
        # Remove in reverse order to maintain indices
        for index in reversed(selected_indices):
            file_path = self.map_files[index]
            del self.map_files[index]
            self.files_listbox.delete(index)
            
            # Remove from processors if exists
            filename = os.path.basename(file_path)
            if filename in self.processors:
                # Clear memory before removing
                if hasattr(self.processors[filename], 'clear_memory'):
                    self.processors[filename].clear_memory()
                del self.processors[filename]
        
        self.status_var.set(f"Removed {len(selected_indices)} files. Total: {len(self.map_files)} files")
        self.update_info_display()
        self.update_filename_preview()

    def move_file_up(self):
        """Move selected file up in the list"""
        selected_indices = self.files_listbox.curselection()

        if not selected_indices:
            messagebox.showwarning("No Selection", "Please select a file to move up.")
            return

        if len(selected_indices) > 1:
            messagebox.showwarning("Multiple Selection", "Please select only one file to move.")
            return

        index = selected_indices[0]

        if index == 0:
            messagebox.showinfo("Cannot Move", "File is already at the top.")
            return

        # Swap files in the list
        self.map_files[index], self.map_files[index - 1] = self.map_files[index - 1], self.map_files[index]

        # Update listbox
        self.refresh_file_listbox()

        # Select the moved item
        self.files_listbox.selection_set(index - 1)

        self.status_var.set(f"Moved file up. First file determines output filename.")
        self.update_info_display()
        self.update_filename_preview()

    def move_file_down(self):
        """Move selected file down in the list"""
        selected_indices = self.files_listbox.curselection()

        if not selected_indices:
            messagebox.showwarning("No Selection", "Please select a file to move down.")
            return

        if len(selected_indices) > 1:
            messagebox.showwarning("Multiple Selection", "Please select only one file to move.")
            return

        index = selected_indices[0]

        if index == len(self.map_files) - 1:
            messagebox.showinfo("Cannot Move", "File is already at the bottom.")
            return

        # Swap files in the list
        self.map_files[index], self.map_files[index + 1] = self.map_files[index + 1], self.map_files[index]

        # Update listbox
        self.refresh_file_listbox()

        # Select the moved item
        self.files_listbox.selection_set(index + 1)

        self.status_var.set(f"Moved file down. First file determines output filename.")
        self.update_info_display()
        self.update_filename_preview()

    def refresh_file_listbox(self):
        """Refresh the file listbox display"""
        self.files_listbox.delete(0, tk.END)
        for file_path in self.map_files:
            self.files_listbox.insert(tk.END, os.path.basename(file_path))

    def update_filename_preview(self):
        """Update the filename preview based on current file order"""
        if not self.map_files:
            self.filename_preview.set("Output filename will be shown here")
            return

        try:
            # Import the processor to use its filename generation logic
            from full_map_processor import FullMapProcessor
            processor = FullMapProcessor()

            # Generate preview filename
            preview_filename = processor._generate_output_filename(self.map_files, len(self.map_files))

            # Remove timestamp for preview (replace with placeholder)
            import re
            preview_filename = re.sub(r'_\d{8}_\d{6}', '_YYYYMMDD_HHMMSS', preview_filename)

            self.filename_preview.set(f"Output: {preview_filename}")

        except Exception as e:
            self.filename_preview.set(f"Preview error: {str(e)}")

    def update_info_display(self):
        """Update the information display"""
        self.info_text.config(state=tk.NORMAL)
        self.info_text.delete(1.0, tk.END)
        
        if not self.map_files:
            self.info_text.insert(tk.END, "No MAP files selected.\n\nPlease add MAP files using the buttons above.")
        else:
            info_text = f"Selected MAP Files: {len(self.map_files)}\n"
            info_text += f"(Use 'Move Up/Down' to change order - first file determines output filename)\n\n"

            for i, file_path in enumerate(self.map_files, 1):
                filename = os.path.basename(file_path)
                file_size = os.path.getsize(file_path) if os.path.exists(file_path) else 0

                # Highlight first file
                if i == 1:
                    info_text += f"{i:2d}. {filename} ({file_size:,} bytes) ← First file (determines filename)\n"
                else:
                    info_text += f"{i:2d}. {filename} ({file_size:,} bytes)\n"

            info_text += f"\nOutput: One Excel file with {len(self.map_files)} sheets\n"
            info_text += f"Each sheet will be named after its corresponding MAP file.\n"
            info_text += f"Excel filename will be based on the first file in the list."
            
            self.info_text.insert(tk.END, info_text)
        
        self.info_text.config(state=tk.DISABLED)

    def clear_memory(self):
        """Clear all processors and free memory"""
        try:
            memory_freed = 0.0
            processor_count = len(self.processors)

            # Clear all processors
            for filename, processor in self.processors.items():
                if hasattr(processor, 'get_memory_usage_mb'):
                    memory_freed += processor.get_memory_usage_mb()
                if hasattr(processor, 'clear_memory'):
                    processor.clear_memory()

            # Clear processors dictionary
            self.processors.clear()

            # Clear config reader
            self.config_reader = None

            # Update memory tracking
            self.last_memory_usage = 0.0

            # Always provide feedback to user (consistent with AB Map Tool)
            if memory_freed > 0:
                print(f"Full Map Tool - Total memory cleared: ~{memory_freed:.1f} MB from {processor_count} processors")
                self.status_var.set(f"Memory cleared: {memory_freed:.1f} MB freed from {processor_count} processors")
            else:
                print("Full Map Tool - Memory cleared (no active processors)")
                self.status_var.set("Memory cleared - no active processors")

        except Exception as e:
            print(f"Warning: Error during Full Map memory cleanup: {e}")
            self.status_var.set("Memory cleanup completed with warnings")

    def get_total_memory_usage(self) -> float:
        """Get total memory usage from all processors"""
        try:
            total_memory = 0.0
            for processor in self.processors.values():
                if hasattr(processor, 'get_memory_usage_mb'):
                    total_memory += processor.get_memory_usage_mb()
            return total_memory
        except Exception:
            return 0.0

    def auto_memory_check(self):
        """Automatically check and manage memory usage"""
        try:
            current_memory = self.get_total_memory_usage()
            processor_count = len(self.processors)

            # Auto-clear if memory usage is high (>800MB) and processing count > 2
            if current_memory > 800 and self.processing_count > 2:
                print(f"Full Map Tool - Auto-clearing memory: {current_memory:.1f} MB in use")

                # Clear memory
                self.clear_memory()

                # Show auto-cleanup popup
                self.show_auto_cleanup_popup(current_memory, processor_count)

                return True

            return False

        except Exception as e:
            print(f"Warning: Error in Full Map auto memory check: {e}")
            return False

    def process_all_files(self):
        """Process all selected MAP files"""
        if not self.map_files:
            messagebox.showerror("No Files", "Please select MAP files to process.")
            return
        
        # Load configuration file if provided
        if self.config_file_path.get():
            from config_reader import ConfigReader
            self.config_reader = ConfigReader()
            if not self.config_reader.read_config_file(self.config_file_path.get()):
                messagebox.showwarning("Warning", "Could not load configuration file. Proceeding without bin name mapping.")
                self.config_reader = None
        
        # Auto memory check before processing
        self.auto_memory_check()

        # Process files
        self.status_var.set("Processing files...")
        self.root.update()

        try:
            from full_map_processor import FullMapProcessor
            processor = FullMapProcessor()
            
            # Set processing options
            processor.set_rotation_angle(self.rotation_angle.get())
            processor.set_filter_empty(self.filter_empty.get())
            processor.set_sort_by_quantity(self.sort_by_quantity.get())
            processor.set_config_reader(self.config_reader)
            
            # Process all files
            output_filename = processor.process_multiple_files(self.map_files)
            
            if output_filename:
                messagebox.showinfo("Success", 
                                  f"Excel file created successfully:\n{output_filename}\n\n"
                                  f"Generated {len(self.map_files)} sheets in one Excel file.")
                self.status_var.set(f"Processing completed: {os.path.basename(output_filename)}")
            else:
                messagebox.showerror("Error", "Failed to process MAP files.")
                self.status_var.set("Processing failed")
                
        except Exception as e:
            messagebox.showerror("Error", f"An error occurred during processing:\n{str(e)}")
            self.status_var.set("Processing failed")
        finally:
            # Update processing count and check memory
            self.processing_count += 1
            current_memory = self.get_total_memory_usage()
            if current_memory > 0:
                print(f"Full Map processing #{self.processing_count} complete. Total memory usage: {current_memory:.1f} MB")

    def show_auto_cleanup_popup(self, memory_freed: float, processor_count: int):
        """Show popup for automatic memory cleanup"""
        try:
            # Create popup window
            popup = tk.Toplevel(self.root)
            popup.title("Auto Memory Cleanup")
            popup.geometry("380x140")
            popup.resizable(False, False)

            # Center popup on parent window
            popup.transient(self.root)

            # Center popup
            x = self.root.winfo_x() + (self.root.winfo_width() // 2) - 190
            y = self.root.winfo_y() + (self.root.winfo_height() // 2) - 70
            popup.geometry(f"380x140+{x}+{y}")

            # Create content
            main_frame = ttk.Frame(popup, padding="20")
            main_frame.pack(fill=tk.BOTH, expand=True)

            # Icon and message
            message = f"🔄 Auto Memory Cleanup Triggered\n\n{memory_freed:.1f} MB freed from {processor_count} processors\n\nMemory usage was high, automatically cleared."

            ttk.Label(main_frame, text=message, justify=tk.CENTER,
                     font=("Arial", 9)).pack(expand=True)

            # Auto-close after 2 seconds
            popup.after(2000, popup.destroy)

            # Allow manual close with Escape key
            popup.bind('<Escape>', lambda e: popup.destroy())
            popup.focus_set()

        except Exception as e:
            print(f"Warning: Error showing Full Map auto cleanup popup: {e}")

    def show_memory_cleanup_popup(self, memory_freed: float, processor_count: int):
        """Show a temporary popup with memory cleanup information"""
        try:
            # Create popup window
            popup = tk.Toplevel(self.root)
            popup.title("Memory Cleanup")
            popup.geometry("350x120")
            popup.resizable(False, False)

            # Center popup on parent window
            popup.transient(self.root)
            popup.grab_set()

            # Center popup
            x = self.root.winfo_x() + (self.root.winfo_width() // 2) - 175
            y = self.root.winfo_y() + (self.root.winfo_height() // 2) - 60
            popup.geometry(f"350x120+{x}+{y}")

            # Create content
            main_frame = ttk.Frame(popup, padding="20")
            main_frame.pack(fill=tk.BOTH, expand=True)

            # Icon and message
            if memory_freed > 0:
                message = f"✅ Memory Cleanup Complete\n\n{memory_freed:.1f} MB freed from {processor_count} processors"
            elif processor_count > 0:
                message = f"✅ Memory Cleanup Complete\n\nProcessors cleared (no memory to free)"
            else:
                message = "✅ Application Closing\n\nThank you for using Full Map Tool!"

            ttk.Label(main_frame, text=message, justify=tk.CENTER,
                     font=("Arial", 10)).pack(expand=True)

            # Auto-close after 2 seconds
            popup.after(2000, popup.destroy)

            # Allow manual close with Escape key
            popup.bind('<Escape>', lambda e: popup.destroy())
            popup.focus_set()

        except Exception as e:
            print(f"Warning: Error showing Full Map cleanup popup: {e}")

    def on_window_close(self):
        """Handle window close event (X button)"""
        self.exit_application()

    def exit_application(self):
        """Exit the application with memory cleanup and user feedback"""
        try:
            # Get memory info before clearing
            memory_freed = 0.0
            processor_count = len(self.processors)

            for processor in self.processors.values():
                if hasattr(processor, 'get_memory_usage_mb'):
                    memory_freed += processor.get_memory_usage_mb()

            # Clear all memory before exit
            self.clear_memory()
            print("Full Map Tool - Application memory cleared before exit")

            # Always show cleanup popup when exiting (user feedback)
            self.show_memory_cleanup_popup(memory_freed, processor_count)
            # Wait a moment for popup to show before quitting
            self.root.after(2100, self.root.quit)

        except Exception as e:
            print(f"Warning: Error during Full Map exit cleanup: {e}")
            self.root.quit()


def main():
    """Main function for testing"""
    root = tk.Tk()
    FullMapGUI(root)
    root.mainloop()


if __name__ == "__main__":
    main()
