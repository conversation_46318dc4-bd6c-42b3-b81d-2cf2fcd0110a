#!/usr/bin/env python3
"""
对比TMB处理器修复前后的差异
展示bin0处理逻辑的改进
"""

from tmb_processor import TMBProcessor
from tsk_map_processor import TSKMapProcessor


def create_mock_processor_with_bin0():
    """创建包含bin0数据的模拟处理器"""
    processor = TSKMapProcessor()
    processor.rowsize = 2
    processor.columnsize = 4
    
    # 创建包含bin0的测试数据
    processor.map_data = [
        # Row 0: bin0, bin1, 未测试, bin2
        [[0, 4], [1, 4], [0, 0], [2, 3]],
        # Row 1: bin0, 未测试, bin0, bin3  
        [[0, 3], [0, 0], [0, 4], [3, 4]]
    ]
    
    return processor


def simulate_old_logic(processor):
    """模拟修复前的逻辑"""
    print("📊 修复前的逻辑 (错误的处理方式):")
    
    # 旧逻辑：category 0 被忽略
    category_counts_old = {}
    for row in processor.map_data:
        for col_data in row:
            category = col_data[0]
            # 旧逻辑：忽略 category 0
            if category != 0:
                category_counts_old[category] = category_counts_old.get(category, 0) + 1
    
    print("   统计结果 (忽略bin0):")
    for cat in sorted(category_counts_old.keys()):
        print(f"     Bin {cat}: {category_counts_old[cat]} dies")
    
    # 旧逻辑：Cat 00 强制为0
    print("   Cat 00: 0  ← 强制设为0 (错误!)")
    for cat in range(1, 256):
        count = category_counts_old.get(cat, 0)
        if count > 0:
            print(f"   Cat {cat:02d}: {count}")
    
    return category_counts_old


def simulate_new_logic(processor):
    """模拟修复后的逻辑"""
    print("\n📊 修复后的逻辑 (正确的处理方式):")
    
    # 新逻辑：基于color判断是否为有效测试数据
    category_counts_new = {}
    for row in processor.map_data:
        for col_data in row:
            category = col_data[0]
            color = col_data[1]
            # 新逻辑：只要color!=0就是有效测试数据，包括bin0
            if color != 0:
                category_counts_new[category] = category_counts_new.get(category, 0) + 1
    
    print("   统计结果 (包含bin0):")
    for cat in sorted(category_counts_new.keys()):
        print(f"     Bin {cat}: {category_counts_new[cat]} dies")
    
    # 新逻辑：所有bin都正确统计
    for cat in range(256):
        count = category_counts_new.get(cat, 0)
        if count > 0 or cat == 0:  # 显示bin0即使为0
            print(f"   Cat {cat:02d}: {count}")
            if cat > 5 and count == 0:  # 避免显示太多0
                break
    
    return category_counts_new


def compare_map_visualization(processor):
    """对比map可视化的差异"""
    print("\n📄 Map可视化对比:")
    
    # 旧逻辑的可视化
    print("   修复前 (category 0 显示为空格):")
    for row in range(processor.rowsize):
        row_data = []
        for col in range(processor.columnsize):
            category = processor.map_data[row][col][0]
            if category == 0:  # 旧逻辑：category 0 显示为空格
                row_data.append("  ")
            else:
                row_data.append(f"{category:2d}")
        print(f"     {row:02d}| {' '.join(row_data)}")
    
    # 新逻辑的可视化
    print("   修复后 (基于color判断，bin0正确显示):")
    for row in range(processor.rowsize):
        row_data = []
        for col in range(processor.columnsize):
            category = processor.map_data[row][col][0]
            color = processor.map_data[row][col][1]
            if color == 0:  # 新逻辑：color 0 才是空白
                row_data.append("  ")
            else:  # 有测试数据，包括bin0
                row_data.append(f"{category:2d}")
        print(f"     {row:02d}| {' '.join(row_data)}")


def main():
    """主函数"""
    print("TMB处理器修复前后对比测试")
    print("展示bin0处理逻辑的改进")
    print("=" * 60)
    
    # 创建测试数据
    processor = create_mock_processor_with_bin0()
    
    print("🧪 测试数据:")
    print("   尺寸: 2 × 4")
    print("   包含bin0测试数据的位置:")
    
    bin0_positions = []
    for row in range(processor.rowsize):
        for col in range(processor.columnsize):
            category = processor.map_data[row][col][0]
            color = processor.map_data[row][col][1]
            if category == 0 and color != 0:
                status = "Pass" if color == 4 else "Fail"
                bin0_positions.append(f"({row},{col})-{status}")
    
    print(f"     bin0位置: {', '.join(bin0_positions)}")
    
    # 对比统计逻辑
    old_counts = simulate_old_logic(processor)
    new_counts = simulate_new_logic(processor)
    
    # 对比可视化
    compare_map_visualization(processor)
    
    # 总结差异
    print(f"\n🎯 关键差异总结:")
    
    old_bin0 = 0  # 旧逻辑强制为0
    new_bin0 = new_counts.get(0, 0)
    
    print(f"   bin0统计:")
    print(f"     修复前: {old_bin0} (强制为0)")
    print(f"     修复后: {new_bin0} (正确统计)")
    print(f"     差异: {'+' if new_bin0 > old_bin0 else ''}{new_bin0 - old_bin0}")
    
    print(f"\n   判断逻辑:")
    print(f"     修复前: 忽略所有 category == 0 的数据")
    print(f"     修复后: 基于 color != 0 判断是否为有效测试数据")
    
    print(f"\n   影响:")
    print(f"     修复前: bin0测试数据丢失，统计不准确")
    print(f"     修复后: 与Full Map Tool逻辑一致，统计准确")
    
    # 验证修复效果
    tmb_processor = TMBProcessor()
    tmb_processor.tsk_processor = processor
    
    lower_part = tmb_processor.generate_lower_part()
    actual_bin0 = 0
    for line in lower_part.split('\n'):
        if line.startswith('Cat 00: '):
            actual_bin0 = int(line.split(': ')[1])
            break
    
    print(f"\n🎉 实际TMB输出验证:")
    print(f"   TMB中的bin0: {actual_bin0}")
    print(f"   与预期匹配: {'✅' if actual_bin0 == new_bin0 else '❌'}")
    
    if actual_bin0 == new_bin0 and new_bin0 > 0:
        print(f"   ✅ 修复成功！bin0数据得到正确处理")
    elif actual_bin0 == new_bin0 and new_bin0 == 0:
        print(f"   ✅ 逻辑正确，此测试数据确实无bin0")
    else:
        print(f"   ❌ 仍有问题需要解决")


if __name__ == "__main__":
    main()
