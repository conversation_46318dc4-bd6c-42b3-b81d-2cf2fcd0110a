任务流程：
针对Bump map To Tsk map Tool工具进行完善:
    1，Test House选择部分，增加一个Clear清除选择，当前如果选错测试厂，无法清除重新选择。
    2，bump map选择增加可以选择多个bump map（应用场景：同一个dummy map，可以对应多个bump map，选择几个bump map都可以对应一个dummy map，最后会依次生成对应bump map个数的修改后的dummy map）
    3，输出output路径选择部分：格式修改为bump map名字（全称+格式）+时间戳（尽量缩短显示）+dummy map（全称+格式）（不是创建tsk或者map格式，保持生成的文件是dummy map文件格式）。删除手动命名格式（默认生成，不需要手动再改名字）（输出只能选择路径，命名自动化。因为上一条开发选择多个bump map功能，生成命名会跟随bump map自动生成多个命名文件）
    4, UI 请按照你的理解设计合理化。美观易用（尽量不要破坏已经设计的UI理念）		  

上述功能开发注意事项：
     --- 其他两个功能不要破坏，代码功能不要破坏
     --- GUI尽量统一，不要占用太多空间
     --- 复用代码部分，尽量做到代码统一

要求：
1，测试脚本和文件请放到test文件夹
2，生成的md说明文档，请放到temp文件夹
3，程序架构统一，框架统一，代码尽量简洁

测试文件说明：
1，可以使用test文件夹下的D97127.09+D99970.01作为bump map的load
2，可以使用test文件夹下的009.NNS157-09-E4作为dummy map的文件