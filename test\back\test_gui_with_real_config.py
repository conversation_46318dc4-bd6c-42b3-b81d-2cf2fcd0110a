#!/usr/bin/env python3
"""
Test GUI with Real Configuration File
Test the complete workflow with 3509_CP1_program_bin.xlsx
"""

import sys
import os
sys.path.append('..')
from tsk_map_processor import TSKMapProcessor
from config_reader import ConfigReader
from excel_output import create_excel_output


def test_config_reader_with_real_file():
    """Test ConfigReader with the real configuration file"""
    print("Testing ConfigReader with 3509_CP1_program_bin.xlsx")
    print("=" * 60)
    
    config_path = "../3509_CP1_program_bin.xlsx"
    
    if not os.path.exists(config_path):
        print(f"❌ Config file not found: {config_path}")
        return False
    
    try:
        reader = ConfigReader()
        
        if reader.read_config_file(config_path):
            print(f"✅ Config file loaded successfully")
            
            # Test program name
            test_program = reader.get_test_program_name()
            print(f"  Test Program: '{test_program}'")
            
            # Bin mappings
            mappings = reader.get_all_bin_mappings()
            print(f"  Bin mappings: {len(mappings)} entries")
            
            for bin_num, bin_name in sorted(mappings.items()):
                formatted = reader.get_formatted_bin_name(bin_num)
                print(f"    Bin {bin_num}: '{bin_name}' -> '{formatted}'")
            
            return True
        else:
            print(f"❌ Failed to load config file")
            return False
            
    except Exception as e:
        print(f"❌ Error testing config reader: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_complete_workflow(tsk_filepath):
    """Test complete workflow with real config file"""
    print(f"\nTesting Complete Workflow")
    print("=" * 50)
    
    config_path = "../3509_CP1_program_bin.xlsx"
    
    if not os.path.exists(tsk_filepath):
        print(f"❌ TSK file not found: {tsk_filepath}")
        return False
    
    if not os.path.exists(config_path):
        print(f"❌ Config file not found: {config_path}")
        return False
    
    try:
        # Load TSK file
        processor = TSKMapProcessor()
        
        print(f"Loading TSK file: {os.path.basename(tsk_filepath)}")
        if not processor.read_file(tsk_filepath):
            print("❌ Failed to read TSK file")
            return False
        
        if not processor.parse_file_header():
            print("❌ Failed to parse TSK header")
            return False
        
        if not processor.process_die_data():
            print("❌ Failed to process die data")
            return False
        
        print(f"✅ TSK file processed successfully")
        
        # Load config file
        config_reader = ConfigReader()
        print(f"Loading config file: {os.path.basename(config_path)}")
        if not config_reader.read_config_file(config_path):
            print("❌ Failed to load config file")
            return False
        
        print(f"✅ Config file loaded successfully")
        
        # Get statistics
        stats = processor.get_test_statistics()
        bin_stats = processor.get_bin_statistics()
        
        print(f"\nTSK File Statistics:")
        print(f"  Device Name: {processor.devicename}")
        print(f"  Lot ID: {processor.lotid}")
        print(f"  Wafer ID: {processor.waferslotid}")
        print(f"  Total Tested: {stats['total_tested']}")
        print(f"  Pass Count: {stats['pass_count']}")
        print(f"  Yield: {stats['yield_percentage']:.2f}%")
        
        print(f"\nConfig File Information:")
        print(f"  Test Program: {config_reader.get_test_program_name()}")
        print(f"  Bin Mappings: {len(config_reader.get_all_bin_mappings())} entries")
        
        print(f"\nTop 10 Bins with Config Names:")
        for i, bin_data in enumerate(bin_stats[:10]):
            bin_number = int(bin_data['bin_name'].replace('bin', ''))
            formatted_name = config_reader.get_formatted_bin_name(bin_number)
            print(f"  {i+1:2d}. {formatted_name:<25} {bin_data['quantity']:>6} dies ({bin_data['yield_percentage']:>6.2f}%)")
        
        # Create Excel output
        output_filename = "test_real_config_output.xlsx"
        
        if create_excel_output(processor, "TestSheet", 0, output_filename, tsk_filepath, config_reader):
            print(f"\n✅ Excel output created: {output_filename}")
            
            if os.path.exists(output_filename):
                file_size = os.path.getsize(output_filename)
                print(f"  File size: {file_size:,} bytes")
                
                print(f"\nExpected Excel Content:")
                print(f"  B3: Test Program = '{config_reader.get_test_program_name()}'")
                print(f"  Bin Statistics with Real Names:")
                
                for i, bin_data in enumerate(bin_stats[:5], 14):
                    bin_number = int(bin_data['bin_name'].replace('bin', ''))
                    formatted_name = config_reader.get_formatted_bin_name(bin_number)
                    print(f"    Row {i}: '{formatted_name}' ({bin_data['quantity']} dies)")
                
                return True
            else:
                print("❌ Output file not found")
                return False
        else:
            print("❌ Failed to create Excel output")
            return False
            
    except Exception as e:
        print(f"❌ Error in complete workflow test: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Main test function"""
    if len(sys.argv) < 2:
        print("Real Configuration Test")
        print("Usage: python test_gui_with_real_config.py <tsk_file_path>")
        print("Example: python test_gui_with_real_config.py ../3AA111-01-B4")
        return
    
    tsk_filepath = sys.argv[1]
    
    print("TSK/MAP Real Configuration Test")
    print("Testing with 3509_CP1_program_bin.xlsx configuration file")
    print("=" * 70)
    
    # Test 1: Config reader with real file
    if not test_config_reader_with_real_file():
        print("❌ Config reader test failed")
        return
    
    # Test 2: Complete workflow
    if not test_complete_workflow(tsk_filepath):
        print("❌ Complete workflow test failed")
        return
    
    print("\n" + "=" * 70)
    print("🎉 Real Configuration Test Completed!")
    print("\nVerified Features:")
    print("✅ Real config file (3509_CP1_program_bin.xlsx) loading")
    print("✅ Test program name extraction from A2")
    print("✅ Bin name mappings from A6+ and B6+")
    print("✅ Excel output with enhanced bin names")
    print("✅ Processor initialization fix")
    
    print(f"\nReal Configuration Data:")
    print(f"• Test Program: ICNA3509WAA_CP1_1D_ND4_E00.tdl")
    print(f"• Bin Mappings: 7 entries")
    print(f"  - Bin 127: PASS")
    print(f"  - Bin 64: TEST OPEN SHORT TEST")
    print(f"  - Bin 89: DMEM")
    print(f"  - Bin 91: CMEM_BIST TEST")
    print(f"  - And more...")


if __name__ == "__main__":
    main()
