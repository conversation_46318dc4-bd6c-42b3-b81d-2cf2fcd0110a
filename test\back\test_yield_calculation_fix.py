#!/usr/bin/env python3
"""
Test script to verify yield calculation fix in Bin_Summary sheet
"""

import os
import sys

# Add parent directory to path to import modules
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from full_map_processor import FullMapProcessor
from tsk_map_processor import TSKMapProcessor


def test_yield_calculation_direct():
    """Test yield calculation directly from TSKMapProcessor"""
    print("🧪 Testing Direct Yield Calculation...")
    
    test_file = "test/3AC468-01-C5.map"
    if not os.path.exists(test_file):
        print(f"❌ Test file not found: {test_file}")
        return False
    
    try:
        # Create and process with TSKMapProcessor directly
        processor = TSKMapProcessor()
        
        if not processor.read_file(test_file):
            print("❌ Failed to read test file")
            return False
        
        if not processor.parse_file_header():
            print("❌ Failed to parse file header")
            return False
        
        if not processor.process_die_data():
            print("❌ Failed to process die data")
            return False
        
        # Get test statistics using existing method
        test_stats = processor.get_test_statistics()
        
        total_tested = test_stats.get('total_tested', 0)
        pass_count = test_stats.get('pass_count', 0)
        yield_percentage = test_stats.get('yield_percentage', 0.0)
        
        print(f"✅ Test Statistics:")
        print(f"   Total tested: {total_tested}")
        print(f"   Pass count: {pass_count}")
        print(f"   Yield: {yield_percentage:.2f}%")
        
        # Verify calculation
        calculated_yield = (pass_count / total_tested * 100) if total_tested > 0 else 0
        print(f"   Calculated yield: {calculated_yield:.2f}%")
        
        if abs(yield_percentage - calculated_yield) < 0.01:
            print("✅ Yield calculation is correct")
            return True
        else:
            print(f"❌ Yield calculation mismatch: {yield_percentage} vs {calculated_yield}")
            return False
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False


def test_bin_summary_yield():
    """Test yield calculation in Bin_Summary sheet"""
    print("\n🧪 Testing Bin_Summary Yield Calculation...")
    
    test_file = "test/3AC468-01-C5.map"
    if not os.path.exists(test_file):
        print(f"❌ Test file not found: {test_file}")
        return False
    
    try:
        # Get direct yield calculation first
        direct_processor = TSKMapProcessor()
        direct_processor.read_file(test_file)
        direct_processor.parse_file_header()
        direct_processor.process_die_data()
        
        direct_stats = direct_processor.get_test_statistics()
        direct_yield = direct_stats.get('yield_percentage', 0.0)
        
        print(f"📊 Direct yield calculation: {direct_yield:.2f}%")
        
        # Create Full Map Processor and generate Bin_Summary
        processor = FullMapProcessor()
        processor.set_rotation_angle(0)
        processor.set_filter_empty(True)

        # Set output to test directory
        test_dir = os.path.dirname(os.path.abspath(__file__))
        processor.set_output_folder(test_dir)
        
        result = processor.process_multiple_files([test_file])
        
        if result and os.path.exists(result):
            print(f"✅ Excel file created: {result}")
            
            # Load and examine the Excel file
            from openpyxl import load_workbook
            wb = load_workbook(result)
            
            if "Bin_Summary" in wb.sheetnames:
                ws = wb["Bin_Summary"]
                
                # Get yield from Bin_Summary (row 6, column B)
                yield_cell = ws.cell(row=6, column=2)
                yield_value = yield_cell.value
                
                # Convert from decimal to percentage if needed
                if yield_value is not None:
                    if yield_value <= 1:  # Decimal format
                        bin_summary_yield = yield_value * 100
                    else:  # Already percentage
                        bin_summary_yield = yield_value
                    
                    print(f"📊 Bin_Summary yield: {bin_summary_yield:.2f}%")
                    print(f"   Cell format: {yield_cell.number_format}")
                    print(f"   Cell value: {yield_value}")
                    
                    # Check Average row yield
                    avg_yield_cell = None
                    for row in range(7, 15):
                        if ws.cell(row=row, column=1).value == "Average":
                            avg_yield_cell = ws.cell(row=row, column=2)
                            break
                    
                    if avg_yield_cell:
                        avg_yield_value = avg_yield_cell.value
                        if avg_yield_value is not None:
                            if avg_yield_value <= 1:
                                avg_yield_percent = avg_yield_value * 100
                            else:
                                avg_yield_percent = avg_yield_value
                            print(f"📊 Average yield: {avg_yield_percent:.2f}%")
                    
                    # Compare with direct calculation
                    if abs(bin_summary_yield - direct_yield) < 0.01:
                        print("✅ Bin_Summary yield matches direct calculation")
                        wb.close()
                        return True
                    else:
                        print(f"❌ Yield mismatch: Bin_Summary={bin_summary_yield:.2f}%, Direct={direct_yield:.2f}%")
                        wb.close()
                        return False
                else:
                    print("❌ No yield value found in Bin_Summary")
                    wb.close()
                    return False
            else:
                print("❌ Bin_Summary sheet not found")
                wb.close()
                return False
        else:
            print("❌ Failed to create Excel file")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False


def test_yield_with_bin_correlation():
    """Test yield calculation correlation with bin statistics"""
    print("\n🧪 Testing Yield vs Bin Statistics Correlation...")
    
    test_file = "test/3AC468-01-C5.map"
    if not os.path.exists(test_file):
        print(f"❌ Test file not found: {test_file}")
        return False
    
    try:
        # Get direct statistics
        processor = TSKMapProcessor()
        processor.read_file(test_file)
        processor.parse_file_header()
        processor.process_die_data()
        
        # Get test statistics
        test_stats = processor.get_test_statistics()
        total_tested = test_stats.get('total_tested', 0)
        pass_count = test_stats.get('pass_count', 0)
        yield_percentage = test_stats.get('yield_percentage', 0.0)
        
        # Get bin statistics
        bin_stats = processor.get_bin_statistics()
        
        print(f"📊 Test Statistics:")
        print(f"   Total tested: {total_tested}")
        print(f"   Pass count: {pass_count}")
        print(f"   Yield: {yield_percentage:.2f}%")
        
        print(f"\n📊 Bin Statistics:")
        total_from_bins = sum(stat['quantity'] for stat in bin_stats)
        print(f"   Total from bins: {total_from_bins}")
        
        # Find bin1 (usually the pass bin)
        bin1_count = 0
        for stat in bin_stats:
            if stat['bin_name'] == 'bin1':
                bin1_count = stat['quantity']
                bin1_yield = stat['yield_percentage']
                print(f"   Bin1 count: {bin1_count}")
                print(f"   Bin1 yield: {bin1_yield:.2f}%")
                break
        
        # Verify correlations
        correlations_ok = True
        
        # Check if total_tested matches total from bins
        if total_tested == total_from_bins:
            print("✅ Total tested matches bin statistics total")
        else:
            print(f"❌ Total mismatch: tested={total_tested}, bins={total_from_bins}")
            correlations_ok = False
        
        # Check if pass_count matches bin1 (assuming bin1 is pass)
        if pass_count == bin1_count:
            print("✅ Pass count matches bin1 count")
        else:
            print(f"❌ Pass count mismatch: pass={pass_count}, bin1={bin1_count}")
            correlations_ok = False
        
        # Check yield calculation
        calculated_yield = (pass_count / total_tested * 100) if total_tested > 0 else 0
        if abs(yield_percentage - calculated_yield) < 0.01:
            print("✅ Yield calculation is consistent")
        else:
            print(f"❌ Yield inconsistent: reported={yield_percentage}, calculated={calculated_yield}")
            correlations_ok = False
        
        return correlations_ok
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False


def main():
    """Run all yield calculation tests"""
    print("🚀 Yield Calculation Fix Verification")
    print("=" * 50)
    
    # Run tests
    test1 = test_yield_calculation_direct()
    test2 = test_bin_summary_yield()
    test3 = test_yield_with_bin_correlation()
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 Test Results:")
    print(f"   Direct Yield Calculation: {'✅ PASS' if test1 else '❌ FAIL'}")
    print(f"   Bin_Summary Yield: {'✅ PASS' if test2 else '❌ FAIL'}")
    print(f"   Yield vs Bin Correlation: {'✅ PASS' if test3 else '❌ FAIL'}")
    
    all_passed = test1 and test2 and test3
    print(f"\n🎯 Overall: {'✅ ALL TESTS PASSED' if all_passed else '❌ SOME TESTS FAILED'}")
    
    if all_passed:
        print("\n🎉 Yield calculation fix verified successfully!")
        print("   Bin_Summary sheet now correctly shows yield percentages")
        print("   Yield calculation uses existing pass/test logic")
    
    return all_passed


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
