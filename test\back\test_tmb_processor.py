#!/usr/bin/env python3
"""
Test TMB Processor - Verify TMB file generation functionality
Based on worker_file17.txt requirements

Test with: test/019.3AD416-19-F4

Author: Yuribytes
Company: Chipone TE development Team
"""

import sys
import os
sys.path.append('..')

from tmb_processor import TMBProcessor
from config_reader import Config<PERSON><PERSON><PERSON>


def test_tmb_processor():
    """Test TMB processor with sample MAP file"""
    print("Testing TMB Processor")
    print("=" * 50)
    
    # Test file path
    test_map_file = "019.3AD416-19-F4"
    
    if not os.path.exists(test_map_file):
        print(f"❌ Test MAP file not found: {test_map_file}")
        print("Please ensure the test file is in the test directory")
        return False
    
    try:
        # Initialize TMB processor
        tmb_processor = TMBProcessor()
        
        # Set output folder to current test directory
        tmb_processor.set_output_folder(".")
        
        print(f"📋 Processing MAP file: {test_map_file}")
        
        # Process MAP to TMB
        tmb_output = tmb_processor.process_map_to_tmb(test_map_file)
        
        if tmb_output:
            print(f"✅ TMB file generated successfully: {os.path.basename(tmb_output)}")
            
            # Verify TMB file content
            if os.path.exists(tmb_output):
                with open(tmb_output, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                print(f"\n📊 TMB File Analysis:")
                print(f"   File size: {len(content)} characters")
                print(f"   Lines: {content.count(chr(10)) + 1}")
                
                # Check for required sections
                has_upper = "00 01" in content  # Map data section
                has_middle = "Wafer Information" in content  # Middle section
                has_lower = "Cat 00:" in content  # Category statistics
                
                print(f"   Upper section (Map data): {'✅' if has_upper else '❌'}")
                print(f"   Middle section (Wafer info): {'✅' if has_middle else '❌'}")
                print(f"   Lower section (Categories): {'✅' if has_lower else '❌'}")
                
                # Show first few lines
                lines = content.split('\n')
                print(f"\n📝 First 10 lines of TMB file:")
                for i, line in enumerate(lines[:10], 1):
                    print(f"   {i:2d}: {line}")
                
                if len(lines) > 10:
                    print(f"   ... and {len(lines) - 10} more lines")
                
                return True
            else:
                print(f"❌ TMB file was not created: {tmb_output}")
                return False
        else:
            print("❌ TMB processing failed")
            return False
            
    except Exception as e:
        print(f"❌ Error during TMB processing: {e}")
        return False


def test_tmb_with_config():
    """Test TMB processor with configuration file"""
    print("\n" + "=" * 50)
    print("Testing TMB Processor with Configuration")
    print("=" * 50)
    
    # Look for config file in parent directory
    config_files = [
        "../3509_CP1_program_bin.xlsx",
        "3509_CP1_program_bin.xlsx"
    ]
    
    config_file = None
    for cf in config_files:
        if os.path.exists(cf):
            config_file = cf
            break
    
    if not config_file:
        print("⚠️  No configuration file found, testing without config")
        return test_tmb_processor()
    
    try:
        # Initialize config reader
        config_reader = ConfigReader()
        if not config_reader.read_config_file(config_file):
            print(f"❌ Failed to read config file: {config_file}")
            return test_tmb_processor()
        
        print(f"📋 Using config file: {os.path.basename(config_file)}")
        print(f"   Device Name: {config_reader.device_name}")
        print(f"   Test Program: {config_reader.test_program_name}")
        
        # Initialize TMB processor with config
        tmb_processor = TMBProcessor()
        tmb_processor.set_config_reader(config_reader)
        tmb_processor.set_output_folder(".")
        
        # Test file
        test_map_file = "019.3AD416-19-F4"
        if not os.path.exists(test_map_file):
            print(f"❌ Test MAP file not found: {test_map_file}")
            return False
        
        # Process with config
        tmb_output = tmb_processor.process_map_to_tmb(test_map_file)
        
        if tmb_output:
            print(f"✅ TMB file with config generated: {os.path.basename(tmb_output)}")
            
            # Check if config data is included
            with open(tmb_output, 'r', encoding='utf-8') as f:
                content = f.read()
            
            has_device_name = config_reader.device_name in content
            has_test_program = config_reader.test_program_name in content
            
            print(f"   Device name included: {'✅' if has_device_name else '❌'}")
            print(f"   Test program included: {'✅' if has_test_program else '❌'}")
            
            return True
        else:
            print("❌ TMB processing with config failed")
            return False
            
    except Exception as e:
        print(f"❌ Error during TMB processing with config: {e}")
        return False


def main():
    """Main test function"""
    print("TMB Processor Test Suite")
    print("=" * 50)
    
    # Change to test directory
    os.chdir(os.path.dirname(os.path.abspath(__file__)))
    
    # Run tests
    test1_result = test_tmb_processor()
    test2_result = test_tmb_with_config()
    
    print("\n" + "=" * 50)
    print("Test Results Summary")
    print("=" * 50)
    print(f"Basic TMB processing: {'✅ PASS' if test1_result else '❌ FAIL'}")
    print(f"TMB with config: {'✅ PASS' if test2_result else '❌ FAIL'}")
    
    if test1_result and test2_result:
        print("\n🎉 All tests passed! TMB processor is working correctly.")
        return True
    else:
        print("\n⚠️  Some tests failed. Please check the implementation.")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
