#!/usr/bin/env python3
"""
Test script for Bump Map Tool functionality
Tests GUI integration, processor initialization, and basic functionality
Author: Yuribytes
Company: Chipone TE development Team
"""

import sys
import os
import tkinter as tk
from tkinter import ttk
import tempfile
import unittest
from unittest.mock import Mock, patch

# Add parent directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from bump_map_tool_frame import BumpMapToolFrame
    from bump_map_processor import BumpMapProcessor, ASEGroupProcessor, SPILProcessor, CustomProcessor
except ImportError as e:
    print(f"Import error: {e}")
    print("Please ensure all required modules are available.")
    sys.exit(1)


class TestBumpMapProcessor(unittest.TestCase):
    """Test cases for BumpMapProcessor"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.processor = BumpMapProcessor()
    
    def test_processor_initialization(self):
        """Test processor initialization"""
        self.assertIsNotNone(self.processor)
        self.assertIsNotNone(self.processor.test_house_processors)
        self.assertEqual(len(self.processor.test_house_processors), 20)
        self.assertIsNone(self.processor.current_processor)
    
    def test_supported_test_houses(self):
        """Test supported test houses list"""
        test_houses = self.processor.get_supported_test_houses()
        self.assertIsInstance(test_houses, list)
        self.assertIn("ASE_Group", test_houses)
        self.assertIn("SPIL", test_houses)
        self.assertIn("Custom", test_houses)
        self.assertEqual(len(test_houses), 20)
    
    def test_set_test_house(self):
        """Test setting test house processor"""
        # Test valid test house
        result = self.processor.set_test_house("ASE_Group")
        self.assertTrue(result)
        self.assertIsNotNone(self.processor.current_processor)
        self.assertEqual(self.processor.current_processor.name, "ASE Group")
        
        # Test invalid test house
        result = self.processor.set_test_house("InvalidTestHouse")
        self.assertFalse(result)
    
    def test_file_validation(self):
        """Test file validation functionality"""
        # Test with non-existent files
        valid, message = self.processor.validate_files("nonexistent1.map", "nonexistent2.map")
        self.assertFalse(valid)
        self.assertIn("not found", message)
        
        # Test with temporary files
        with tempfile.NamedTemporaryFile(mode='w', suffix='.map', delete=False) as bump_file:
            bump_file.write("dummy bump map content")
            bump_path = bump_file.name
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.map', delete=False) as dummy_file:
            dummy_file.write("dummy dummy map content")
            dummy_path = dummy_file.name
        
        try:
            valid, message = self.processor.validate_files(bump_path, dummy_path)
            self.assertTrue(valid)
            self.assertIn("validated successfully", message)
        finally:
            os.unlink(bump_path)
            os.unlink(dummy_path)
    
    def test_processing_stats(self):
        """Test processing statistics"""
        stats = self.processor.get_processing_stats()
        self.assertIsInstance(stats, dict)
        self.assertIn("files_processed", stats)
        self.assertIn("success_count", stats)
        self.assertIn("error_count", stats)
        self.assertEqual(stats["files_processed"], 0)
    
    def test_memory_management(self):
        """Test memory management"""
        memory_usage = self.processor.get_memory_usage_mb()
        self.assertIsInstance(memory_usage, float)
        self.assertGreaterEqual(memory_usage, 0.0)
        
        # Test memory clearing
        self.processor.clear_memory()
        self.assertIsNone(self.processor.current_processor)


class TestTestHouseProcessors(unittest.TestCase):
    """Test cases for specific test house processors"""
    
    def test_ase_group_processor(self):
        """Test ASE Group processor"""
        processor = ASEGroupProcessor()
        self.assertEqual(processor.name, "ASE Group")
        self.assertEqual(processor.format_version, "1.0")
        
        # Test method existence (implementation will be added later)
        self.assertTrue(hasattr(processor, 'parse_bump_map'))
        self.assertTrue(hasattr(processor, 'parse_dummy_map'))
        self.assertTrue(hasattr(processor, 'align_maps'))
        self.assertTrue(hasattr(processor, 'generate_output'))
    
    def test_spil_processor(self):
        """Test SPIL processor"""
        processor = SPILProcessor()
        self.assertEqual(processor.name, "SPIL")
        self.assertEqual(processor.format_version, "1.0")
    
    def test_custom_processor(self):
        """Test Custom processor"""
        processor = CustomProcessor()
        self.assertEqual(processor.name, "Custom")
        self.assertEqual(processor.format_version, "1.0")


class TestBumpMapToolGUI:
    """Test cases for Bump Map Tool GUI (non-unittest for GUI testing)"""
    
    def __init__(self):
        self.root = None
        self.tool_frame = None
    
    def setup_gui(self):
        """Set up GUI for testing"""
        self.root = tk.Tk()
        self.root.title("Bump Map Tool Test")
        self.root.geometry("900x700")
        
        # Mock app controller
        class MockController:
            def __init__(self, root):
                self.root = root
            def return_to_selector_with_confirmation(self, tool_name, has_unsaved):
                print(f"Mock: Return to selector from {tool_name}, unsaved: {has_unsaved}")
                return True
        
        controller = MockController(self.root)
        self.tool_frame = BumpMapToolFrame(self.root, controller)
        self.tool_frame.show()
    
    def test_gui_initialization(self):
        """Test GUI initialization"""
        self.setup_gui()
        
        # Check that main components exist
        assert self.tool_frame is not None
        assert self.tool_frame.main_frame is not None
        assert len(self.tool_frame.test_houses) == 20
        
        # Check variables
        assert self.tool_frame.bump_map_file_path.get() == ""
        assert self.tool_frame.dummy_map_file_path.get() == ""
        assert self.tool_frame.selected_test_house.get() == ""
        
        print("✅ GUI initialization test passed")
    
    def test_file_selection_simulation(self):
        """Test file selection simulation"""
        if not self.tool_frame:
            self.setup_gui()
        
        # Simulate file selection
        test_bump_path = "/path/to/test_bump.map"
        test_dummy_path = "/path/to/test_dummy.map"
        
        self.tool_frame.bump_map_file_path.set(test_bump_path)
        self.tool_frame.dummy_map_file_path.set(test_dummy_path)
        
        assert self.tool_frame.bump_map_file_path.get() == test_bump_path
        assert self.tool_frame.dummy_map_file_path.get() == test_dummy_path
        
        print("✅ File selection simulation test passed")
    
    def test_test_house_selection(self):
        """Test test house selection"""
        if not self.tool_frame:
            self.setup_gui()
        
        # Test setting test house
        self.tool_frame.selected_test_house.set("ASE_Group")
        assert self.tool_frame.selected_test_house.get() == "ASE_Group"
        
        print("✅ Test house selection test passed")
    
    def run_gui_tests(self):
        """Run all GUI tests"""
        print("🚀 Running Bump Map Tool GUI Tests")
        print("=" * 50)
        
        try:
            self.test_gui_initialization()
            self.test_file_selection_simulation()
            self.test_test_house_selection()
            
            print("\n🎉 All GUI tests passed!")
            
            # Show GUI for manual inspection
            print("\n📋 GUI is now displayed for manual inspection...")
            print("Close the window to continue...")
            self.root.mainloop()
            
        except Exception as e:
            print(f"❌ GUI test failed: {e}")
        finally:
            if self.root:
                try:
                    self.root.destroy()
                except:
                    pass


def run_unit_tests():
    """Run unit tests"""
    print("🧪 Running Bump Map Tool Unit Tests")
    print("=" * 50)
    
    # Create test suite
    suite = unittest.TestSuite()
    
    # Add test cases
    suite.addTest(unittest.makeSuite(TestBumpMapProcessor))
    suite.addTest(unittest.makeSuite(TestTestHouseProcessors))
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    return result.wasSuccessful()


def run_integration_test():
    """Run integration test"""
    print("\n🔗 Running Integration Test")
    print("=" * 30)
    
    try:
        # Test processor and GUI integration
        processor = BumpMapProcessor()
        
        # Test setting test house
        success = processor.set_test_house("ASE_Group")
        assert success, "Failed to set test house"
        
        # Test processor in GUI context
        root = tk.Tk()
        root.withdraw()  # Hide window
        
        class MockController:
            def __init__(self, root):
                self.root = root
            def return_to_selector_with_confirmation(self, tool_name, has_unsaved):
                return True
        
        controller = MockController(root)
        tool_frame = BumpMapToolFrame(root, controller)
        
        # Test that GUI can access processor functionality
        assert len(tool_frame.test_houses) == 20
        assert "ASE_Group" in tool_frame.test_houses
        
        root.destroy()
        
        print("✅ Integration test passed")
        return True
        
    except Exception as e:
        print(f"❌ Integration test failed: {e}")
        return False


def main():
    """Main test function"""
    print("🚀 Bump Map Tool - Comprehensive Test Suite")
    print("=" * 60)
    
    # Run unit tests
    unit_tests_passed = run_unit_tests()
    
    # Run integration test
    integration_passed = run_integration_test()
    
    # Run GUI tests
    gui_tester = TestBumpMapToolGUI()
    gui_tester.run_gui_tests()
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 Test Results Summary:")
    print("=" * 60)
    print(f"Unit Tests:       {'✅ PASSED' if unit_tests_passed else '❌ FAILED'}")
    print(f"Integration Test: {'✅ PASSED' if integration_passed else '❌ FAILED'}")
    print(f"GUI Tests:        ✅ COMPLETED (manual verification)")
    
    if unit_tests_passed and integration_passed:
        print("\n🎉 All automated tests passed! Bump Map Tool is ready for use.")
    else:
        print("\n⚠️  Some tests failed. Please check the output above.")
    
    print("\n💡 Next steps:")
    print("  • Implement specific test house format parsers")
    print("  • Add bump and dummy map alignment algorithms")
    print("  • Implement output file generation")
    print("=" * 60)


if __name__ == "__main__":
    main()
