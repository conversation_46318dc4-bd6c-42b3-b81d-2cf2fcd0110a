# TSK MAP Tool 详尽使用说明

## 📋 **目录**
1. [工具概述](#工具概述)
2. [系统要求](#系统要求)
3. [启动方式](#启动方式)
4. [工具选择器](#工具选择器)
5. [Full Map Tool 详细说明](#full-map-tool-详细说明)
6. [AB Map Tool 详细说明](#ab-map-tool-详细说明)
7. [Bump Map Tool 详细说明](#bump-map-tool-详细说明)
8. [配置文件说明](#配置文件说明)
9. [输出文件格式](#输出文件格式)
10. [故障排除](#故障排除)
11. [高级功能](#高级功能)

---

## 🔧 **工具概述**

TSK MAP Tool 是一个专业的半导体测试数据处理工具集，支持多种 MAP 文件格式的处理和分析。

### **主要功能**：
- **Full Map Tool**：批量处理多个 MAP 文件，生成综合分析报告
- **AB Map Tool**：对比分析两个 MAP 文件的差异
- **Bump Map Tool**：处理 Bump Map 数据，支持多种高级算法

### **支持的文件格式**：
- MAP 文件 (各种版本)
- TSK 文件
- TMB 文件
- Excel 配置文件 (.xlsx)

---

## 💻 **系统要求**

### **操作系统**：
- Windows 10/11 (推荐)
- Windows 7/8 (兼容)

### **Python 环境** (如果从源码运行)：
- Python 3.8 或更高版本
- 必需库：tkinter, openpyxl, pandas

### **硬件要求**：
- 内存：至少 4GB RAM (推荐 8GB+)
- 存储：至少 100MB 可用空间
- 处理器：支持多核处理器以获得更好性能

---

## 🚀 **启动方式**

### **方式一：可执行文件 (推荐)**
1. 双击 `TSK_MAP_Tool.exe`
2. 等待工具选择器界面出现

### **方式二：Python 源码**
```bash
python main.py
```

### **方式三：直接启动特定工具**
```bash
python full_map_gui.py      # 启动 Full Map Tool
python tsk_map_gui.py       # 启动 AB Map Tool
python bump_map_tool_frame.py  # 启动 Bump Map Tool
```

---

## 🎯 **工具选择器**

启动后首先看到工具选择器界面：

### **界面布局**：
```
┌─────────────────────────────────────┐
│        TSK MAP Tool Suite           │
├─────────────────────────────────────┤
│  [Full Map Tool]  [AB Map Tool]     │
│                                     │
│  [Bump Map Tool]  [Exit]            │
└─────────────────────────────────────┘
```

### **工具选择**：
- **Full Map Tool**：处理多个 MAP 文件
- **AB Map Tool**：对比两个 MAP 文件
- **Bump Map Tool**：处理 Bump Map 数据
- **Exit**：退出程序

---

## 📊 **Full Map Tool 详细说明**

### **主要功能**：
批量处理多个 MAP 文件，生成综合 Excel 报告和 TMB 文件。

### **界面布局**：
```
┌─────────────────────────────────────────────────────────┐
│  Full Map Tool - Process Multiple MAP Files            │
├─────────────────────────────────────────────────────────┤
│  Configuration File: [Browse...] [Clear]               │
│  Output Folder: [Browse...] [Clear]                    │
├─────────────────────────────────────────────────────────┤
│  Processing Options:                                    │
│  ☑ Filter Empty Areas    ☑ Sort Bins by Quantity      │
│  Rotation Angle: [0°] ▼                               │
├─────────────────────────────────────────────────────────┤
│  Selected Files:                                        │
│  ┌─────────────────────────────────────────────────┐   │
│  │ File1.map                                       │   │
│  │ File2.map                                       │   │
│  │ ...                                             │   │
│  └─────────────────────────────────────────────────┘   │
│  [Add MAP Files...] [Add Folder...] [Remove Selected]  │
│  [Move Up] [Move Down] [Clear All]                     │
├─────────────────────────────────────────────────────────┤
│  [Process All Files] [Process TMB Files] [Clear Memory]│
│  Status: Ready                                          │
└─────────────────────────────────────────────────────────┘
```

### **详细操作步骤**：

#### **1. 配置文件设置 (可选)**
- **目的**：提供 Bin 名称映射和设备信息
- **操作**：点击 "Browse..." 选择 Excel 配置文件
- **格式要求**：
  - A2: 测试程序名称
  - D2: 设备名称, F2: 供应商名称
  - D5: 测试机名称, F5: 探针卡编号
  - H5: 测试流程, J5: 晶圆尺寸
  - A6:B∞: Bin 编号和名称映射

#### **2. 输出文件夹设置 (可选)**
- **目的**：指定输出文件保存位置
- **操作**：点击 "Browse..." 选择输出文件夹
- **默认**：如果不设置，将保存到第一个 MAP 文件所在目录

#### **3. 处理选项配置**
- **Filter Empty Areas**：过滤空白区域，减少输出文件大小
- **Sort Bins by Quantity**：按 Bin 数量排序，便于分析
- **Rotation Angle**：旋转角度 (0°, 90°, 180°, 270°)

#### **4. 文件选择**
- **Add MAP Files...**：选择单个或多个 MAP 文件
- **Add Folder...**：添加文件夹中的所有 MAP 文件
- **Remove Selected**：移除选中的文件
- **Move Up/Down**：调整文件处理顺序
- **Clear All**：清空所有文件

#### **5. 处理执行**
- **Process All Files**：生成综合 Excel 报告
- **Process TMB Files**：生成 TMB 格式文件
- **Clear Memory**：清理内存，释放资源

### **输出文件**：
- **Excel 报告**：`FullMap_Xfiles_YYYYMMDD_HHMMSS.xlsx`
- **TMB 文件**：`filename_YYYYMMDD_HHMMSS.tmb`

---

## 🔍 **AB Map Tool 详细说明**

### **主要功能**：
对比分析两个 MAP 文件的差异，生成详细的对比报告。

### **界面布局**：
```
┌─────────────────────────────────────────────────────────┐
│  AB Map Tool - Compare Two MAP Files                   │
├─────────────────────────────────────────────────────────┤
│  Configuration File: [Browse...] [Clear]               │
├─────────────────────────────────────────────────────────┤
│  MAP File A: [Browse...] [Clear]                       │
│  MAP File B: [Browse...] [Clear]                       │
├─────────────────────────────────────────────────────────┤
│  Processing Options:                                    │
│  ☑ Filter Empty Areas    ☑ Sort Bins by Quantity      │
│  Rotation Angle: [0°] ▼                               │
├─────────────────────────────────────────────────────────┤
│  [Process AB Comparison] [Return to Selector]          │
│  Status: Ready                                          │
└─────────────────────────────────────────────────────────┘
```

### **详细操作步骤**：

#### **1. 配置文件设置 (可选)**
- 与 Full Map Tool 相同的配置文件格式

#### **2. 文件选择**
- **MAP File A**：选择第一个对比文件
- **MAP File B**：选择第二个对比文件
- **要求**：两个文件必须具有相同的芯片尺寸

#### **3. 处理选项**
- 与 Full Map Tool 相同的选项

#### **4. 执行对比**
- **Process AB Comparison**：开始对比分析
- **Return to Selector**：返回工具选择器

### **输出文件**：
- **对比报告**：`AB_Comparison_YYYYMMDD_HHMMSS.xlsx`
- **包含内容**：
  - 文件 A 和 B 的基本信息
  - Bin 分布对比
  - 差异分析
  - 统计摘要

---

## 🎨 **Bump Map Tool 详细说明**

### **主要功能**：
处理 Bump Map 数据，支持多种高级处理算法。

### **可用处理器**：
1. **Enhanced Processor**：增强型处理器，支持高级特性
2. **Advanced Processor**：高级处理器，提供更多选项
3. **Precise Processor**：精确处理器，高精度计算
4. **Bump Processor**：专用 Bump 处理器

### **详细操作**：
1. 选择处理器类型
2. 配置处理参数
3. 选择输入文件
4. 执行处理
5. 查看结果

---

## 📄 **配置文件说明**

### **Excel 配置文件格式**：

| 单元格 | 内容 | 示例 | 必需 |
|--------|------|------|------|
| **A2** | 测试程序名称 | "CP1_TEST_PROGRAM" | 是 |
| **D2** | 设备名称 | "DEVICE_001" | 否 |
| **F2** | 供应商名称 | "VENDOR_NAME" | 否 |
| **D5** | 测试机名称 | "TESTER_01" | 否 |
| **F5** | 探针卡编号 | "PROBE_CARD_123" | 否 |
| **H5** | 测试流程 | "FLOW_A" | 否 |
| **J5** | 晶圆尺寸 | "8inch" | 否 |
| **A6:B∞** | Bin 映射 | 1="Pass", 2="Fail" | 是 |

### **Bin 映射格式**：
```
A列    B列
1      Pass
2      Fail
3      Low_Current
4      High_Current
...    ...
```

---

## 📁 **输出文件格式**

### **Excel 报告结构**：
1. **Summary Sheet**：总体摘要
2. **File Details**：文件详细信息
3. **Bin Analysis**：Bin 分析
4. **Statistics**：统计数据
5. **Raw Data**：原始数据

### **TMB 文件格式**：
```
Header Information
Device: DEVICE_NAME
Wafer Size: 8inch
Test Program: CP1_TEST_PROGRAM
...

Die Data
X,Y,Bin
1,1,1
1,2,2
...
```

---

## 🔧 **故障排除**

### **常见问题**：

#### **1. 文件无法打开**
- **原因**：文件格式不支持或文件损坏
- **解决**：检查文件格式，尝试其他文件

#### **2. 内存不足**
- **原因**：处理大文件时内存不够
- **解决**：使用 "Clear Memory" 功能，或重启程序

#### **3. 配置文件读取失败**
- **原因**：Excel 文件格式错误
- **解决**：检查配置文件格式，确保单元格内容正确

#### **4. 输出文件生成失败**
- **原因**：输出目录权限不足或磁盘空间不够
- **解决**：检查目录权限，清理磁盘空间

### **错误代码**：
- **E001**：文件读取错误
- **E002**：内存不足
- **E003**：配置文件错误
- **E004**：输出错误

---

## 🚀 **高级功能**

### **1. 批处理模式**
- 支持命令行参数
- 自动化处理流程

### **2. 内存优化**
- 自动内存管理
- 大文件处理优化

### **3. 多线程处理**
- 并行处理多个文件
- 提高处理速度

### **4. 自定义输出格式**
- 可配置输出模板
- 支持多种导出格式

---

## 📞 **技术支持**

### **联系信息**：
- **开发团队**：Chipone TE Development Team
- **作者**：Yuribytes
- **版本**：2025.08.12

### **获取帮助**：
1. 查看程序内置帮助
2. 参考示例文件
3. 联系技术支持

---

## 📝 **更新日志**

### **最新版本 (2025.08.12)**：
- ✅ 添加动态弹窗大小调整
- ✅ 修复 Wafer Size J5 单元格读取
- ✅ 优化 Full Map Tool 详细弹窗内容
- ✅ 改进居中弹窗显示
- ✅ 增强用户体验

### **历史版本**：
- 2025.08.11: 添加 Bump Map Tool 高级功能
- 2025.08.10: 优化内存管理
- 2025.08.09: 修复文件处理错误

---

**文档版本**：详尽版 v1.0  
**最后更新**：2025-08-12  
**适用版本**：TSK MAP Tool v2025.08.12
