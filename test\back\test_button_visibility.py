#!/usr/bin/env python3
"""
Test script to verify all buttons are visible in Full Map Tool
Specifically tests the bottom button area that was being clipped
"""

import os
import sys
import tkinter as tk

# Add parent directory to path to import modules
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_button_visibility():
    """Test that all buttons are visible and accessible"""
    print("Testing Button Visibility...")
    
    try:
        from full_map_gui import FullMapGUI
        
        # Create test window
        root = tk.Tk()
        
        # Create Full Map GUI
        app = FullMapGUI(root)
        
        # Update layout to get accurate measurements
        root.update_idletasks()
        
        # Get window dimensions
        window_width = root.winfo_width()
        window_height = root.winfo_height()
        
        print(f"Window size: {window_width} x {window_height}")
        
        # Find all buttons in the GUI
        buttons = []
        
        def find_buttons(widget):
            """Recursively find all buttons"""
            for child in widget.winfo_children():
                if isinstance(child, tk.Button) or isinstance(child, tk.ttk.Button):
                    buttons.append(child)
                find_buttons(child)
        
        find_buttons(root)
        
        print(f"Found {len(buttons)} buttons")
        
        # Check if buttons are within window bounds
        all_visible = True
        
        for i, button in enumerate(buttons):
            try:
                # Get button position and size
                button.update_idletasks()
                x = button.winfo_x()
                y = button.winfo_y()
                width = button.winfo_width()
                height = button.winfo_height()
                
                # Get button text for identification
                try:
                    text = button.cget('text')
                except:
                    text = f"Button_{i}"
                
                # Check if button is within window bounds
                right_edge = x + width
                bottom_edge = y + height
                
                visible = (x >= 0 and y >= 0 and 
                          right_edge <= window_width and 
                          bottom_edge <= window_height)
                
                status = "✓ VISIBLE" if visible else "✗ CLIPPED"
                print(f"  {text}: {status} (pos: {x},{y}, size: {width}x{height})")
                
                if not visible:
                    all_visible = False
                    if bottom_edge > window_height:
                        print(f"    → Bottom edge {bottom_edge} exceeds window height {window_height}")
                    if right_edge > window_width:
                        print(f"    → Right edge {right_edge} exceeds window width {window_width}")
                
            except Exception as e:
                print(f"  Error checking button {i}: {e}")
        
        root.destroy()
        
        if all_visible:
            print("✓ All buttons are visible within window bounds")
        else:
            print("✗ Some buttons are clipped or outside window bounds")
        
        return all_visible
        
    except Exception as e:
        print(f"Error testing button visibility: {e}")
        return False


def test_specific_buttons():
    """Test specific buttons that were mentioned as being clipped"""
    print("\nTesting Specific Bottom Buttons...")
    
    try:
        from full_map_gui import FullMapGUI
        
        # Create test window
        root = tk.Tk()
        
        # Create Full Map GUI
        app = FullMapGUI(root)
        
        # Update layout
        root.update_idletasks()
        
        window_height = root.winfo_height()
        
        # Look for specific buttons by text
        target_buttons = ["Process All Files", "Clear Memory", "Back", "Exit"]
        found_buttons = {}
        
        def find_button_by_text(widget, target_text):
            """Find button by its text"""
            for child in widget.winfo_children():
                if isinstance(child, (tk.Button, tk.ttk.Button)):
                    try:
                        if child.cget('text') == target_text:
                            return child
                    except:
                        pass
                result = find_button_by_text(child, target_text)
                if result:
                    return result
            return None
        
        # Find each target button
        for button_text in target_buttons:
            button = find_button_by_text(root, button_text)
            if button:
                found_buttons[button_text] = button
                
                # Check position
                button.update_idletasks()
                y = button.winfo_y()
                height = button.winfo_height()
                bottom = y + height
                
                visible = bottom <= window_height
                status = "✓ VISIBLE" if visible else "✗ CLIPPED"
                
                print(f"  {button_text}: {status} (y: {y}, bottom: {bottom}, window_height: {window_height})")
            else:
                print(f"  {button_text}: NOT FOUND")
        
        root.destroy()
        
        # Check if all target buttons were found and visible
        all_found = len(found_buttons) == len(target_buttons)
        all_visible = all(button.winfo_y() + button.winfo_height() <= window_height 
                         for button in found_buttons.values())
        
        if all_found and all_visible:
            print("✓ All critical buttons are visible")
            return True
        else:
            print("✗ Some critical buttons are missing or clipped")
            return False
        
    except Exception as e:
        print(f"Error testing specific buttons: {e}")
        return False


def test_window_content_fit():
    """Test if all content fits within the window"""
    print("\nTesting Window Content Fit...")
    
    try:
        from full_map_gui import FullMapGUI
        
        # Create test window
        root = tk.Tk()
        
        # Create Full Map GUI
        app = FullMapGUI(root)
        
        # Update layout
        root.update_idletasks()
        
        # Get window size
        window_width = root.winfo_width()
        window_height = root.winfo_height()
        
        # Get required size for all content
        root.update_idletasks()
        required_width = root.winfo_reqwidth()
        required_height = root.winfo_reqheight()
        
        print(f"Window size: {window_width} x {window_height}")
        print(f"Required size: {required_width} x {required_height}")
        
        width_ok = window_width >= required_width
        height_ok = window_height >= required_height
        
        print(f"Width adequate: {'✓' if width_ok else '✗'}")
        print(f"Height adequate: {'✓' if height_ok else '✗'}")
        
        if not height_ok:
            shortage = required_height - window_height
            print(f"Height shortage: {shortage} pixels")
        
        root.destroy()
        
        return width_ok and height_ok
        
    except Exception as e:
        print(f"Error testing content fit: {e}")
        return False


def main():
    """Run all button visibility tests"""
    print("Button Visibility Test")
    print("=" * 40)
    
    # Run tests
    test1 = test_button_visibility()
    test2 = test_specific_buttons()
    test3 = test_window_content_fit()
    
    # Summary
    print("\n" + "=" * 40)
    print("Test Results:")
    print(f"   General Button Visibility: {'PASS' if test1 else 'FAIL'}")
    print(f"   Critical Buttons: {'PASS' if test2 else 'FAIL'}")
    print(f"   Content Fit: {'PASS' if test3 else 'FAIL'}")
    
    all_passed = test1 and test2 and test3
    print(f"\nOverall: {'ALL TESTS PASSED' if all_passed else 'SOME TESTS FAILED'}")
    
    if all_passed:
        print("\n✓ All buttons are visible and accessible!")
        print("✓ No more clipping issues")
        print("✓ Window height is adequate")
    else:
        print("\n✗ Some visibility issues remain")
        print("Consider increasing window height further")
    
    return all_passed


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
