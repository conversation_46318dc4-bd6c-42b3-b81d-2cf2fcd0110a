#!/usr/bin/env python3
"""
详细调试遍历逻辑
找出为什么统计数据不匹配

Author: Yuribytes
Company: Chipone TE development Team
"""

import os
import sys

# 添加父目录到路径
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, parent_dir)

from nepes_enhanced_processor import NEPESEnhancedProcessor

def create_detailed_tracking_processor():
    """创建详细跟踪处理器"""
    
    class DetailedTrackingProcessor(NEPESEnhancedProcessor):
        def __init__(self):
            super().__init__()
            self.traversal_log = []
            self.value_counts = {"__": 0, "00": 0, "0C": 0, "XX": 0, "other": 0}
            self.boundary_issues = []
        
        def apply_enhanced_conversion(self):
            """重写以详细跟踪遍历"""
            print(f"🔄 开始详细跟踪遍历...")
            print(f"   rowsize: {self.rowsize}, columnsize: {self.columnsize}")
            
            total_processed = 0
            total_skipped = 0
            
            for i in range(1, self.rowsize + 1):
                for j in range(1, self.columnsize + 1):
                    row_index = i - 1
                    col_index = j - 1
                    
                    # 检查边界
                    if row_index >= len(self.bump_data):
                        self.boundary_issues.append(f"Row {row_index} >= {len(self.bump_data)}")
                        total_skipped += 1
                        continue
                    
                    if col_index >= len(self.bump_data[row_index]):
                        self.boundary_issues.append(f"Col {col_index} >= {len(self.bump_data[row_index])} at row {row_index}")
                        total_skipped += 1
                        continue
                    
                    # 获取值
                    bump_value = self.bump_data[row_index][col_index]
                    
                    # 统计值
                    if bump_value == "__":
                        self.value_counts["__"] += 1
                    elif bump_value == "00":
                        self.value_counts["00"] += 1
                    elif bump_value == "0C":
                        self.value_counts["0C"] += 1
                    elif bump_value == "XX":
                        self.value_counts["XX"] += 1
                    else:
                        self.value_counts["other"] += 1
                    
                    # 计算位置
                    die_index = (i - 1) * self.columnsize + j - 1
                    category_pos = self.TestResultCategory + 4 * die_index - 1
                    
                    display_x = j - 1
                    display_y = i - 1
                    
                    # 记录遍历
                    self.traversal_log.append({
                        'x': display_x,
                        'y': display_y,
                        'i': i,
                        'j': j,
                        'row_index': row_index,
                        'col_index': col_index,
                        'die_index': die_index,
                        'bump_value': bump_value,
                        'category_pos': category_pos
                    })
                    
                    total_processed += 1
                    
                    # 检查文件边界
                    if category_pos + 4 <= len(self.tsk_processor.filearray):
                        self.processing_stats['total_positions'] += 1
                        
                        binary_pattern = self.get_binary_modification_pattern(bump_value)
                        
                        if binary_pattern is None:
                            self.processing_stats['unchanged_positions'] += 1
                        else:
                            # 执行写入
                            for k in range(4):
                                self.tsk_processor.filearray[category_pos + k] = binary_pattern[k]
                                self.processing_stats['binary_modifications'] += 1
                            
                            if bump_value == "00":
                                self.processing_stats['pass_positions'] += 1
                            else:
                                self.processing_stats['fail_positions'] += 1
                    else:
                        # 越界
                        if bump_value == "__":
                            self.processing_stats['unchanged_positions'] += 1
                        else:
                            self.processing_stats['errors'] += 1
            
            print(f"   总处理: {total_processed}, 总跳过: {total_skipped}")
            print(f"   边界问题: {len(self.boundary_issues)}")
            
            return True
    
    return DetailedTrackingProcessor()

def debug_detailed_traversal():
    """详细调试遍历"""
    print("🔍 详细调试遍历逻辑")
    print("=" * 50)
    
    bump_file = "D97127.09"
    dummy_file = "009.NNS157-09-E4"
    output_file = "detailed_traversal.tsk"
    
    # 使用详细跟踪处理器
    processor = create_detailed_tracking_processor()
    success = processor.process_nepes_enhanced(bump_file, dummy_file, output_file)
    
    if not success:
        print("❌ 处理失败")
        return False
    
    # 分析遍历日志
    print(f"\n📊 遍历统计:")
    print(f"   总遍历位置: {len(processor.traversal_log)}")
    print(f"   边界问题: {len(processor.boundary_issues)}")
    
    if processor.boundary_issues:
        print(f"   边界问题详情:")
        for issue in processor.boundary_issues[:5]:  # 只显示前5个
            print(f"     {issue}")
    
    # 值统计
    print(f"\n📊 值统计 (遍历过程中):")
    for value, count in processor.value_counts.items():
        print(f"   '{value}': {count}")
    
    total_values = sum(processor.value_counts.values())
    print(f"   总计: {total_values}")
    
    # 处理器统计
    stats = processor.get_processing_stats()
    print(f"\n📊 处理器统计:")
    print(f"   '__'位置: {stats['processing_stats']['unchanged_positions']}")
    print(f"   '00'位置: {stats['processing_stats']['pass_positions']}")
    print(f"   'XX'位置: {stats['processing_stats']['fail_positions']}")
    print(f"   错误: {stats['processing_stats']['errors']}")
    
    # 对比分析
    print(f"\n🎯 对比分析:")
    
    # 遍历统计 vs 处理器统计
    traversal_underscore = processor.value_counts["__"]
    processor_underscore = stats['processing_stats']['unchanged_positions']
    
    traversal_zero = processor.value_counts["00"]
    processor_zero = stats['processing_stats']['pass_positions']
    
    traversal_fail = processor.value_counts["0C"] + processor.value_counts["XX"] + processor.value_counts["other"]
    processor_fail = stats['processing_stats']['fail_positions']
    
    print(f"   '__'位置: 遍历{traversal_underscore} vs 处理器{processor_underscore} {'✅' if traversal_underscore == processor_underscore else '❌'}")
    print(f"   '00'位置: 遍历{traversal_zero} vs 处理器{processor_zero} {'✅' if traversal_zero == processor_zero else '❌'}")
    print(f"   失败位置: 遍历{traversal_fail} vs 处理器{processor_fail} {'✅' if traversal_fail == processor_fail else '❌'}")
    
    # 检查前10个位置的详细信息
    print(f"\n📊 前10个位置详情:")
    for i, log in enumerate(processor.traversal_log[:10]):
        print(f"   {i+1}. ({log['x']},{log['y']}): '{log['bump_value']}', die_index={log['die_index']}, pos={log['category_pos']}")
    
    return True

def main():
    """主调试函数"""
    print("🧪 详细遍历逻辑调试")
    print("=" * 70)
    
    # 切换到测试目录
    os.chdir(os.path.dirname(os.path.abspath(__file__)))
    
    try:
        # 详细调试遍历
        result = debug_detailed_traversal()
        
        print("\n" + "=" * 70)
        print("🎉 调试完成")
        
        return result
            
    except Exception as e:
        print(f"❌ 调试过程出错: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
