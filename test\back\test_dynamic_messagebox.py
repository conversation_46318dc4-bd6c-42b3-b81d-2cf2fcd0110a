#!/usr/bin/env python3
"""
Test script for dynamic sizing messagebox functionality
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import tkinter as tk
from tkinter import ttk
import centered_messagebox as messagebox

def test_dynamic_sizing():
    """Test the dynamic sizing messagebox functionality"""
    root = tk.Tk()
    root.title("Full Map Tool - Dynamic Sizing MessageBox Test")
    root.geometry("900x700+100+100")
    root.configure(bg='lightgray')
    
    # Create a frame to simulate the Full Map Tool interface
    main_frame = ttk.Frame(root, padding="20")
    main_frame.pack(fill=tk.BOTH, expand=True)
    
    title_label = ttk.Label(main_frame, text="Dynamic Sizing MessageBox Test", 
                           font=("Arial", 14, "bold"))
    title_label.pack(pady=(0, 20))
    
    def test_short_message():
        """Test short message - should use small dialog"""
        messagebox.showinfo("Short Message", "This is a short message.", parent=root)
    
    def test_medium_message():
        """Test medium message - should use medium dialog"""
        success_msg = "Full Map Processing Complete!\n\n"
        success_msg += "Successfully processed 3 MAP files:\n"
        success_msg += "• 019.3AD416-19-F4\n"
        success_msg += "• 020.3AD416-20-F4\n"
        success_msg += "• 021.3AD416-21-F4\n"
        success_msg += "\nOutput file: FullMap_3files_20250812_143021.xlsx"
        success_msg += "\nOutput folder: C:/Users/<USER>/Desktop/map_raw_data/map/test"
        success_msg += "\n\nProcessing options applied:"
        success_msg += "\n• Rotation angle: 0°"
        success_msg += "\n• Filter empty areas: Yes"
        success_msg += "\n• Sort bins by quantity: Yes"
        success_msg += "\n• Configuration file: CP1_program_bin.xlsx"
        
        messagebox.showinfo("Processing Complete", success_msg, parent=root)
    
    def test_long_message():
        """Test long message - should use large dialog with scrolling"""
        success_msg = "Full Map Processing Complete!\n\n"
        success_msg += "Successfully processed 15 MAP files:\n"
        for i in range(15):
            success_msg += f"• File_{i+1:03d}.3AD416-{i+19}-F4_very_long_filename_example\n"
        success_msg += "\nOutput file: FullMap_15files_20250812_143021_with_very_long_filename.xlsx"
        success_msg += "\nOutput folder: C:/Users/<USER>/Desktop/map_raw_data/map/test/output/final/results"
        success_msg += "\n\nProcessing options applied:"
        success_msg += "\n• Rotation angle: 90°"
        success_msg += "\n• Filter empty areas: Yes"
        success_msg += "\n• Sort bins by quantity: Yes (descending order)"
        success_msg += "\n• Configuration file: CP1_program_bin_extended_configuration.xlsx"
        success_msg += "\n• Memory optimization: Enabled"
        success_msg += "\n• Parallel processing: 4 threads"
        success_msg += "\n• Output format: Excel 2019 (.xlsx)"
        success_msg += "\n• Compression: Enabled"
        success_msg += "\n• Backup created: Yes"
        success_msg += "\n\nAdditional information:"
        success_msg += "\n• Total processing time: 2 minutes 34 seconds"
        success_msg += "\n• Memory usage: 245.7 MB"
        success_msg += "\n• Files processed successfully: 15/15"
        success_msg += "\n• Warnings: 0"
        success_msg += "\n• Errors: 0"
        
        messagebox.showinfo("Processing Complete", success_msg, parent=root)
    
    def test_very_long_message():
        """Test very long message - should use scrollable text widget"""
        error_msg = "Full Map Processing Error!\n\n"
        error_msg += "An unexpected error occurred while processing 20 MAP files.\n\n"
        error_msg += "Error details:\n"
        error_msg += "FileNotFoundError: [Errno 2] No such file or directory: 'very_long_output_filename_that_exceeds_normal_length.xlsx'\n\n"
        error_msg += "Detailed error trace:\n"
        for i in range(20):
            error_msg += f"  File {i+1}: Processing failed at step {i%5+1}\n"
            error_msg += f"    Error: Invalid data format in row {i*100+50}\n"
            error_msg += f"    Location: Column {chr(65+i%26)}{i*10+15}\n"
            error_msg += f"    Expected: Numeric value\n"
            error_msg += f"    Found: '{chr(65+i%26)}{i}invalid_data_example'\n"
        
        error_msg += "\nTroubleshooting steps:\n"
        error_msg += "• Verify all selected files are valid MAP format\n"
        error_msg += "• Check available disk space and memory\n"
        error_msg += "• Ensure output folder has write permissions\n"
        error_msg += "• Try processing fewer files at once\n"
        error_msg += "• Check console output for additional details\n"
        error_msg += "• Verify file encoding (should be UTF-8 or ASCII)\n"
        error_msg += "• Check for special characters in file names\n"
        error_msg += "• Ensure all files are not corrupted\n"
        error_msg += "• Try running as administrator\n"
        error_msg += "• Check antivirus software interference\n"
        error_msg += "• Verify network drive accessibility\n"
        error_msg += "• Check file locks by other applications\n"
        
        messagebox.showerror("Processing Error", error_msg, parent=root)
    
    def test_wide_message():
        """Test message with very long lines - should adjust width"""
        wide_msg = "Processing Complete!\n\n"
        wide_msg += "Output file: FullMap_VeryLongFileNameThatExceedsNormalDisplayWidthAndShouldCauseTheDialogToExpandHorizontally_20250812_143021.xlsx\n"
        wide_msg += "Output folder: C:/Users/<USER>/Desktop/map_raw_data/map/test/output/final/results/with/very/deep/nested/folder/structure/that/is/quite/long\n"
        wide_msg += "Configuration: CP1_program_bin_with_extremely_long_configuration_filename_that_should_test_horizontal_expansion.xlsx"
        
        messagebox.showinfo("Wide Content Test", wide_msg, parent=root)
    
    # Create test buttons
    button_frame = ttk.Frame(main_frame)
    button_frame.pack(fill=tk.BOTH, expand=True)
    
    buttons = [
        ("Short Message", test_short_message, "Test small dialog size"),
        ("Medium Message", test_medium_message, "Test medium dialog size"),
        ("Long Message", test_long_message, "Test large dialog size"),
        ("Very Long Message", test_very_long_message, "Test scrollable text widget"),
        ("Wide Message", test_wide_message, "Test horizontal expansion")
    ]
    
    for i, (text, command, tooltip) in enumerate(buttons):
        btn = ttk.Button(button_frame, text=text, command=command, width=20)
        btn.grid(row=i//2, column=i%2, padx=10, pady=10, sticky="ew")
    
    # Configure grid weights
    button_frame.columnconfigure(0, weight=1)
    button_frame.columnconfigure(1, weight=1)
    
    # Add instructions
    instructions = ttk.Label(main_frame, 
                           text="Click the buttons above to test dynamic sizing messagebox dialogs.\n"
                                "Notice how the dialog size adjusts based on content length and width.\n"
                                "Very long messages will use scrollable text widgets.",
                           justify=tk.CENTER, font=("Arial", 10))
    instructions.pack(pady=(20, 0))
    
    # Add features note
    features_note = ttk.Label(main_frame, 
                            text="Features: Dynamic width/height calculation • Scrollable text for long content • Resizable dialogs • Optimized font sizes",
                            justify=tk.CENTER, font=("Arial", 9), foreground="blue")
    features_note.pack(pady=(10, 0))
    
    root.mainloop()

if __name__ == "__main__":
    test_dynamic_sizing()
