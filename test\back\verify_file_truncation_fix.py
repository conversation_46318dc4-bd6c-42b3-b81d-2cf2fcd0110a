#!/usr/bin/env python3
"""
验证文件截断问题的修复
确认TSK处理器恢复原始状态，NEPES处理器正确处理截断文件
"""

import os
import sys

# 添加父目录到路径
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, parent_dir)

from nepes_enhanced_processor import NEPESEnhancedProcessor
from tsk_map_processor import TSKMapProcessor

def verify_tsk_processor_restored():
    """验证TSK处理器已恢复原始状态"""
    print("🔧 验证TSK处理器恢复状态")
    print("=" * 40)
    
    processor = TSKMapProcessor()
    success = processor.read_file('009.NNS157-09-E4')
    
    if success:
        processor.parse_file_header()
        print(f"✅ TSK处理器状态:")
        print(f"   TestResultCategory: {processor.TestResultCategory}")
        print(f"   预期值: 15049")
        
        if processor.TestResultCategory == 15049:
            print(f"   ✅ TSK处理器已恢复原始状态")
            return True
        else:
            print(f"   ❌ TSK处理器状态不正确")
            return False
    else:
        print(f"❌ TSK处理器加载失败")
        return False

def analyze_file_truncation():
    """分析文件截断问题"""
    print(f"\n🔍 分析文件截断问题")
    print("=" * 40)
    
    processor = TSKMapProcessor()
    processor.read_file('009.NNS157-09-E4')
    processor.parse_file_header()
    
    # 计算理论文件大小
    total_dies = processor.columnsize * processor.rowsize
    theoretical_size = processor.TestResultCategory + total_dies * 4
    actual_size = len(processor.filearray)
    
    print(f"📊 文件大小分析:")
    print(f"   实际文件大小: {actual_size} bytes")
    print(f"   理论文件大小: {theoretical_size} bytes")
    print(f"   截断字节数: {theoretical_size - actual_size} bytes")
    
    # 检查最后一个位置
    last_die_index = total_dies - 1
    last_pos = processor.TestResultCategory + last_die_index * 4
    
    print(f"\n📍 最后位置分析:")
    print(f"   最后die索引: {last_die_index}")
    print(f"   最后位置字节: {last_pos}-{last_pos+3}")
    print(f"   文件边界: 0-{actual_size-1}")
    print(f"   越界情况: {last_pos+3 > actual_size-1}")
    
    return theoretical_size - actual_size == 1

def test_nepes_enhanced_with_truncation():
    """测试NEPES Enhanced处理器处理截断文件"""
    print(f"\n🧪 测试NEPES Enhanced处理器")
    print("=" * 40)
    
    processor = NEPESEnhancedProcessor()
    
    print("🚀 开始处理...")
    success = processor.process_nepes_enhanced(
        bump_map_path="D97127.09",
        dummy_map_path="009.NNS157-09-E4", 
        output_path="truncation_fix_test_output.tsk"
    )
    
    if success:
        stats = processor.get_processing_stats()
        
        print(f"\n📊 处理结果:")
        print(f"   总位置: {stats['processing_stats']['total_positions']}")
        print(f"   '__'位置: {stats['processing_stats']['unchanged_positions']}")
        print(f"   '00'位置: {stats['processing_stats']['pass_positions']}")
        print(f"   'XX'位置: {stats['processing_stats']['fail_positions']}")
        print(f"   错误数: {stats['processing_stats']['errors']}")
        
        # 验证关键指标
        expected_underscore = 500
        actual_underscore = stats['processing_stats']['unchanged_positions']
        
        print(f"\n🎯 关键验证:")
        print(f"   预期'__'位置: {expected_underscore}")
        print(f"   实际'__'位置: {actual_underscore}")
        
        if actual_underscore == expected_underscore:
            print(f"   ✅ 所有'__'位置正确处理 (包括截断位置)")
        else:
            print(f"   ❌ '__'位置处理不完整")
            
        if stats['processing_stats']['errors'] == 0:
            print(f"   ✅ 无关键错误 (截断的'__'位置不算错误)")
        else:
            print(f"   ❌ 存在 {stats['processing_stats']['errors']} 个错误")
            
        return actual_underscore == expected_underscore and stats['processing_stats']['errors'] == 0
    else:
        print(f"❌ 处理失败")
        return False

def main():
    """主验证函数"""
    print("🧪 文件截断问题修复验证")
    print("=" * 60)
    
    os.chdir(os.path.dirname(os.path.abspath(__file__)))
    
    try:
        # 步骤1: 验证TSK处理器恢复
        tsk_ok = verify_tsk_processor_restored()
        
        # 步骤2: 分析文件截断
        truncation_confirmed = analyze_file_truncation()
        
        # 步骤3: 测试NEPES处理器
        nepes_ok = test_nepes_enhanced_with_truncation()
        
        print(f"\n" + "=" * 60)
        print(f"🎉 验证结果总结:")
        print(f"   TSK处理器恢复: {'✅' if tsk_ok else '❌'}")
        print(f"   文件截断确认: {'✅' if truncation_confirmed else '❌'}")
        print(f"   NEPES处理器: {'✅' if nepes_ok else '❌'}")
        
        if tsk_ok and truncation_confirmed and nepes_ok:
            print(f"\n🎯 结论:")
            print(f"   ✅ TSK处理器恢复原始状态 (TestResultCategory=15049)")
            print(f"   ✅ 文件确实被截断1字节 (24808 vs 24809)")
            print(f"   ✅ NEPES处理器正确处理截断情况")
            print(f"   ✅ 所有500个'__'位置都被正确处理")
            print(f"   ✅ 截断的'__'位置不影响数据完整性")
            return True
        else:
            print(f"\n❌ 存在问题需要进一步调查")
            return False
            
    except Exception as e:
        print(f"❌ 验证过程出错: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
