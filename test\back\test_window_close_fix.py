#!/usr/bin/env python3
"""
Test script for window close fix
Verifies that the tool selector handles window close events properly
"""

import sys
import os
import threading
import time

# Add parent directory to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


def test_tool_selector_close():
    """Test that ToolSelector handles window close properly"""
    print("Testing ToolSelector window close handling...")
    
    try:
        from tool_selector import ToolSelector
        
        # Create selector
        selector = ToolSelector()
        
        # Test that window close protocol is set
        if hasattr(selector, 'window_closed'):
            print("✅ window_closed attribute exists")
        else:
            print("❌ window_closed attribute missing")
            return False
        
        # Test that on_window_close method exists
        if hasattr(selector, 'on_window_close'):
            print("✅ on_window_close method exists")
        else:
            print("❌ on_window_close method missing")
            return False
        
        # Test window close protocol
        protocol = selector.root.protocol("WM_DELETE_WINDOW")
        if protocol:
            print("✅ Window close protocol is set")
        else:
            print("❌ Window close protocol not set")
        
        # Simulate window close
        print("Testing window close simulation...")
        selector.on_window_close()
        
        if selector.window_closed:
            print("✅ window_closed flag set correctly")
        else:
            print("❌ window_closed flag not set")
            return False
        
        if selector.selected_tool is None:
            print("✅ selected_tool is None after close")
        else:
            print("❌ selected_tool should be None after close")
            return False
        
        # Clean up
        try:
            selector.root.destroy()
        except:
            pass  # Already destroyed
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing ToolSelector: {e}")
        return False


def test_show_method_safety():
    """Test that show() method handles errors safely"""
    print("\nTesting show() method safety...")
    
    try:
        from tool_selector import ToolSelector
        
        # Create selector
        selector = ToolSelector()
        
        # Simulate window close before show
        selector.window_closed = True
        
        # Test that show returns safely
        def close_window_after_delay():
            time.sleep(0.1)  # Small delay
            try:
                selector.root.quit()
            except:
                pass
        
        # Start thread to close window
        close_thread = threading.Thread(target=close_window_after_delay)
        close_thread.daemon = True
        close_thread.start()
        
        # This should not raise an exception
        result = selector.show()
        
        print("✅ show() method completed without exception")
        print(f"✅ Returned result: {result}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing show() method: {e}")
        return False


def test_main_py_integration():
    """Test integration with main.py"""
    print("\nTesting main.py integration...")
    
    try:
        import subprocess
        import sys
        
        # Test main.py with timeout (simulates user closing window)
        print("Testing main.py with quick exit...")
        
        # Create a script that will close quickly
        test_script = '''
import sys
import os
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from tool_selector import ToolSelector

# Create and immediately close
selector = ToolSelector()
selector.on_window_close()
result = selector.show()
print(f"Result: {result}")
'''
        
        # Write temporary test script
        with open('temp_test_close.py', 'w') as f:
            f.write(test_script)
        
        try:
            # Run the test script
            result = subprocess.run([sys.executable, 'temp_test_close.py'], 
                                  capture_output=True, text=True, timeout=5)
            
            if result.returncode == 0:
                print("✅ Integration test passed")
                if "Result: None" in result.stdout:
                    print("✅ Correct None result returned")
                else:
                    print(f"⚠️ Unexpected result: {result.stdout}")
                return True
            else:
                print(f"❌ Integration test failed: {result.stderr}")
                return False
                
        finally:
            # Clean up temp file
            try:
                os.remove('temp_test_close.py')
            except:
                pass
        
    except Exception as e:
        print(f"❌ Error testing integration: {e}")
        return False


def main():
    """Run all tests"""
    print("Window Close Fix Verification")
    print("=" * 50)
    
    tests = [
        test_tool_selector_close,
        test_show_method_safety,
        test_main_py_integration
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            print()  # Add spacing between tests
        except Exception as e:
            print(f"❌ Test {test.__name__} failed with exception: {e}")
            print()
    
    print("=" * 50)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All window close fix tests passed!")
        print("\nFix Summary:")
        print("✅ Added window_closed flag to track window state")
        print("✅ Added on_window_close() method to handle close events")
        print("✅ Set WM_DELETE_WINDOW protocol to handle X button")
        print("✅ Added safe window destruction in show() method")
        print("✅ Added error handling in main.py")
        return True
    else:
        print("❌ Some tests failed")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
