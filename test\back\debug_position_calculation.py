#!/usr/bin/env python3
"""
调试位置计算问题
检查参数获取和位置计算是否正确

问题: x=79,y=0应该从15365开始，但实际从15366开始
需要验证: TSK处理器参数获取和位置计算逻辑

Author: Yuribytes
Company: Chipone TE development Team
"""

import os
import sys

# 添加父目录到路径
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, parent_dir)

from tsk_map_processor import TSKMapProcessor
from nepes_enhanced_processor import NEPESEnhancedProcessor

def debug_tsk_processor_parameters():
    """调试TSK处理器参数获取"""
    print("🔍 调试TSK处理器参数获取")
    print("=" * 50)
    
    dummy_file = "009.NNS157-09-E4"
    
    # 直接使用TSK处理器
    tsk_processor = TSKMapProcessor()
    success = tsk_processor.read_file(dummy_file)
    
    if not success:
        print("❌ TSK处理器文件读取失败")
        return False
    
    header_success = tsk_processor.parse_file_header()
    if not header_success:
        print("❌ TSK处理器头部解析失败")
        return False
    
    print("📊 TSK处理器参数:")
    print(f"   文件大小: {len(tsk_processor.filearray)} bytes")
    print(f"   May_version: {tsk_processor.May_version}")
    print(f"   columnsize: {tsk_processor.columnsize}")
    print(f"   rowsize: {tsk_processor.rowsize}")
    print(f"   RefdieX: {tsk_processor.RefdieX}")
    print(f"   RefdieY: {tsk_processor.RefdieY}")
    print(f"   TestResultStartPos: {tsk_processor.TestResultStartPos}")
    print(f"   TestResultCategory: {tsk_processor.TestResultCategory}")
    
    # 验证参数获取的字节位置
    print(f"\n🔍 参数获取字节位置验证:")
    print(f"   May_version (byte 52): {tsk_processor.filearray[52]} → {tsk_processor.May_version}")
    print(f"   columnsize (bytes 53-54): {tsk_processor.filearray[53:55]} → {tsk_processor.columnsize}")
    print(f"   rowsize (bytes 55-56): {tsk_processor.filearray[55:57]} → {tsk_processor.rowsize}")
    print(f"   TestResultStartPos (bytes 217-220): {tsk_processor.filearray[217:221]} → {tsk_processor.TestResultStartPos}")
    
    return tsk_processor

def debug_nepes_processor_parameters():
    """调试NEPES处理器参数获取"""
    print(f"\n🔍 调试NEPES处理器参数获取")
    print("=" * 50)
    
    dummy_file = "009.NNS157-09-E4"
    
    # 创建NEPES处理器并加载dummy map
    nepes_processor = NEPESEnhancedProcessor()
    
    # 手动执行加载步骤
    nepes_processor.tsk_processor = TSKMapProcessor()
    success = nepes_processor.tsk_processor.read_file(dummy_file)
    
    if not success:
        print("❌ NEPES处理器文件读取失败")
        return False
    
    header_success = nepes_processor.tsk_processor.parse_file_header()
    if not header_success:
        print("❌ NEPES处理器头部解析失败")
        return False
    
    # 提取参数 (复制自process_nepes_enhanced方法)
    nepes_processor.columnsize = nepes_processor.tsk_processor.columnsize
    nepes_processor.rowsize = nepes_processor.tsk_processor.rowsize
    nepes_processor.map_version = nepes_processor.tsk_processor.May_version
    nepes_processor.TestResultStartPos = nepes_processor.tsk_processor.TestResultStartPos
    nepes_processor.TestResultCategory = nepes_processor.tsk_processor.TestResultCategory
    nepes_processor.total_dies = nepes_processor.columnsize * nepes_processor.rowsize
    
    print("📊 NEPES处理器参数:")
    print(f"   columnsize: {nepes_processor.columnsize}")
    print(f"   rowsize: {nepes_processor.rowsize}")
    print(f"   map_version: {nepes_processor.map_version}")
    print(f"   TestResultStartPos: {nepes_processor.TestResultStartPos}")
    print(f"   TestResultCategory: {nepes_processor.TestResultCategory}")
    print(f"   total_dies: {nepes_processor.total_dies}")
    
    return nepes_processor

def debug_position_calculation():
    """调试位置计算逻辑"""
    print(f"\n🎯 调试位置计算逻辑")
    print("=" * 50)
    
    # 获取NEPES处理器
    nepes_processor = debug_nepes_processor_parameters()
    if not nepes_processor:
        return False
    
    # 测试关键位置
    test_positions = [
        {"x": 0, "y": 0, "expected_pos": 15049, "description": "x=0,y=0"},
        {"x": 79, "y": 0, "expected_pos": 15365, "description": "x=79,y=0 (关键位置)"},
        {"x": 149, "y": 2, "expected_pos": 18085, "description": "x=149,y=2"}
    ]
    
    print("📊 位置计算验证:")
    for pos_info in test_positions:
        x, y = pos_info["x"], pos_info["y"]
        expected_pos = pos_info["expected_pos"]
        
        # 使用NEPES处理器的计算方法
        die_index = y * nepes_processor.columnsize + x
        calculated_pos = nepes_processor.TestResultCategory + die_index * 4
        
        # 手动验证计算
        manual_calculation = f"{nepes_processor.TestResultCategory} + {die_index} * 4 = {calculated_pos}"
        
        print(f"\n   {pos_info['description']}:")
        print(f"     die_index: {die_index}")
        print(f"     计算: {manual_calculation}")
        print(f"     计算位置: {calculated_pos}-{calculated_pos+3}")
        print(f"     预期位置: {expected_pos}-{expected_pos+3}")
        print(f"     位置匹配: {'✅' if calculated_pos == expected_pos else '❌'}")
        
        if calculated_pos != expected_pos:
            offset = calculated_pos - expected_pos
            print(f"     偏移量: {offset} bytes")
            
            # 分析可能的原因
            if offset == 1:
                print(f"     可能原因: TestResultCategory计算多了1")
            elif offset == -1:
                print(f"     可能原因: TestResultCategory计算少了1")
            else:
                print(f"     可能原因: 参数获取或计算逻辑错误")
    
    return True

def debug_testresultcategory_calculation():
    """调试TestResultCategory计算逻辑"""
    print(f"\n🔧 调试TestResultCategory计算逻辑")
    print("=" * 50)
    
    # 获取TSK处理器
    tsk_processor = debug_tsk_processor_parameters()
    if not tsk_processor:
        return False
    
    # 查看TestResultCategory的计算过程
    print("📊 TestResultCategory计算分析:")
    print(f"   TestResultStartPos: {tsk_processor.TestResultStartPos}")
    print(f"   columnsize: {tsk_processor.columnsize}")
    print(f"   rowsize: {tsk_processor.rowsize}")
    
    # 根据tsk_map_processor.py中的计算公式
    # TestResultCategory = TestResultStartPos + 1 + rowsize * columnsize * 6 + 172
    calculated_category = (tsk_processor.TestResultStartPos + 1 + 
                          tsk_processor.rowsize * tsk_processor.columnsize * 6 + 172)
    
    print(f"\n   计算公式: TestResultStartPos + 1 + rowsize * columnsize * 6 + 172")
    print(f"   计算过程: {tsk_processor.TestResultStartPos} + 1 + {tsk_processor.rowsize} * {tsk_processor.columnsize} * 6 + 172")
    print(f"   计算过程: {tsk_processor.TestResultStartPos} + 1 + {tsk_processor.rowsize * tsk_processor.columnsize * 6} + 172")
    print(f"   计算结果: {calculated_category}")
    print(f"   实际值: {tsk_processor.TestResultCategory}")
    print(f"   匹配: {'✅' if calculated_category == tsk_processor.TestResultCategory else '❌'}")
    
    if calculated_category != tsk_processor.TestResultCategory:
        offset = tsk_processor.TestResultCategory - calculated_category
        print(f"   偏移量: {offset}")
        
        # 检查是否是之前修复的字节索引问题
        if offset == -1:
            print(f"   ⚠️  可能是之前修复的字节索引问题被还原了")
            print(f"   建议检查tsk_map_processor.py中的TestResultCategory计算")
    
    return True

def main():
    """主调试函数"""
    print("🧪 位置计算问题调试")
    print("问题: x=79,y=0应该从15365开始，但实际从15366开始")
    print("=" * 70)
    
    # 切换到测试目录
    os.chdir(os.path.dirname(os.path.abspath(__file__)))
    
    try:
        # 调试1: TSK处理器参数
        tsk_result = debug_tsk_processor_parameters()
        
        # 调试2: NEPES处理器参数
        nepes_result = debug_nepes_processor_parameters()
        
        # 调试3: 位置计算
        pos_result = debug_position_calculation()
        
        # 调试4: TestResultCategory计算
        category_result = debug_testresultcategory_calculation()
        
        print("\n" + "=" * 70)
        print("🎉 调试结果总结:")
        print(f"   TSK处理器参数: {'✅ 正常' if tsk_result else '❌ 异常'}")
        print(f"   NEPES处理器参数: {'✅ 正常' if nepes_result else '❌ 异常'}")
        print(f"   位置计算: {'✅ 正常' if pos_result else '❌ 异常'}")
        print(f"   TestResultCategory: {'✅ 正常' if category_result else '❌ 异常'}")
        
        if all([tsk_result, nepes_result, pos_result, category_result]):
            print(f"\n🎯 调试完成，请检查上述输出找出位置偏移的原因")
        else:
            print(f"\n❌ 发现问题，请根据上述分析进行修复")
            
        return True
            
    except Exception as e:
        print(f"❌ 调试过程出错: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
