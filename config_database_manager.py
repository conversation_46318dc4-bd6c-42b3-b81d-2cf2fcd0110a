#!/usr/bin/env python3
"""
Configuration Database Manager
Provides tools for managing test configurations in database
Includes web interface for easy configuration management
"""

import sqlite3
import os
import json
from typing import Dict, List, Optional
from datetime import datetime
import argparse


class ConfigDatabaseManager:
    """Manager for configuration database operations"""
    
    def __init__(self, db_path: str = "config_database.db"):
        self.db_path = db_path
        self.connection = sqlite3.connect(db_path)
        self.connection.row_factory = sqlite3.Row
    
    def create_config(self, config_data: Dict) -> int:
        """Create a new configuration"""
        cursor = self.connection.cursor()
        
        try:
            # Insert main configuration
            cursor.execute('''
                INSERT INTO test_configurations 
                (config_name, test_program_name, device_name, vendor_name, 
                 tester_name, probe_card_no, test_flow, wafer_size, operator_name, version)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                config_data.get('config_name'),
                config_data.get('test_program_name', ''),
                config_data.get('device_name', ''),
                config_data.get('vendor_name', ''),
                config_data.get('tester_name', ''),
                config_data.get('probe_card_no', ''),
                config_data.get('test_flow', ''),
                config_data.get('wafer_size', ''),
                config_data.get('operator_name', ''),
                config_data.get('version', '1.0')
            ))
            
            config_id = cursor.lastrowid
            
            # Insert bin mappings if provided
            bin_mappings = config_data.get('bin_mappings', {})
            for bin_number, bin_name in bin_mappings.items():
                cursor.execute('''
                    INSERT INTO bin_mappings (config_id, bin_number, bin_name)
                    VALUES (?, ?, ?)
                ''', (config_id, int(bin_number), bin_name))
            
            self.connection.commit()
            print(f"Created configuration '{config_data.get('config_name')}' with ID {config_id}")
            return config_id
            
        except Exception as e:
            self.connection.rollback()
            print(f"Error creating configuration: {e}")
            return -1
    
    def update_config(self, config_id: int, config_data: Dict) -> bool:
        """Update existing configuration"""
        cursor = self.connection.cursor()
        
        try:
            # Update main configuration
            cursor.execute('''
                UPDATE test_configurations SET
                    test_program_name = ?, device_name = ?, vendor_name = ?,
                    tester_name = ?, probe_card_no = ?, test_flow = ?,
                    wafer_size = ?, operator_name = ?, version = ?,
                    updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            ''', (
                config_data.get('test_program_name', ''),
                config_data.get('device_name', ''),
                config_data.get('vendor_name', ''),
                config_data.get('tester_name', ''),
                config_data.get('probe_card_no', ''),
                config_data.get('test_flow', ''),
                config_data.get('wafer_size', ''),
                config_data.get('operator_name', ''),
                config_data.get('version', '1.0'),
                config_id
            ))
            
            # Update bin mappings if provided
            if 'bin_mappings' in config_data:
                # Delete existing mappings
                cursor.execute('DELETE FROM bin_mappings WHERE config_id = ?', (config_id,))
                
                # Insert new mappings
                bin_mappings = config_data['bin_mappings']
                for bin_number, bin_name in bin_mappings.items():
                    cursor.execute('''
                        INSERT INTO bin_mappings (config_id, bin_number, bin_name)
                        VALUES (?, ?, ?)
                    ''', (config_id, int(bin_number), bin_name))
            
            self.connection.commit()
            print(f"Updated configuration ID {config_id}")
            return True
            
        except Exception as e:
            self.connection.rollback()
            print(f"Error updating configuration: {e}")
            return False
    
    def delete_config(self, config_id: int) -> bool:
        """Soft delete configuration (mark as inactive)"""
        try:
            cursor = self.connection.cursor()
            cursor.execute('''
                UPDATE test_configurations SET is_active = 0 
                WHERE id = ?
            ''', (config_id,))
            
            cursor.execute('''
                UPDATE bin_mappings SET is_active = 0 
                WHERE config_id = ?
            ''', (config_id,))
            
            self.connection.commit()
            print(f"Deleted configuration ID {config_id}")
            return True
            
        except Exception as e:
            print(f"Error deleting configuration: {e}")
            return False
    
    def export_config_to_json(self, config_id: int, output_file: str) -> bool:
        """Export configuration to JSON file"""
        try:
            cursor = self.connection.cursor()
            
            # Get main configuration
            cursor.execute('SELECT * FROM test_configurations WHERE id = ?', (config_id,))
            config_row = cursor.fetchone()
            
            if not config_row:
                print(f"Configuration ID {config_id} not found")
                return False
            
            # Get bin mappings
            cursor.execute('''
                SELECT bin_number, bin_name FROM bin_mappings 
                WHERE config_id = ? AND is_active = 1
            ''', (config_id,))
            
            bin_mappings = {}
            for row in cursor.fetchall():
                bin_mappings[row['bin_number']] = row['bin_name']
            
            # Build export data
            export_data = {
                'config_name': config_row['config_name'],
                'test_program_name': config_row['test_program_name'],
                'device_name': config_row['device_name'],
                'vendor_name': config_row['vendor_name'],
                'tester_name': config_row['tester_name'],
                'probe_card_no': config_row['probe_card_no'],
                'test_flow': config_row['test_flow'],
                'wafer_size': config_row['wafer_size'],
                'operator_name': config_row['operator_name'],
                'version': config_row['version'],
                'bin_mappings': bin_mappings,
                'exported_at': datetime.now().isoformat(),
                'exported_from_id': config_id
            }
            
            # Write to JSON file
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, indent=2, ensure_ascii=False)
            
            print(f"Exported configuration to {output_file}")
            return True
            
        except Exception as e:
            print(f"Error exporting configuration: {e}")
            return False
    
    def import_config_from_json(self, json_file: str) -> int:
        """Import configuration from JSON file"""
        try:
            with open(json_file, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
            
            # Ensure unique config name
            original_name = config_data.get('config_name', 'Imported_Config')
            config_name = original_name
            counter = 1
            
            cursor = self.connection.cursor()
            while True:
                cursor.execute('SELECT id FROM test_configurations WHERE config_name = ?', (config_name,))
                if not cursor.fetchone():
                    break
                config_name = f"{original_name}_{counter}"
                counter += 1
            
            config_data['config_name'] = config_name
            return self.create_config(config_data)
            
        except Exception as e:
            print(f"Error importing configuration: {e}")
            return -1
    
    def list_configs_detailed(self) -> List[Dict]:
        """Get detailed list of all configurations"""
        cursor = self.connection.cursor()
        cursor.execute('''
            SELECT tc.*, 
                   COUNT(bm.id) as bin_count,
                   (SELECT COUNT(*) FROM config_usage_log WHERE config_id = tc.id) as usage_count
            FROM test_configurations tc
            LEFT JOIN bin_mappings bm ON tc.id = bm.config_id AND bm.is_active = 1
            WHERE tc.is_active = 1
            GROUP BY tc.id
            ORDER BY tc.updated_at DESC
        ''')
        
        configs = []
        for row in cursor.fetchall():
            configs.append({
                'id': row['id'],
                'config_name': row['config_name'],
                'test_program_name': row['test_program_name'],
                'device_name': row['device_name'],
                'vendor_name': row['vendor_name'],
                'test_flow': row['test_flow'],
                'version': row['version'],
                'bin_count': row['bin_count'],
                'usage_count': row['usage_count'],
                'created_at': row['created_at'],
                'updated_at': row['updated_at']
            })
        
        return configs
    
    def get_usage_statistics(self) -> Dict:
        """Get usage statistics for configurations"""
        cursor = self.connection.cursor()
        
        # Most used configurations
        cursor.execute('''
            SELECT tc.config_name, COUNT(cul.id) as usage_count
            FROM test_configurations tc
            LEFT JOIN config_usage_log cul ON tc.id = cul.config_id
            WHERE tc.is_active = 1
            GROUP BY tc.id
            ORDER BY usage_count DESC
            LIMIT 10
        ''')
        most_used = [{'config_name': row[0], 'usage_count': row[1]} for row in cursor.fetchall()]
        
        # Recent usage
        cursor.execute('''
            SELECT tc.config_name, cul.used_at, cul.usage_context
            FROM config_usage_log cul
            JOIN test_configurations tc ON cul.config_id = tc.id
            WHERE tc.is_active = 1
            ORDER BY cul.used_at DESC
            LIMIT 20
        ''')
        recent_usage = [{'config_name': row[0], 'used_at': row[1], 'context': row[2]} for row in cursor.fetchall()]
        
        return {
            'most_used': most_used,
            'recent_usage': recent_usage
        }
    
    def close(self):
        """Close database connection"""
        if self.connection:
            self.connection.close()


def main():
    """Command line interface for database management"""
    parser = argparse.ArgumentParser(description='Configuration Database Manager')
    parser.add_argument('--db', default='config_database.db', help='Database file path')
    
    subparsers = parser.add_subparsers(dest='command', help='Available commands')
    
    # List command
    list_parser = subparsers.add_parser('list', help='List all configurations')
    
    # Export command
    export_parser = subparsers.add_parser('export', help='Export configuration to JSON')
    export_parser.add_argument('config_id', type=int, help='Configuration ID to export')
    export_parser.add_argument('output_file', help='Output JSON file path')
    
    # Import command
    import_parser = subparsers.add_parser('import', help='Import configuration from JSON')
    import_parser.add_argument('json_file', help='JSON file to import')
    
    # Migrate command
    migrate_parser = subparsers.add_parser('migrate', help='Migrate Excel file to database')
    migrate_parser.add_argument('excel_file', help='Excel file to migrate')
    migrate_parser.add_argument('config_name', help='Name for the configuration')
    
    # Stats command
    stats_parser = subparsers.add_parser('stats', help='Show usage statistics')
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    manager = ConfigDatabaseManager(args.db)
    
    try:
        if args.command == 'list':
            configs = manager.list_configs_detailed()
            print(f"\nFound {len(configs)} configurations:")
            print("-" * 80)
            for config in configs:
                print(f"ID: {config['id']:3d} | {config['config_name']:20s} | {config['device_name']:15s} | "
                      f"Flow: {config['test_flow']:5s} | Bins: {config['bin_count']:3d} | "
                      f"Used: {config['usage_count']:3d} times")
        
        elif args.command == 'export':
            manager.export_config_to_json(args.config_id, args.output_file)
        
        elif args.command == 'import':
            config_id = manager.import_config_from_json(args.json_file)
            if config_id > 0:
                print(f"Successfully imported configuration with ID {config_id}")
        
        elif args.command == 'migrate':
            from database_config_reader import DatabaseConfigReader
            db_reader = DatabaseConfigReader(args.db)
            if db_reader.migrate_from_excel(args.excel_file, args.config_name):
                print("Migration completed successfully")
            else:
                print("Migration failed")
        
        elif args.command == 'stats':
            stats = manager.get_usage_statistics()
            print("\nMost Used Configurations:")
            for item in stats['most_used'][:5]:
                print(f"  {item['config_name']}: {item['usage_count']} times")
            
            print("\nRecent Usage:")
            for item in stats['recent_usage'][:10]:
                print(f"  {item['config_name']} - {item['used_at']} ({item['context']})")
    
    finally:
        manager.close()


if __name__ == "__main__":
    main()
