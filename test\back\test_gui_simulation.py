#!/usr/bin/env python3
"""
Test GUI Simulation - Simulate GUI operations programmatically
"""

import sys
import os
import tkinter as tk
sys.path.append('..')
from tsk_map_gui import TSKMapGUI


def simulate_gui_workflow(tsk_filepath):
    """Simulate the complete GUI workflow"""
    print(f"Simulating GUI Workflow")
    print("=" * 50)
    
    config_path = "../3509_CP1_program_bin.xlsx"
    
    if not os.path.exists(tsk_filepath):
        print(f"❌ TSK file not found: {tsk_filepath}")
        return False
    
    if not os.path.exists(config_path):
        print(f"❌ Config file not found: {config_path}")
        return False
    
    try:
        # Create a root window (but don't show it)
        root = tk.Tk()
        root.withdraw()  # Hide the main window
        
        # Create GUI instance
        gui = TSKMapGUI(root)
        print(f"✅ GUI instance created")
        
        # Step 1: Set configuration file path
        print(f"\nStep 1: Setting configuration file")
        gui.config_file_path.set(config_path)
        print(f"  Config file set: {os.path.basename(config_path)}")
        
        # Step 2: Set TSK/MAP file path
        print(f"\nStep 2: Setting TSK/MAP file")
        gui.file_path.set(tsk_filepath)
        print(f"  TSK file set: {os.path.basename(tsk_filepath)}")
        
        # Step 3: Update file info (this should work now)
        print(f"\nStep 3: Updating file information")
        gui.update_file_info()
        
        # Check if info was updated
        info_content = gui.info_text.get(1.0, tk.END).strip()
        if info_content and "Error:" not in info_content:
            print(f"  ✅ File info updated successfully")
            lines = info_content.split('\n')[:3]
            for line in lines:
                print(f"    {line}")
        else:
            print(f"  ❌ File info update failed: {info_content}")
            return False
        
        # Step 4: Simulate read file operation
        print(f"\nStep 4: Simulating read file operation")
        
        # This should now work without the 'NoneType' error
        try:
            gui.read_file()
            print(f"  ✅ Read file operation completed successfully")
            
            # Check if processor was created
            if gui.processor is not None:
                print(f"  ✅ Processor created successfully")
                
                # Check if config reader was loaded
                if gui.config_reader is not None:
                    print(f"  ✅ Config reader loaded successfully")
                    print(f"    Test Program: {gui.config_reader.get_test_program_name()}")
                    print(f"    Bin Mappings: {len(gui.config_reader.get_all_bin_mappings())} entries")
                else:
                    print(f"  ⚠️  Config reader not loaded")
                
                # Check basic statistics
                stats = gui.processor.get_test_statistics()
                print(f"  Statistics:")
                print(f"    Total Tested: {stats['total_tested']}")
                print(f"    Pass Count: {stats['pass_count']}")
                print(f"    Yield: {stats['yield_percentage']:.2f}%")
                
            else:
                print(f"  ❌ Processor not created")
                return False
                
        except Exception as e:
            print(f"  ❌ Read file operation failed: {e}")
            import traceback
            traceback.print_exc()
            return False
        
        # Clean up
        root.destroy()
        
        return True
        
    except Exception as e:
        print(f"❌ Error in GUI simulation: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Main test function"""
    if len(sys.argv) < 2:
        print("GUI Simulation Test")
        print("Usage: python test_gui_simulation.py <tsk_file_path>")
        print("Example: python test_gui_simulation.py ../3AA111-01-B4")
        return
    
    tsk_filepath = sys.argv[1]
    
    print("TSK/MAP GUI Simulation Test")
    print("Testing GUI workflow programmatically")
    print("=" * 70)
    
    if simulate_gui_workflow(tsk_filepath):
        print("\n" + "=" * 70)
        print("🎉 GUI Simulation Test Completed!")
        print("\nFixed Issues:")
        print("✅ 'NoneType' object has no attribute 'read_file' error resolved")
        print("✅ Processor initialization in read_file() function")
        print("✅ Configuration file loading works correctly")
        print("✅ File information display works")
        print("✅ Complete workflow from config selection to processing")
        
        print(f"\nGUI Workflow Steps:")
        print(f"1. Select configuration Excel file (3509_CP1_program_bin.xlsx)")
        print(f"2. Select TSK/MAP file")
        print(f"3. File information displays correctly")
        print(f"4. Click 'Read File' to process")
        print(f"5. Excel output generated with enhanced bin names")
    else:
        print("\n❌ GUI simulation test failed")


if __name__ == "__main__":
    main()
