#!/usr/bin/env python3
"""
测试Map Version 4的正确处理逻辑
验证10-bit修改而不是32-bit修改

基于worker_file12.txt的具体要求:
- x=0,y=0: "__" → 不修改 (15049-15052保持原值)
- x=79,y=0: "00" → 修改10bit (15367的bit0,bit1 + 15368的8bit)
- x=149,y=2: "XX" → 修改10bit (18087的bit0,bit1 + 18088的8bit)

Author: Yuribytes
Company: Chipone TE development Team
"""

import os
import sys
import struct

# 添加父目录到路径
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, parent_dir)

from nepes_enhanced_processor import NEPESEnhancedProcessor
from tsk_map_processor import TSKMapProcessor

def analyze_specific_positions_before_after():
    """分析处理前后的具体位置变化"""
    print("🔍 分析Map Version 4处理前后的位置变化")
    print("=" * 60)
    
    bump_file = "D97127.09"
    dummy_file = "009.NNS157-09-E4"
    output_file = "map_version4_test_output.tsk"
    
    # 读取原始dummy map数据
    print("📖 读取原始dummy map数据...")
    with open(dummy_file, 'rb') as f:
        original_data = f.read()
    
    # 分析关键位置的原始数据
    test_positions = [
        {"x": 0, "y": 0, "expected_bump": "__", "description": "x=0,y=0 (__) - 应保持不变"},
        {"x": 79, "y": 0, "expected_bump": "00", "description": "x=79,y=0 (00) - 应修改10bit为Pass"},
        {"x": 149, "y": 2, "expected_bump": "XX", "description": "x=149,y=2 (XX) - 应修改10bit为Fail"}
    ]
    
    # 计算位置
    columnsize = 305
    TestResultCategory = 15049
    
    print(f"📊 原始数据分析:")
    for pos_info in test_positions:
        x, y = pos_info["x"], pos_info["y"]
        die_index = y * columnsize + x
        category_pos = TestResultCategory + die_index * 4
        
        # 读取原始4字节数据
        original_bytes = original_data[category_pos:category_pos+4]
        
        print(f"\n   {pos_info['description']}:")
        print(f"     位置: {category_pos}-{category_pos+3} (die_index={die_index})")
        print(f"     原始数据: {original_bytes.hex()} ({[hex(b) for b in original_bytes]})")
        print(f"     原始byte3: 0x{original_bytes[2]:02x} (bit0={original_bytes[2]&1}, bit1={(original_bytes[2]>>1)&1})")
        print(f"     原始byte4: 0x{original_bytes[3]:02x}")
    
    # 执行处理
    print(f"\n🚀 执行NEPES Enhanced处理...")
    processor = NEPESEnhancedProcessor()
    success = processor.process_nepes_enhanced(bump_file, dummy_file, output_file)
    
    if not success:
        print("❌ 处理失败")
        return False
    
    # 读取处理后的数据
    print(f"📖 读取处理后数据...")
    with open(output_file, 'rb') as f:
        processed_data = f.read()
    
    print(f"\n📊 处理后数据分析:")
    for pos_info in test_positions:
        x, y = pos_info["x"], pos_info["y"]
        die_index = y * columnsize + x
        category_pos = TestResultCategory + die_index * 4
        
        # 读取处理后4字节数据
        original_bytes = original_data[category_pos:category_pos+4]
        processed_bytes = processed_data[category_pos:category_pos+4]
        
        print(f"\n   {pos_info['description']}:")
        print(f"     原始数据: {original_bytes.hex()}")
        print(f"     处理后数据: {processed_bytes.hex()}")
        
        # 分析变化
        if original_bytes == processed_bytes:
            print(f"     ✅ 数据未变化 (符合'__'位置要求)")
        else:
            # 分析具体变化
            orig_byte3, orig_byte4 = original_bytes[2], original_bytes[3]
            proc_byte3, proc_byte4 = processed_bytes[2], processed_bytes[3]
            
            print(f"     🔄 数据已变化:")
            print(f"       Byte3: 0x{orig_byte3:02x} → 0x{proc_byte3:02x}")
            print(f"       Byte4: 0x{orig_byte4:02x} → 0x{proc_byte4:02x}")
            
            # 验证Map Version 4的10-bit修改
            if pos_info["expected_bump"] == "00":
                # Pass: 应该是10bit全1 (0x3FF)
                expected_byte3_bits = orig_byte3 | 0x03  # Set bit0,bit1
                expected_byte4 = 0xFF
                
                if proc_byte3 == expected_byte3_bits and proc_byte4 == expected_byte4:
                    print(f"       ✅ Map Version 4 Pass修改正确 (10bit设为1)")
                else:
                    print(f"       ❌ Map Version 4 Pass修改错误")
                    print(f"          预期byte3: 0x{expected_byte3_bits:02x}, 实际: 0x{proc_byte3:02x}")
                    print(f"          预期byte4: 0x{expected_byte4:02x}, 实际: 0x{proc_byte4:02x}")
                    
            elif pos_info["expected_bump"] in ["XX", "0C"]:
                # Fail: 应该是10bit为1111111011 (0x3FB)
                expected_byte3_bits = orig_byte3 | 0x03  # Set bit0,bit1
                expected_byte4 = 0xFB
                
                if proc_byte3 == expected_byte3_bits and proc_byte4 == expected_byte4:
                    print(f"       ✅ Map Version 4 Fail修改正确 (10bit设为1111111011)")
                else:
                    print(f"       ❌ Map Version 4 Fail修改错误")
                    print(f"          预期byte3: 0x{expected_byte3_bits:02x}, 实际: 0x{proc_byte3:02x}")
                    print(f"          预期byte4: 0x{expected_byte4:02x}, 实际: 0x{proc_byte4:02x}")
    
    return True

def verify_10bit_modification():
    """验证确实只修改了10bit而不是32bit"""
    print(f"\n🔍 验证10bit修改逻辑")
    print("=" * 40)
    
    dummy_file = "009.NNS157-09-E4"
    output_file = "map_version4_test_output.tsk"
    
    # 读取原始和处理后的数据
    with open(dummy_file, 'rb') as f:
        original_data = f.read()
    
    with open(output_file, 'rb') as f:
        processed_data = f.read()
    
    # 统计修改的字节数
    total_bytes = len(original_data)
    changed_bytes = 0
    unchanged_bytes = 0
    
    for i in range(total_bytes):
        if original_data[i] != processed_data[i]:
            changed_bytes += 1
        else:
            unchanged_bytes += 1
    
    print(f"📊 字节修改统计:")
    print(f"   总字节数: {total_bytes}")
    print(f"   修改字节数: {changed_bytes}")
    print(f"   未修改字节数: {unchanged_bytes}")
    print(f"   修改比例: {(changed_bytes/total_bytes*100):.2f}%")
    
    # 对于Map Version 4，应该只修改特定位置的特定字节
    # 预期修改: 1940个"00"位置 × 2字节 + 2个"XX"位置 × 2字节 = 3884字节
    expected_changes = 1940 * 2 + 2 * 2  # 基于之前的统计
    
    print(f"\n🎯 Map Version 4验证:")
    print(f"   预期修改字节数: ~{expected_changes} (仅10bit相关字节)")
    print(f"   实际修改字节数: {changed_bytes}")
    
    if changed_bytes < total_bytes * 0.2:  # 少于20%的字节被修改
        print(f"   ✅ 修改范围合理 (Map Version 4只修改10bit)")
    else:
        print(f"   ❌ 修改范围过大 (可能仍在修改32bit)")
    
    return changed_bytes < total_bytes * 0.2

def main():
    """主测试函数"""
    print("🧪 Map Version 4处理逻辑测试")
    print("基于worker_file12.txt的具体要求")
    print("=" * 70)
    
    # 切换到测试目录
    os.chdir(os.path.dirname(os.path.abspath(__file__)))
    
    try:
        # 测试1: 分析处理前后的具体位置
        result1 = analyze_specific_positions_before_after()
        
        # 测试2: 验证10bit修改逻辑
        result2 = verify_10bit_modification()
        
        print("\n" + "=" * 70)
        print("🎉 测试结果总结:")
        print(f"   具体位置分析: {'✅ 通过' if result1 else '❌ 失败'}")
        print(f"   10bit修改验证: {'✅ 通过' if result2 else '❌ 失败'}")
        
        if result1 and result2:
            print(f"\n🎯 Map Version 4处理逻辑验证成功!")
            print(f"✅ '__'位置保持不变")
            print(f"✅ '00'位置正确修改10bit为Pass")
            print(f"✅ 'XX'位置正确修改10bit为Fail")
            print(f"✅ 只修改必要的字节，不影响其他数据")
            return True
        else:
            print(f"\n❌ Map Version 4处理逻辑需要进一步调整")
            return False
            
    except Exception as e:
        print(f"❌ 测试过程出错: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
