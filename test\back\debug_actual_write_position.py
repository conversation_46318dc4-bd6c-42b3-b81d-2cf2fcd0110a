#!/usr/bin/env python3
"""
调试实际文件写入位置
检查NEPES处理器实际写入的位置是否正确

问题: x=79,y=0应该从15365开始，但可能实际从15366开始写入
需要验证: 实际的文件修改位置

Author: Yuribytes
Company: Chipone TE development Team
"""

import os
import sys

# 添加父目录到路径
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, parent_dir)

from nepes_enhanced_processor import NEPESEnhancedProcessor

def analyze_actual_file_changes():
    """分析实际的文件修改位置"""
    print("🔍 分析实际文件修改位置")
    print("=" * 50)
    
    bump_file = "D97127.09"
    dummy_file = "009.NNS157-09-E4"
    output_file = "debug_write_position.tsk"
    
    # 读取原始文件
    with open(dummy_file, 'rb') as f:
        original_data = bytearray(f.read())
    
    print(f"📖 原始文件大小: {len(original_data)} bytes")
    
    # 检查关键位置的原始数据
    key_positions = [
        {"name": "x=0,y=0", "start": 15049, "end": 15052},
        {"name": "x=79,y=0", "start": 15365, "end": 15368},
        {"name": "x=149,y=2", "start": 18085, "end": 18088}
    ]
    
    print(f"\n📊 关键位置原始数据:")
    for pos in key_positions:
        data = original_data[pos["start"]:pos["end"]+1]
        print(f"   {pos['name']}: 位置{pos['start']}-{pos['end']} = {data.hex().upper()}")
    
    # 执行NEPES处理
    print(f"\n🚀 执行NEPES Enhanced处理...")
    processor = NEPESEnhancedProcessor()
    success = processor.process_nepes_enhanced(bump_file, dummy_file, output_file)
    
    if not success:
        print("❌ 处理失败")
        return False
    
    # 读取处理后的文件
    with open(output_file, 'rb') as f:
        processed_data = bytearray(f.read())
    
    print(f"📖 处理后文件大小: {len(processed_data)} bytes")
    
    # 比较文件变化
    print(f"\n📊 文件变化分析:")
    changes_found = []
    
    for i in range(len(original_data)):
        if original_data[i] != processed_data[i]:
            changes_found.append({
                "position": i,
                "original": original_data[i],
                "modified": processed_data[i]
            })
    
    print(f"   总变化字节数: {len(changes_found)}")
    
    # 分析关键位置的变化
    print(f"\n📊 关键位置变化分析:")
    for pos in key_positions:
        original_bytes = original_data[pos["start"]:pos["end"]+1]
        processed_bytes = processed_data[pos["start"]:pos["end"]+1]
        
        print(f"\n   {pos['name']} (位置{pos['start']}-{pos['end']}):")
        print(f"     原始: {original_bytes.hex().upper()}")
        print(f"     处理后: {processed_bytes.hex().upper()}")
        print(f"     是否变化: {'是' if original_bytes != processed_bytes else '否'}")
        
        # 检查周围位置是否有变化
        extended_start = max(0, pos["start"] - 2)
        extended_end = min(len(original_data) - 1, pos["end"] + 2)
        
        extended_original = original_data[extended_start:extended_end+1]
        extended_processed = processed_data[extended_start:extended_end+1]
        
        print(f"     扩展范围({extended_start}-{extended_end}):")
        print(f"       原始: {extended_original.hex().upper()}")
        print(f"       处理后: {extended_processed.hex().upper()}")
        
        # 找出具体变化的字节
        for j in range(len(extended_original)):
            if extended_original[j] != extended_processed[j]:
                actual_pos = extended_start + j
                print(f"       变化位置: {actual_pos} ({original_data[actual_pos]:02X} → {processed_data[actual_pos]:02X})")
    
    # 分析x=79,y=0的具体情况
    print(f"\n🎯 x=79,y=0详细分析:")
    target_start = 15365
    target_end = 15368
    
    # 检查更大范围
    check_start = target_start - 5
    check_end = target_start + 10
    
    print(f"   检查范围: {check_start}-{check_end}")
    
    for pos in range(check_start, check_end + 1):
        if pos < len(original_data):
            orig_byte = original_data[pos]
            proc_byte = processed_data[pos]
            
            status = "🔄 变化" if orig_byte != proc_byte else "   不变"
            marker = " ← 目标位置" if target_start <= pos <= target_end else ""
            
            print(f"     位置{pos}: {orig_byte:02X} → {proc_byte:02X} {status}{marker}")
    
    return True

def trace_nepes_processing():
    """跟踪NEPES处理过程"""
    print(f"\n🔍 跟踪NEPES处理过程")
    print("=" * 50)
    
    # 创建一个修改版的NEPES处理器来跟踪写入
    class TracingNEPESProcessor(NEPESEnhancedProcessor):
        def __init__(self):
            super().__init__()
            self.write_log = []
        
        def get_binary_modification_pattern(self, bump_value: str):
            """重写以记录调用"""
            result = super().get_binary_modification_pattern(bump_value)
            if result is not None:
                self.write_log.append(f"get_binary_modification_pattern({bump_value}) → {result.hex().upper()}")
            return result
        
        def apply_enhanced_conversion(self):
            """重写以记录写入位置"""
            print(f"🔄 开始apply_enhanced_conversion...")
            
            for y in range(self.rowsize):
                for x in range(self.columnsize):
                    if y * self.columnsize + x >= len(self.bump_data):
                        break
                    
                    bump_value = self.bump_data[y * self.columnsize + x]
                    
                    # 计算位置
                    die_index = y * self.columnsize + x
                    category_pos = self.TestResultCategory + die_index * 4
                    
                    # 只记录关键位置
                    if (x, y) in [(0, 0), (79, 0), (149, 2)]:
                        print(f"   处理位置 x={x}, y={y}: bump_value='{bump_value}', category_pos={category_pos}")
                        
                        if category_pos + 3 < len(self.tsk_processor.filearray):
                            original_bytes = self.tsk_processor.filearray[category_pos:category_pos+4]
                            print(f"     原始数据: {original_bytes.hex().upper()}")
                            
                            # 获取修改模式
                            binary_pattern = self.get_binary_modification_pattern(bump_value)
                            
                            if binary_pattern is not None:
                                print(f"     修改模式: {binary_pattern.hex().upper()}")
                                print(f"     写入位置: {category_pos}-{category_pos+3}")
                                
                                # 执行写入
                                for i in range(4):
                                    self.tsk_processor.filearray[category_pos + i] = binary_pattern[i]
                                
                                modified_bytes = self.tsk_processor.filearray[category_pos:category_pos+4]
                                print(f"     写入后数据: {modified_bytes.hex().upper()}")
                            else:
                                print(f"     无修改 ('{bump_value}')")
            
            return True
    
    # 使用跟踪处理器
    bump_file = "D97127.09"
    dummy_file = "009.NNS157-09-E4"
    output_file = "trace_processing.tsk"
    
    processor = TracingNEPESProcessor()
    success = processor.process_nepes_enhanced(bump_file, dummy_file, output_file)
    
    print(f"\n📊 跟踪结果:")
    print(f"   处理成功: {'是' if success else '否'}")
    
    return success

def main():
    """主调试函数"""
    print("🧪 实际文件写入位置调试")
    print("检查NEPES处理器实际写入的位置")
    print("=" * 70)
    
    # 切换到测试目录
    os.chdir(os.path.dirname(os.path.abspath(__file__)))
    
    try:
        # 分析1: 实际文件变化
        result1 = analyze_actual_file_changes()
        
        # 分析2: 跟踪处理过程
        result2 = trace_nepes_processing()
        
        print("\n" + "=" * 70)
        print("🎉 调试结果总结:")
        print(f"   文件变化分析: {'✅ 完成' if result1 else '❌ 失败'}")
        print(f"   处理过程跟踪: {'✅ 完成' if result2 else '❌ 失败'}")
        
        if result1 and result2:
            print(f"\n🎯 请检查上述输出，找出实际写入位置与预期的差异")
        else:
            print(f"\n❌ 调试过程中出现问题")
            
        return True
            
    except Exception as e:
        print(f"❌ 调试过程出错: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
