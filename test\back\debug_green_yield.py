#!/usr/bin/env python3
"""
Debug script to check the actual structure of the Bin_Summary sheet
and verify where yield values are located
"""

import os
import sys
import tempfile

# Add parent directory to path to import modules
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def debug_bin_summary_structure():
    """Debug the actual structure of the Bin_Summary sheet"""
    print("🔍 Debugging Bin_Summary Structure")
    print("=" * 50)
    
    try:
        from full_map_processor import FullMapProcessor
        from config_reader import ConfigReader
        from openpyxl import Workbook
        
        # Create test setup
        processor = FullMapProcessor()
        
        # Create mock config
        config_reader = ConfigReader()
        config_reader.device_name = "Debug_TestChip"
        config_reader.vendor_name = "Debug_Corp"
        processor.set_config_reader(config_reader)
        
        # Create test workbook
        workbook = Workbook()
        worksheet = workbook.active
        worksheet.title = "Bin_Summary"
        
        # Create mock processors
        mock_processors = {
            "DEBUG-W01.map": {
                "processor": MockTSKProcessor(2000, 1900),  # 95% yield
                "file_path": "DEBUG-W01.map"
            },
            "DEBUG-W02.map": {
                "processor": MockTSKProcessor(2100, 1680),  # 80% yield
                "file_path": "DEBUG-W02.map"
            }
        }
        
        print("1️⃣ Creating mock sheets for data rows...")
        # Create mock sheets to generate data rows
        for filename in mock_processors.keys():
            sheet_name = processor._sanitize_sheet_name(os.path.splitext(filename)[0])
            mock_sheet = workbook.create_sheet(title=sheet_name)
            mock_sheet['A1'] = f"Mock data for {sheet_name}"

        print("2️⃣ Creating Bin_Summary sheet...")
        processor._create_bin_summary_sheet(worksheet, mock_processors, workbook)
        
        print("3️⃣ Analyzing sheet structure...")
        max_row = worksheet.max_row
        max_col = worksheet.max_column
        
        print(f"   Sheet dimensions: {max_row} rows × {max_col} columns")
        
        # Check each row for content
        print("4️⃣ Row-by-row analysis:")
        for row in range(1, min(max_row + 1, 20)):  # Check first 20 rows
            row_content = []
            for col in range(1, min(max_col + 1, 15)):  # Check first 15 columns
                cell = worksheet.cell(row=row, column=col)
                if cell.value is not None:
                    # Check if it's a yield percentage
                    value_str = str(cell.value)
                    if '%' in value_str or (isinstance(cell.value, (int, float)) and col == 2 and row > 5):
                        font_color = "No color"
                        if cell.font.color and cell.font.color.rgb:
                            font_color = f"#{cell.font.color.rgb}"
                        row_content.append(f"{chr(64+col)}{row}:{value_str}({font_color})")
                    else:
                        row_content.append(f"{chr(64+col)}{row}:{value_str}")
            
            if row_content:
                print(f"   Row {row:2d}: {' | '.join(row_content[:8])}")  # Show first 8 cells
        
        # Specifically look for yield columns
        print("5️⃣ Looking for yield-related cells...")
        yield_cells_found = []
        
        for row in range(1, max_row + 1):
            for col in range(1, max_col + 1):
                cell = worksheet.cell(row=row, column=col)
                if cell.value is not None:
                    value_str = str(cell.value)
                    # Check if it looks like a yield percentage
                    if ('%' in value_str and any(char.isdigit() for char in value_str)) or \
                       (isinstance(cell.value, float) and 0 <= cell.value <= 1 and col == 2):
                        
                        font_color = "No color"
                        if cell.font.color and cell.font.color.rgb:
                            font_color = f"#{cell.font.color.rgb}"
                            is_green = '28A745' in font_color
                        else:
                            is_green = False
                        
                        yield_cells_found.append({
                            'location': f"{chr(64+col)}{row}",
                            'value': value_str,
                            'color': font_color,
                            'is_green': is_green
                        })
        
        print(f"   Found {len(yield_cells_found)} yield-related cells:")
        for cell_info in yield_cells_found:
            status = "✅" if cell_info['is_green'] else "❌"
            print(f"     {status} {cell_info['location']}: {cell_info['value']} (Color: {cell_info['color']})")
        
        # Save debug file
        debug_file = os.path.join(tempfile.gettempdir(), "Debug_Bin_Summary_Structure.xlsx")
        workbook.save(debug_file)
        workbook.close()
        
        print(f"\n6️⃣ Debug file saved: {debug_file}")
        print("   You can open this file to visually inspect the structure")
        
        return len(yield_cells_found) > 0
        
    except Exception as e:
        print(f"❌ Error during debugging: {e}")
        import traceback
        traceback.print_exc()
        return False


class MockTSKProcessor:
    """Mock TSKMapProcessor for debugging"""
    def __init__(self, total_tested, pass_count):
        self.total_tested = total_tested
        self.pass_count = pass_count
    
    def get_test_statistics(self):
        return {
            'total_tested': self.total_tested,
            'pass_count': self.pass_count,
            'yield_percentage': (self.pass_count / self.total_tested * 100) if self.total_tested > 0 else 0.0
        }


def main():
    """Run the debug analysis"""
    print("🔍 BIN_SUMMARY STRUCTURE DEBUG")
    print("=" * 60)
    
    success = debug_bin_summary_structure()
    
    print("\n" + "=" * 60)
    if success:
        print("✅ Debug analysis completed successfully!")
        print("Check the output above to understand the sheet structure")
    else:
        print("❌ Debug analysis failed")
    
    return success


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
