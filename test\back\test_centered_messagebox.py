#!/usr/bin/env python3
"""
Test script for centered messagebox functionality
"""

import tkinter as tk
from tkinter import ttk
import centered_messagebox as messagebox

def test_centered_dialogs():
    """Test the centered messagebox functionality"""
    root = tk.Tk()
    root.title("Centered MessageBox Test - Full Map Tool Style")
    root.geometry("800x600+100+100")
    root.configure(bg='lightgray')
    
    # Create a frame to simulate the Full Map Tool interface
    main_frame = ttk.Frame(root, padding="20")
    main_frame.pack(fill=tk.BOTH, expand=True)
    
    title_label = ttk.Label(main_frame, text="Full Map Tool - Centered MessageBox Test", 
                           font=("Arial", 14, "bold"))
    title_label.pack(pady=(0, 20))
    
    def test_info():
        messagebox.showinfo("Processing Complete", 
                          "All MAP files processed successfully!\n\nOutput files have been generated.", 
                          parent=root)
    
    def test_warning():
        messagebox.showwarning("No Files Selected", 
                             "Please select MAP files to process before continuing.", 
                             parent=root)
    
    def test_error():
        messagebox.showerror("Processing Error", 
                           "An error occurred during processing:\n\nCould not read the selected MAP file.\nPlease check the file format.", 
                           parent=root)
    
    def test_success():
        messagebox.showsuccess("TMB Processing Complete", 
                             "Generated 5 TMB files:\n• File1.tmb\n• File2.tmb\n• File3.tmb\n• File4.tmb\n• File5.tmb\n\nOutput folder: C:/output/", 
                             parent=root)
    
    def test_question():
        result = messagebox.askyesno("Add Folder", 
                                   "This will add ALL files from the selected folder.\n\nPlease ensure the folder contains only supported MAP/TSK format files.\nUnsupported files may cause processing errors.\n\nContinue?", 
                                   parent=root)
        if result:
            messagebox.showinfo("Result", "You chose to continue with folder addition.", parent=root)
        else:
            messagebox.showinfo("Result", "Folder addition cancelled.", parent=root)
    
    def test_okcancel():
        result = messagebox.askokcancel("Clear Memory", 
                                      "This will clear all loaded processors and free memory.\n\nAny unsaved work will be lost.\n\nProceed?", 
                                      parent=root)
        if result:
            messagebox.showinfo("Result", "Memory cleared successfully.", parent=root)
        else:
            messagebox.showinfo("Result", "Memory clear cancelled.", parent=root)
    
    # Create test buttons in a grid layout
    button_frame = ttk.Frame(main_frame)
    button_frame.pack(fill=tk.BOTH, expand=True)
    
    buttons = [
        ("Test Info Dialog", test_info, "Test successful processing message"),
        ("Test Warning Dialog", test_warning, "Test file selection warning"),
        ("Test Error Dialog", test_error, "Test processing error message"),
        ("Test Success Dialog", test_success, "Test TMB processing success"),
        ("Test Question Dialog", test_question, "Test folder addition confirmation"),
        ("Test OK/Cancel Dialog", test_okcancel, "Test memory clear confirmation")
    ]
    
    for i, (text, command, tooltip) in enumerate(buttons):
        btn = ttk.Button(button_frame, text=text, command=command, width=25)
        btn.grid(row=i//2, column=i%2, padx=10, pady=10, sticky="ew")
        
        # Add tooltip (simple version)
        def create_tooltip(widget, text):
            def on_enter(event):
                widget.config(cursor="hand2")
            def on_leave(event):
                widget.config(cursor="")
            widget.bind("<Enter>", on_enter)
            widget.bind("<Leave>", on_leave)
        
        create_tooltip(btn, tooltip)
    
    # Configure grid weights
    button_frame.columnconfigure(0, weight=1)
    button_frame.columnconfigure(1, weight=1)
    
    # Add instructions
    instructions = ttk.Label(main_frame, 
                           text="Click the buttons above to test centered messagebox dialogs.\nNotice how they appear centered on this window instead of the screen center.",
                           justify=tk.CENTER, font=("Arial", 10))
    instructions.pack(pady=(20, 0))
    
    # Add window position info
    def update_position():
        x = root.winfo_x()
        y = root.winfo_y()
        w = root.winfo_width()
        h = root.winfo_height()
        pos_label.config(text=f"Window Position: ({x}, {y}) Size: {w}x{h}")
        root.after(1000, update_position)
    
    pos_label = ttk.Label(main_frame, text="", font=("Arial", 8))
    pos_label.pack(pady=(10, 0))
    update_position()
    
    root.mainloop()

if __name__ == "__main__":
    test_centered_dialogs()
