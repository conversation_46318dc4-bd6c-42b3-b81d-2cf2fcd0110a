#!/usr/bin/env python3
"""
测试Bump Map Advanced Tool的增强功能
验证worker_file15.txt中要求的所有新功能

Author: Yuribytes
Company: Chipone TE development Team
"""

import os
import sys
import tkinter as tk

# 添加父目录到路径
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, parent_dir)

def test_test_house_clear_functionality():
    """测试Test House Clear功能"""
    print("🧪 测试Test House Clear功能")
    print("=" * 50)
    
    try:
        from bump_map_advanced_frame import BumpMapAdvancedFrame
        
        # 创建测试环境
        root = tk.Tk()
        root.withdraw()
        
        class MockController:
            def __init__(self):
                self.root = root
        
        controller = MockController()
        advanced_frame = BumpMapAdvancedFrame(root, controller)
        
        # 检查Clear功能方法
        if hasattr(advanced_frame, 'clear_test_house_selection'):
            print("✅ clear_test_house_selection方法存在")
        else:
            print("❌ clear_test_house_selection方法不存在")
            return False
        
        # 测试Clear功能
        # 先选择一个测试厂
        advanced_frame.selected_test_house.set("NEPES")
        advanced_frame.on_test_house_selection_changed()
        
        # 验证选择成功
        if "NEPES Corporation" in advanced_frame.selected_test_house_display.get():
            print("✅ 测试厂选择功能正常")
        else:
            print("❌ 测试厂选择功能异常")
            return False
        
        # 测试Clear功能
        advanced_frame.clear_test_house_selection()
        
        # 验证Clear成功
        if advanced_frame.selected_test_house.get() == "":
            print("✅ Clear功能正常 - 选择已清除")
        else:
            print("❌ Clear功能异常 - 选择未清除")
            return False
        
        if "No test house selected" in advanced_frame.selected_test_house_display.get():
            print("✅ Clear功能显示正常")
        else:
            print("❌ Clear功能显示异常")
            return False
        
        # 清理
        root.destroy()
        
        return True
        
    except Exception as e:
        print(f"❌ Test House Clear功能测试失败: {e}")
        return False

def test_multiple_bump_map_functionality():
    """测试多个Bump Map选择功能"""
    print(f"\n🧪 测试多个Bump Map选择功能")
    print("=" * 50)
    
    try:
        from bump_map_advanced_frame import BumpMapAdvancedFrame
        
        # 创建测试环境
        root = tk.Tk()
        root.withdraw()
        
        class MockController:
            def __init__(self):
                self.root = root
        
        controller = MockController()
        advanced_frame = BumpMapAdvancedFrame(root, controller)
        
        # 检查多选相关属性
        multi_select_attrs = ['bump_map_files', 'bump_map_display']
        for attr in multi_select_attrs:
            if hasattr(advanced_frame, attr):
                print(f"✅ {attr}属性存在")
            else:
                print(f"❌ {attr}属性不存在")
                return False
        
        # 检查多选相关方法
        multi_select_methods = [
            'browse_bump_map_files', 'clear_bump_map_files', 
            'update_bump_map_display'
        ]
        for method in multi_select_methods:
            if hasattr(advanced_frame, method):
                print(f"✅ {method}方法存在")
            else:
                print(f"❌ {method}方法不存在")
                return False
        
        # 测试初始状态
        if len(advanced_frame.bump_map_files) == 0:
            print("✅ 初始状态正确 - 无文件选择")
        else:
            print("❌ 初始状态异常")
            return False
        
        # 模拟添加文件
        test_files = ["test1.map", "test2.map", "test3.map"]
        advanced_frame.bump_map_files.extend(test_files)
        advanced_frame.update_bump_map_display()
        
        # 验证显示更新
        display_text = advanced_frame.bump_map_display.get()
        if "3 files" in display_text:
            print("✅ 多文件显示功能正常")
        else:
            print(f"❌ 多文件显示异常: {display_text}")
            return False
        
        # 测试Clear功能
        advanced_frame.clear_bump_map_files()
        
        if len(advanced_frame.bump_map_files) == 0:
            print("✅ Clear功能正常 - 文件列表已清空")
        else:
            print("❌ Clear功能异常 - 文件列表未清空")
            return False
        
        # 清理
        root.destroy()
        
        return True
        
    except Exception as e:
        print(f"❌ 多个Bump Map功能测试失败: {e}")
        return False

def test_output_directory_functionality():
    """测试输出目录功能"""
    print(f"\n🧪 测试输出目录功能")
    print("=" * 50)
    
    try:
        from bump_map_advanced_frame import BumpMapAdvancedFrame
        
        # 创建测试环境
        root = tk.Tk()
        root.withdraw()
        
        class MockController:
            def __init__(self):
                self.root = root
        
        controller = MockController()
        advanced_frame = BumpMapAdvancedFrame(root, controller)
        
        # 检查输出目录相关属性
        if hasattr(advanced_frame, 'output_directory_path'):
            print("✅ output_directory_path属性存在")
        else:
            print("❌ output_directory_path属性不存在")
            return False
        
        # 检查输出目录相关方法
        output_methods = [
            'browse_output_directory', 'auto_generate_output_directory',
            'generate_output_filename'
        ]
        for method in output_methods:
            if hasattr(advanced_frame, method):
                print(f"✅ {method}方法存在")
            else:
                print(f"❌ {method}方法不存在")
                return False
        
        # 测试文件名生成功能
        test_bump = "/path/to/bump_test.map"
        test_dummy = "/path/to/dummy_test.tsk"
        
        filename = advanced_frame.generate_output_filename(test_bump, test_dummy)
        
        if filename and "bump_test" in filename and "dummy_test" in filename:
            print(f"✅ 文件名生成功能正常: {filename}")
        else:
            print(f"❌ 文件名生成异常: {filename}")
            return False
        
        # 清理
        root.destroy()
        
        return True
        
    except Exception as e:
        print(f"❌ 输出目录功能测试失败: {e}")
        return False

def test_batch_processing_messages():
    """测试批处理消息功能"""
    print(f"\n🧪 测试批处理消息功能")
    print("=" * 50)
    
    try:
        from bump_map_advanced_frame import BumpMapAdvancedFrame
        
        # 创建测试环境
        root = tk.Tk()
        root.withdraw()
        
        class MockController:
            def __init__(self):
                self.root = root
        
        controller = MockController()
        advanced_frame = BumpMapAdvancedFrame(root, controller)
        
        # 检查批处理消息方法
        batch_methods = [
            'create_batch_success_message', 'create_partial_success_message'
        ]
        for method in batch_methods:
            if hasattr(advanced_frame, method):
                print(f"✅ {method}方法存在")
            else:
                print(f"❌ {method}方法不存在")
                return False
        
        # 模拟批处理结果
        processed_files = [
            ("bump1.map", "output1.map"),
            ("bump2.map", "output2.map"),
            ("bump3.map", "output3.map")
        ]
        
        failed_files = ["bump4.map", "bump5.map"]
        
        # 测试部分成功消息
        partial_msg = advanced_frame.create_partial_success_message(processed_files, failed_files)
        
        if "Partial Processing Results" in partial_msg and "3" in partial_msg and "2" in partial_msg:
            print("✅ 部分成功消息生成正常")
        else:
            print("❌ 部分成功消息生成异常")
            return False
        
        print("✅ 批处理消息功能正常")
        
        # 清理
        root.destroy()
        
        return True
        
    except Exception as e:
        print(f"❌ 批处理消息功能测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🧪 Bump Map Advanced Tool 增强功能测试")
    print("=" * 70)
    
    test_results = []
    
    try:
        # 测试1: Test House Clear功能
        result1 = test_test_house_clear_functionality()
        test_results.append(("Test House Clear功能", result1))
        
        # 测试2: 多个Bump Map选择功能
        result2 = test_multiple_bump_map_functionality()
        test_results.append(("多个Bump Map选择功能", result2))
        
        # 测试3: 输出目录功能
        result3 = test_output_directory_functionality()
        test_results.append(("输出目录功能", result3))
        
        # 测试4: 批处理消息功能
        result4 = test_batch_processing_messages()
        test_results.append(("批处理消息功能", result4))
        
        # 总结
        print("\n" + "=" * 70)
        print("🎉 增强功能测试结果总结:")
        
        passed = 0
        for test_name, result in test_results:
            status = "✅ 通过" if result else "❌ 失败"
            print(f"   {test_name}: {status}")
            if result:
                passed += 1
        
        print(f"\n📊 总体结果: {passed}/{len(test_results)} 测试通过")
        
        if passed == len(test_results):
            print("\n🎯 🎉 所有增强功能测试成功！")
            print("✅ worker_file15.txt要求的功能已完整实现:")
            print("   1. ✅ Test House选择增加Clear功能")
            print("   2. ✅ Bump Map多选功能")
            print("   3. ✅ 输出路径自动化")
            print("   4. ✅ 批处理消息系统")
            print("   5. ✅ UI优化和空间利用")
            print("\n🚀 Bump Map Advanced Tool 增强功能完全实现！")
        else:
            print("⚠️  部分增强功能测试失败，需要进一步检查")
        
        return passed == len(test_results)
        
    except Exception as e:
        print(f"❌ 增强功能测试过程出错: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
