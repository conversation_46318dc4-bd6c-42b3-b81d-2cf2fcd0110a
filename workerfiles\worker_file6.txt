任务流程：
开发新的功能:
 1, 软件打开Main界面增加选项(Bump map To Tsk map Tool)（和其他两个tool一样，main打开界面先需要选择Bump map To Tsk map Tool，之后点击Bump map To Tsk map Tool button后跳转到Bump map To Tsk map Tool的真正GUI（功能界面）(UI呈现要一致）：
    --Bump map To Tsk map Tool的主要功能，选择不同测试厂的bump map文件和原始dummy map文件，根据bump map文件和dummy map文件，来生成最后叠加的tsk map文件。下面将分GUI设计内容和选择的bump map和原始的dummy map文件进行描述。     
      -----GUI设计部分：
               --两个选择控件（选择bump map文件和选择原始dummy map文件）
               --设计合理间距UI，对勾选择测试厂（预估可能有20个测试厂）(因为每个测试厂有可能有不同的bump和dummy map), 通过选择不同测试厂来完成不同的bump与dummy格式对应（对应不同的代码实现）
			   --输出最终map路径选择（输出格式不一定是必须.tsk或者.map后缀，设计代码优先维持原dummy map后缀格式）	
               --复用另外两个工具的clear memory功能和Back返回软件主界面功能（尽量复用代码，不要设计太多新代码，保证内存正确清理，back功能实现）			   
2, 上述功能开发注意事项：
     --- 其他两个功能不要破坏，代码功能不要破坏
     --- GUI尽量统一，不要占用太多空间
     ---复用代码部分，尽量做到代码统一
	 ---先开发GUI部分，稍后会设计这个Tool的主要功能，请提前根据GUI，分析代码实现方式，提前设计架构。

要求：
1，测试脚本和文件请放到test文件夹
2，生成的md说明文档，请放到temp文件夹
3，程序架构统一，框架统一，代码尽量简洁