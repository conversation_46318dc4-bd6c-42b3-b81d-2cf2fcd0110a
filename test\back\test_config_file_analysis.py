#!/usr/bin/env python3
"""
Analyze the 3509_CP1_program_bin.xlsx configuration file
"""

import sys
import os
sys.path.append('..')

try:
    from openpyxl import load_workbook
except ImportError:
    print("Error: openpyxl not installed. Please run: pip install openpyxl")
    exit(1)


def analyze_config_file(config_path):
    """Analyze the configuration Excel file"""
    print(f"Analyzing Configuration File: {os.path.basename(config_path)}")
    print("=" * 60)
    
    if not os.path.exists(config_path):
        print(f"❌ Config file not found: {config_path}")
        return False
    
    try:
        # Load Excel workbook
        workbook = load_workbook(config_path, read_only=True)
        
        print(f"✅ Excel file loaded successfully")
        print(f"  Worksheets: {workbook.sheetnames}")
        
        # Use the first worksheet
        worksheet = workbook.active
        print(f"  Active worksheet: {worksheet.title}")
        print(f"  Max row: {worksheet.max_row}")
        print(f"  Max column: {worksheet.max_column}")
        
        # Check A2 cell (test program name)
        a2_value = worksheet['A2'].value
        print(f"\nA2 Cell (Test Program Name): '{a2_value}'")
        
        # Check first few rows and columns to understand structure
        print(f"\nFirst 20 rows, columns A-D:")
        print(f"{'Row':<4} {'A':<15} {'B':<15} {'C':<15} {'D':<15}")
        print("-" * 64)
        
        for row in range(1, min(21, worksheet.max_row + 1)):
            a_val = worksheet.cell(row=row, column=1).value
            b_val = worksheet.cell(row=row, column=2).value
            c_val = worksheet.cell(row=row, column=3).value
            d_val = worksheet.cell(row=row, column=4).value
            
            a_str = str(a_val)[:14] if a_val is not None else ""
            b_str = str(b_val)[:14] if b_val is not None else ""
            c_str = str(c_val)[:14] if c_val is not None else ""
            d_str = str(d_val)[:14] if d_val is not None else ""
            
            print(f"{row:<4} {a_str:<15} {b_str:<15} {c_str:<15} {d_str:<15}")
        
        # Look for bin mappings starting from row 6
        print(f"\nLooking for bin mappings from row 6 onwards:")
        bin_mappings = {}
        
        for row in range(6, min(worksheet.max_row + 1, 100)):  # Check up to row 100
            a_val = worksheet.cell(row=row, column=1).value
            b_val = worksheet.cell(row=row, column=2).value
            
            if a_val is not None:
                try:
                    bin_number = int(float(str(a_val)))
                    bin_name = str(b_val).strip() if b_val is not None else ""
                    if bin_name:
                        bin_mappings[bin_number] = bin_name
                        print(f"  Row {row}: Bin {bin_number} -> '{bin_name}'")
                except (ValueError, TypeError):
                    pass
        
        print(f"\nFound {len(bin_mappings)} bin mappings:")
        for bin_num, bin_name in sorted(bin_mappings.items()):
            print(f"  Bin {bin_num}: '{bin_name}'")
        
        workbook.close()
        return True
        
    except Exception as e:
        print(f"❌ Error analyzing config file: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    config_path = "../3509_CP1_program_bin.xlsx"
    
    print("Configuration File Analysis")
    print("Analyzing 3509_CP1_program_bin.xlsx")
    print("=" * 70)
    
    if analyze_config_file(config_path):
        print("\n" + "=" * 70)
        print("🎉 Configuration File Analysis Completed!")
    else:
        print("\n❌ Configuration file analysis failed")


if __name__ == "__main__":
    main()
