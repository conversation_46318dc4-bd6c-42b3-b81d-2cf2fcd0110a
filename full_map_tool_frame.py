#!/usr/bin/env python3
"""
Full Map Tool Frame - Process multiple MAP files
Frame-based version for main application controller
Based on full_map_gui.py but adapted for frame usage
Author: Yuribytes
Company: Chipone TE development Team
"""

import tkinter as tk
from tkinter import ttk, filedialog
import os
from typing import List
import centered_messagebox as messagebox


class FullMapToolFrame:
    """Full Map Tool as a frame for the main application"""
    
    def __init__(self, parent, app_controller):
        self.parent = parent
        self.app_controller = app_controller
        self.main_frame = None
        
        # Variables
        self.config_file_path = tk.StringVar()
        self.map_files = []  # List of selected MAP files
        self.rotation_angle = tk.IntVar(value=0)
        self.filter_empty = tk.BooleanVar(value=True)
        self.sort_by_quantity = tk.BooleanVar(value=True)  # New: Sort bins by quantity (default: True)
        self.output_folder_path = tk.StringVar()
        self.tmb_output_folder_path = tk.StringVar()  # New: TMB output folder path
        self.status_var = tk.StringVar(value="Ready - Select MAP files to begin")

        # Initialize processors and config reader
        self.processors = {}  # Dict: filename -> processor
        self.config_reader = None
        self.tmb_processor = None  # New: TMB processor

        # Memory management
        self.processing_count = 0
        self.last_memory_usage = 0.0
        
        self.create_widgets()
    
    def create_widgets(self):
        """Create the Full Map Tool interface"""
        # Main frame
        self.main_frame = ttk.Frame(self.parent, padding="10")
        
        # Configure grid weights
        self.main_frame.columnconfigure(1, weight=1)
        
        # Title
        title_frame = ttk.Frame(self.main_frame)
        title_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        ttk.Label(title_frame, text="Full Map Tool - Process Multiple MAP Files", 
                 font=("Arial", 14, "bold")).pack(side=tk.LEFT)
        
        # Configuration file selection section
        config_frame = ttk.LabelFrame(self.main_frame, text="Configuration File", padding="10")
        config_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        config_frame.columnconfigure(1, weight=1)
        
        ttk.Label(config_frame, text="Config Excel File:").grid(row=0, column=0, sticky=tk.W, padx=(0, 5))
        
        config_entry = ttk.Entry(config_frame, textvariable=self.config_file_path, width=60)
        config_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 5))
        
        ttk.Button(config_frame, text="Browse...", command=self.browse_config_file).grid(row=0, column=2)
        
        # MAP files selection section
        files_frame = ttk.LabelFrame(self.main_frame, text="MAP Files Selection", padding="10")
        files_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        files_frame.columnconfigure(0, weight=1)
        
        # Buttons for file selection
        buttons_frame = ttk.Frame(files_frame)
        buttons_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        
        ttk.Button(buttons_frame, text="Add MAP Files...", 
                  command=self.add_map_files).grid(row=0, column=0, padx=(0, 5))
        ttk.Button(buttons_frame, text="Add Folder...", 
                  command=self.add_folder).grid(row=0, column=1, padx=(0, 5))
        ttk.Button(buttons_frame, text="Clear All", 
                  command=self.clear_files).grid(row=0, column=2, padx=(0, 5))
        
        # File list with scrollbar
        list_frame = ttk.Frame(files_frame)
        list_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        list_frame.columnconfigure(0, weight=1)
        list_frame.rowconfigure(0, weight=1)
        
        # Listbox with scrollbar
        self.files_listbox = tk.Listbox(list_frame, height=6, selectmode=tk.EXTENDED)
        self.files_listbox.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        files_scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.files_listbox.yview)
        files_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        self.files_listbox.configure(yscrollcommand=files_scrollbar.set)

        # Add mousewheel support for Map File Selection area (entire area)
        def _on_files_mousewheel(event):
            self.files_listbox.yview_scroll(int(-1*(event.delta/120)), "units")

        # Bind mousewheel to the entire files frame and all its children
        def bind_files_mousewheel_recursive(widget):
            widget.bind("<MouseWheel>", _on_files_mousewheel)
            for child in widget.winfo_children():
                bind_files_mousewheel_recursive(child)

        bind_files_mousewheel_recursive(files_frame)
        
        # File management buttons
        file_buttons_frame = ttk.Frame(files_frame)
        file_buttons_frame.grid(row=2, column=0, pady=(5, 0), sticky=(tk.W, tk.E))

        ttk.Button(file_buttons_frame, text="Move Up",
                  command=self.move_file_up).grid(row=0, column=0, padx=(0, 5))
        ttk.Button(file_buttons_frame, text="Move Down",
                  command=self.move_file_down).grid(row=0, column=1, padx=(0, 5))
        ttk.Button(file_buttons_frame, text="Remove Selected",
                  command=self.remove_selected_files).grid(row=0, column=2, padx=(0, 5))

        # Preview filename label
        self.filename_preview = tk.StringVar(value="Output filename will be shown here")
        preview_label = ttk.Label(files_frame, textvariable=self.filename_preview,
                                 font=("Arial", 9, "italic"), foreground="blue")
        preview_label.grid(row=3, column=0, pady=(5, 0), sticky=tk.W)
        
        # Rotation angle selection section
        rotation_frame = ttk.LabelFrame(self.main_frame, text="Rotation Angle", padding="10")
        rotation_frame.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        ttk.Radiobutton(rotation_frame, text="0° (Original)", variable=self.rotation_angle, 
                       value=0).grid(row=0, column=0, sticky=tk.W, padx=(0, 20))
        ttk.Radiobutton(rotation_frame, text="90° (Clockwise)", variable=self.rotation_angle, 
                       value=90).grid(row=0, column=1, sticky=tk.W, padx=(0, 20))
        ttk.Radiobutton(rotation_frame, text="180°", variable=self.rotation_angle, 
                       value=180).grid(row=1, column=0, sticky=tk.W, padx=(0, 20))
        ttk.Radiobutton(rotation_frame, text="270° (Counter-clockwise)", variable=self.rotation_angle, 
                       value=270).grid(row=1, column=1, sticky=tk.W)
        
        # Output options section
        filter_frame = ttk.LabelFrame(self.main_frame, text="Output Options", padding="10")
        filter_frame.grid(row=4, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        filter_frame.columnconfigure(1, weight=1)

        tk.Checkbutton(filter_frame, text="Filter empty areas (recommended for large files)",
                      variable=self.filter_empty).grid(row=0, column=0, columnspan=3, sticky=tk.W, pady=(0, 8))

        # Bin sorting option
        tk.Checkbutton(filter_frame, text="Sort bins by quantity (descending order)",
                      variable=self.sort_by_quantity).grid(row=1, column=0, columnspan=3, sticky=tk.W, pady=(0, 8))

        # Excel output folder selection
        ttk.Label(filter_frame, text="Excel Output Folder:").grid(row=2, column=0, sticky=tk.W, padx=(0, 8))

        output_entry = ttk.Entry(filter_frame, textvariable=self.output_folder_path, width=50)
        output_entry.grid(row=2, column=1, sticky=(tk.W, tk.E), padx=(0, 8))

        ttk.Button(filter_frame, text="Browse...", command=self.browse_output_folder).grid(row=2, column=2)

        # TMB output folder selection (New feature)
        ttk.Label(filter_frame, text="TMB Output Folder:").grid(row=3, column=0, sticky=tk.W, padx=(0, 8), pady=(8, 0))

        tmb_output_entry = ttk.Entry(filter_frame, textvariable=self.tmb_output_folder_path, width=50)
        tmb_output_entry.grid(row=3, column=1, sticky=(tk.W, tk.E), padx=(0, 8), pady=(8, 0))

        ttk.Button(filter_frame, text="Browse...", command=self.browse_tmb_output_folder).grid(row=3, column=2, pady=(8, 0))
        
        # Processing information section
        info_frame = ttk.LabelFrame(self.main_frame, text="Processing Information", padding="10")
        info_frame.grid(row=5, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        info_frame.columnconfigure(0, weight=1)
        info_frame.rowconfigure(0, weight=1)
        
        self.info_text = tk.Text(info_frame, height=4, width=80, state=tk.DISABLED)
        self.info_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        info_scrollbar = ttk.Scrollbar(info_frame, orient=tk.VERTICAL, command=self.info_text.yview)
        info_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        self.info_text.configure(yscrollcommand=info_scrollbar.set)

        # Add mousewheel support for Processing Information area (entire area)
        def _on_info_mousewheel(event):
            self.info_text.yview_scroll(int(-1*(event.delta/120)), "units")

        # Bind mousewheel to the entire info frame and all its children
        def bind_info_mousewheel_recursive(widget):
            widget.bind("<MouseWheel>", _on_info_mousewheel)
            for child in widget.winfo_children():
                bind_info_mousewheel_recursive(child)

        bind_info_mousewheel_recursive(info_frame)
        
        # Buttons section
        button_frame = ttk.Frame(self.main_frame)
        button_frame.grid(row=6, column=0, columnspan=2, pady=(10, 0))

        ttk.Button(button_frame, text="Process All Files",
                  command=self.process_all_files).pack(side=tk.LEFT, padx=(0, 15))
        ttk.Button(button_frame, text="Process TMB",
                  command=self.process_tmb_files, style="Accent.TButton").pack(side=tk.LEFT, padx=(0, 15))
        ttk.Button(button_frame, text="Clear Memory",
                  command=self.clear_memory).pack(side=tk.LEFT, padx=(0, 15))
        ttk.Button(button_frame, text="← Back",
                  command=self.return_to_selector).pack(side=tk.LEFT, padx=(0, 15))
        ttk.Button(button_frame, text="Exit",
                  command=self.exit_application).pack(side=tk.LEFT)
        
        # Status bar
        status_bar = ttk.Label(self.main_frame, textvariable=self.status_var, relief=tk.SUNKEN)
        status_bar.grid(row=7, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(10, 0))
        
        # Initialize displays
        self.update_info_display()
        self.update_filename_preview()
    
    def browse_config_file(self):
        """Open file dialog to select configuration Excel file"""
        filename = filedialog.askopenfilename(
            title="Select Configuration Excel File",
            filetypes=[
                ("Excel files", "*.xlsx"),
                ("Excel files", "*.xls"),
                ("All files", "*.*")
            ]
        )
        if filename:
            self.config_file_path.set(filename)
            self.status_var.set(f"Configuration file selected: {os.path.basename(filename)}")

    def browse_output_folder(self):
        """Open folder dialog to select Excel output directory"""
        folder = filedialog.askdirectory(
            title="Select Excel Output Folder"
        )
        if folder:
            self.output_folder_path.set(folder)
            self.status_var.set(f"Excel output folder selected: {folder}")

    def browse_tmb_output_folder(self):
        """Open folder dialog to select TMB output directory"""
        folder = filedialog.askdirectory(
            title="Select TMB Output Folder"
        )
        if folder:
            self.tmb_output_folder_path.set(folder)
            self.status_var.set(f"TMB output folder selected: {folder}")

    def add_map_files(self):
        """Add MAP files to the processing list"""
        filenames = filedialog.askopenfilenames(
            title="Select MAP Files",
            filetypes=[
                ("All supported files", "*.*"),
                ("MAP files", "*.map"),
                ("TSK files", "*.tsk")
            ]
        )
        
        if filenames:
            added_count = 0
            for filename in filenames:
                if filename not in self.map_files:
                    self.map_files.append(filename)
                    self.files_listbox.insert(tk.END, os.path.basename(filename))
                    added_count += 1
            
            self.status_var.set(f"Added {added_count} MAP files. Total: {len(self.map_files)} files")
            self.update_info_display()
            self.update_filename_preview()
    
    def add_folder(self):
        """Add all files from a selected folder (no format restriction)"""
        # Show user warning about format selection
        result = messagebox.askyesno(
            "Add Folder",
            "This will add ALL files from the selected folder.\n\n"
            "Please ensure the folder contains only supported MAP/TSK format files.\n"
            "Unsupported files may cause processing errors.\n\n"
            "Continue?",
            parent=self.parent
        )

        if not result:
            return

        folder_path = filedialog.askdirectory(title="Select Folder Containing MAP Files")

        if folder_path:
            added_count = 0

            for filename in os.listdir(folder_path):
                file_path = os.path.join(folder_path, filename)
                if os.path.isfile(file_path):  # Add all files, no extension restriction
                    if file_path not in self.map_files:
                        self.map_files.append(file_path)
                        self.files_listbox.insert(tk.END, filename)
                        added_count += 1

            if added_count > 0:
                self.status_var.set(f"Added {added_count} files from folder. Total: {len(self.map_files)} files")
                self.update_info_display()
                self.update_filename_preview()
            else:
                messagebox.showinfo("No Files Found", "No files found in the selected folder.", parent=self.parent)
    
    def clear_files(self):
        """Clear all selected files and free memory"""
        # Clear processors with memory cleanup
        for processor in self.processors.values():
            if hasattr(processor, 'clear_memory'):
                processor.clear_memory()
        
        self.map_files.clear()
        self.files_listbox.delete(0, tk.END)
        self.processors.clear()
        
        self.status_var.set("All files and memory cleared")
        self.update_info_display()
        self.update_filename_preview()
    
    def remove_selected_files(self):
        """Remove selected files from the list"""
        selected_indices = self.files_listbox.curselection()
        
        if not selected_indices:
            messagebox.showwarning("No Selection", "Please select files to remove.", parent=self.parent)
            return
        
        # Remove in reverse order to maintain indices
        for index in reversed(selected_indices):
            file_path = self.map_files[index]
            del self.map_files[index]
            self.files_listbox.delete(index)
            
            # Remove from processors if exists
            filename = os.path.basename(file_path)
            if filename in self.processors:
                # Clear memory before removing
                if hasattr(self.processors[filename], 'clear_memory'):
                    self.processors[filename].clear_memory()
                del self.processors[filename]
        
        self.status_var.set(f"Removed {len(selected_indices)} files. Total: {len(self.map_files)} files")
        self.update_info_display()
        self.update_filename_preview()

    def move_file_up(self):
        """Move selected file up in the list"""
        selected_indices = self.files_listbox.curselection()

        if not selected_indices:
            messagebox.showwarning("No Selection", "Please select a file to move up.", parent=self.parent)
            return

        if len(selected_indices) > 1:
            messagebox.showwarning("Multiple Selection", "Please select only one file to move.", parent=self.parent)
            return

        index = selected_indices[0]

        if index == 0:
            messagebox.showinfo("Cannot Move", "File is already at the top.", parent=self.parent)
            return

        # Swap files in the list
        self.map_files[index], self.map_files[index - 1] = self.map_files[index - 1], self.map_files[index]

        # Update listbox
        self.refresh_file_listbox()

        # Select the moved item
        self.files_listbox.selection_set(index - 1)

        self.status_var.set(f"Moved file up. First file determines output filename.")
        self.update_info_display()
        self.update_filename_preview()

    def move_file_down(self):
        """Move selected file down in the list"""
        selected_indices = self.files_listbox.curselection()

        if not selected_indices:
            messagebox.showwarning("No Selection", "Please select a file to move down.", parent=self.parent)
            return

        if len(selected_indices) > 1:
            messagebox.showwarning("Multiple Selection", "Please select only one file to move.", parent=self.parent)
            return

        index = selected_indices[0]

        if index == len(self.map_files) - 1:
            messagebox.showinfo("Cannot Move", "File is already at the bottom.", parent=self.parent)
            return

        # Swap files in the list
        self.map_files[index], self.map_files[index + 1] = self.map_files[index + 1], self.map_files[index]

        # Update listbox
        self.refresh_file_listbox()

        # Select the moved item
        self.files_listbox.selection_set(index + 1)

        self.status_var.set(f"Moved file down. First file determines output filename.")
        self.update_info_display()
        self.update_filename_preview()

    def refresh_file_listbox(self):
        """Refresh the file listbox display"""
        self.files_listbox.delete(0, tk.END)
        for file_path in self.map_files:
            self.files_listbox.insert(tk.END, os.path.basename(file_path))

    def update_filename_preview(self):
        """Update the filename preview based on current file order"""
        if not self.map_files:
            self.filename_preview.set("Output filename will be shown here")
            return

        try:
            # Generate preview filename based on first file
            first_file = os.path.basename(self.map_files[0])
            base_name = os.path.splitext(first_file)[0]
            preview_filename = f"{base_name}_FullMap_{len(self.map_files)}files_YYYYMMDD_HHMMSS.xlsx"
            self.filename_preview.set(f"Output: {preview_filename}")

        except Exception as e:
            self.filename_preview.set(f"Preview error: {str(e)}")

    def update_info_display(self):
        """Update the information display"""
        self.info_text.config(state=tk.NORMAL)
        self.info_text.delete(1.0, tk.END)

        if not self.map_files:
            info_text = "No MAP files selected.\n\nPlease add MAP files using the buttons above."
        else:
            info_text = f"Selected MAP Files: {len(self.map_files)}\n"
            info_text += f"(Use 'Move Up/Down' to change order - first file determines output filename)\n\n"

            for i, file_path in enumerate(self.map_files[:3], 1):  # Show first 3 files
                filename = os.path.basename(file_path)
                file_size = os.path.getsize(file_path) if os.path.exists(file_path) else 0

                # Highlight first file
                if i == 1:
                    info_text += f"{i:2d}. {filename} ({file_size:,} bytes) ← First file (determines filename)\n"
                else:
                    info_text += f"{i:2d}. {filename} ({file_size:,} bytes)\n"

            if len(self.map_files) > 3:
                info_text += f"... and {len(self.map_files) - 3} more files\n"

            info_text += f"\nOutput: One Excel file with {len(self.map_files)} sheets"

        self.info_text.insert(tk.END, info_text)
        self.info_text.config(state=tk.DISABLED)

    def process_all_files(self):
        """Process all selected MAP files"""
        if not self.map_files:
            messagebox.showwarning("No Files", "Please select MAP files to process.", parent=self.parent)
            return

        # Auto memory check before processing
        self.auto_memory_check()

        # Process files
        self.status_var.set("Processing files...")
        self.parent.update()

        try:
            from full_map_processor import FullMapProcessor
            processor = FullMapProcessor()

            # Set configuration if provided
            config_path = self.config_file_path.get()
            if config_path and os.path.exists(config_path):
                from config_reader import ConfigReader
                config_reader = ConfigReader()
                if config_reader.read_config_file(config_path):
                    processor.set_config_reader(config_reader)
                else:
                    print(f"Warning: Failed to read config file: {config_path}")

            # Set processing options
            processor.set_rotation_angle(self.rotation_angle.get())
            processor.set_filter_empty(self.filter_empty.get())
            processor.set_sort_by_quantity(self.sort_by_quantity.get())

            # Set output folder if specified
            output_folder = self.output_folder_path.get()
            if output_folder:
                processor.set_output_folder(output_folder)

            # Process all files
            output_file = processor.process_multiple_files(self.map_files)
            success = output_file is not None

            if success:
                # Create detailed success message
                success_msg = "Full Map Processing Complete!\n\n"
                success_msg += f"Successfully processed {len(self.map_files)} MAP files:\n"

                # Show first few files and total count
                files_to_show = min(5, len(self.map_files))
                for i in range(files_to_show):
                    filename = os.path.basename(self.map_files[i])
                    success_msg += f"• {filename}\n"

                if len(self.map_files) > 5:
                    success_msg += f"• ... and {len(self.map_files) - 5} more files\n"

                # Add output information
                if output_file:
                    success_msg += f"\nOutput file: {os.path.basename(output_file)}"
                    if output_folder:
                        success_msg += f"\nOutput folder: {output_folder}"
                    else:
                        success_msg += f"\nOutput folder: {os.path.dirname(output_file)}"

                # Add processing options information
                success_msg += f"\n\nProcessing options applied:"
                success_msg += f"\n• Rotation angle: {self.rotation_angle.get()}°"
                success_msg += f"\n• Filter empty areas: {'Yes' if self.filter_empty.get() else 'No'}"
                success_msg += f"\n• Sort bins by quantity: {'Yes' if self.sort_by_quantity.get() else 'No'}"

                # Add configuration information if used
                config_path = self.config_file_path.get()
                if config_path and os.path.exists(config_path):
                    success_msg += f"\n• Configuration file: {os.path.basename(config_path)}"

                messagebox.showinfo("Processing Complete", success_msg, parent=self.parent)
                self.status_var.set("Processing completed successfully")
            else:
                # Create detailed error message
                error_msg = "Full Map Processing Failed!\n\n"
                error_msg += f"Attempted to process {len(self.map_files)} MAP files, but some failed.\n\n"
                error_msg += "Common causes of processing failures:\n"
                error_msg += "• Invalid or corrupted MAP file format\n"
                error_msg += "• Unsupported MAP file version\n"
                error_msg += "• Insufficient memory for large files\n"
                error_msg += "• File access permissions issues\n"
                error_msg += "• Missing or invalid configuration file\n\n"
                error_msg += "Please check the console output for detailed error messages\n"
                error_msg += "and verify that all selected files are valid MAP format files."

                messagebox.showerror("Processing Failed", error_msg, parent=self.parent)
                self.status_var.set("Processing completed with errors")

        except Exception as e:
            # Create detailed exception error message
            error_msg = "Full Map Processing Error!\n\n"
            error_msg += f"An unexpected error occurred while processing {len(self.map_files)} MAP files.\n\n"
            error_msg += f"Error details:\n{str(e)}\n\n"
            error_msg += "Troubleshooting steps:\n"
            error_msg += "• Verify all selected files are valid MAP format\n"
            error_msg += "• Check available disk space and memory\n"
            error_msg += "• Ensure output folder has write permissions\n"
            error_msg += "• Try processing fewer files at once\n"
            error_msg += "• Check console output for additional details"

            messagebox.showerror("Processing Error", error_msg, parent=self.parent)
            self.status_var.set("Processing failed")
        finally:
            # Update processing count and check memory
            self.processing_count += 1
            current_memory = self.get_total_memory_usage()
            if current_memory > 0:
                print(f"Full Map processing #{self.processing_count} complete. Total memory usage: {current_memory:.1f} MB")

    def process_tmb_files(self):
        """Process all selected MAP files to TMB format"""
        if not self.map_files:
            messagebox.showwarning("No Files", "Please select MAP files to process.", parent=self.parent)
            return

        # Check TMB output folder
        tmb_output_folder = self.tmb_output_folder_path.get()
        if not tmb_output_folder:
            messagebox.showwarning("No TMB Output Folder",
                                 "Please select a TMB output folder before processing.", parent=self.parent)
            return

        if not os.path.exists(tmb_output_folder):
            messagebox.showerror("Invalid Folder",
                               "The selected TMB output folder does not exist.", parent=self.parent)
            return

        # Process TMB files
        self.status_var.set("Processing TMB files...")
        self.parent.update()

        try:
            from tmb_processor import TMBProcessor
            self.tmb_processor = TMBProcessor()

            # Set configuration if provided
            config_path = self.config_file_path.get()
            if config_path and os.path.exists(config_path):
                from config_reader import ConfigReader
                config_reader = ConfigReader()
                if config_reader.read_config_file(config_path):
                    self.tmb_processor.set_config_reader(config_reader)
                else:
                    print(f"Warning: Failed to read config file: {config_path}")

            # Set TMB output folder
            self.tmb_processor.set_output_folder(tmb_output_folder)

            # Process all files to TMB
            successful_outputs = self.tmb_processor.process_multiple_maps_to_tmb(self.map_files)

            if successful_outputs:
                success_msg = f"TMB processing completed successfully!\n\n"
                success_msg += f"Generated {len(successful_outputs)} TMB files:\n"
                for output_file in successful_outputs:
                    success_msg += f"• {os.path.basename(output_file)}\n"
                success_msg += f"\nOutput folder: {tmb_output_folder}"

                messagebox.showinfo("TMB Processing Complete", success_msg, parent=self.parent)
                self.status_var.set(f"TMB processing completed - {len(successful_outputs)} files generated")
            else:
                messagebox.showerror("Error", "TMB processing failed. No files were generated.", parent=self.parent)
                self.status_var.set("TMB processing failed")

        except ImportError:
            messagebox.showerror("Error", "TMB processor module not found. Please check the installation.", parent=self.parent)
            self.status_var.set("TMB processor not available")
        except Exception as e:
            messagebox.showerror("Error", f"An error occurred during TMB processing:\n{str(e)}", parent=self.parent)
            self.status_var.set("TMB processing failed")

    def clear_memory(self):
        """Clear all processors and free memory"""
        try:
            memory_freed = 0.0
            processor_count = len(self.processors)

            # Clear all processors
            for filename, processor in self.processors.items():
                if hasattr(processor, 'get_memory_usage_mb'):
                    memory_freed += processor.get_memory_usage_mb()
                if hasattr(processor, 'clear_memory'):
                    processor.clear_memory()

            # Clear processors dictionary
            self.processors.clear()

            # Clear config reader and TMB processor
            self.config_reader = None
            self.tmb_processor = None

            # Update memory tracking
            self.last_memory_usage = 0.0

            # Always provide feedback to user (consistent with AB Map Tool)
            if memory_freed > 0:
                print(f"Full Map Tool - Total memory cleared: ~{memory_freed:.1f} MB from {processor_count} processors")
                self.status_var.set(f"Memory cleared: {memory_freed:.1f} MB freed from {processor_count} processors")
            else:
                print("Full Map Tool - Memory cleared (no active processors)")
                self.status_var.set("Memory cleared - no active processors")

        except Exception as e:
            print(f"Warning: Error during Full Map memory cleanup: {e}")
            self.status_var.set("Memory cleanup completed with warnings")

    def get_total_memory_usage(self) -> float:
        """Get total memory usage from all processors"""
        try:
            total_memory = 0.0
            for processor in self.processors.values():
                if hasattr(processor, 'get_memory_usage_mb'):
                    total_memory += processor.get_memory_usage_mb()
            return total_memory
        except Exception:
            return 0.0

    def auto_memory_check(self):
        """Automatically check and manage memory usage"""
        try:
            current_memory = self.get_total_memory_usage()
            processor_count = len(self.processors)

            # Auto-clear if memory usage is high (>800MB) and processing count > 2
            if current_memory > 800 and self.processing_count > 2:
                print(f"Full Map Tool - Auto-clearing memory: {current_memory:.1f} MB in use")

                # Clear memory
                self.clear_memory()

                # Show auto-cleanup popup
                self.show_auto_cleanup_popup(current_memory, processor_count)

                return True

            return False

        except Exception as e:
            print(f"Warning: Error in Full Map auto memory check: {e}")
            return False

    def show_auto_cleanup_popup(self, memory_freed: float, processor_count: int):
        """Show popup for automatic memory cleanup"""
        try:
            # Create popup window
            popup = tk.Toplevel(self.app_controller.root)
            popup.title("Auto Memory Cleanup")
            popup.geometry("380x140")
            popup.resizable(False, False)

            # Center popup on parent window
            popup.transient(self.app_controller.root)

            # Center popup
            x = self.app_controller.root.winfo_x() + (self.app_controller.root.winfo_width() // 2) - 190
            y = self.app_controller.root.winfo_y() + (self.app_controller.root.winfo_height() // 2) - 70
            popup.geometry(f"380x140+{x}+{y}")

            # Create content
            main_frame = ttk.Frame(popup, padding="20")
            main_frame.pack(fill=tk.BOTH, expand=True)

            # Icon and message
            message = f"🔄 Auto Memory Cleanup Triggered\n\n{memory_freed:.1f} MB freed from {processor_count} processors\n\nMemory usage was high, automatically cleared."

            ttk.Label(main_frame, text=message, justify=tk.CENTER,
                     font=("Arial", 9)).pack(expand=True)

            # Auto-close after 2 seconds
            popup.after(2000, popup.destroy)

            # Allow manual close with Escape key
            popup.bind('<Escape>', lambda e: popup.destroy())
            popup.focus_set()

        except Exception as e:
            print(f"Warning: Error showing Full Map auto cleanup popup: {e}")

    def show_memory_cleanup_popup(self, memory_freed: float, processor_count: int):
        """Show a temporary popup with memory cleanup information"""
        try:
            # Create popup window
            popup = tk.Toplevel(self.app_controller.root)
            popup.title("Memory Cleanup")
            popup.geometry("350x120")
            popup.resizable(False, False)

            # Center popup on parent window
            popup.transient(self.app_controller.root)
            popup.grab_set()

            # Center popup
            x = self.app_controller.root.winfo_x() + (self.app_controller.root.winfo_width() // 2) - 175
            y = self.app_controller.root.winfo_y() + (self.app_controller.root.winfo_height() // 2) - 60
            popup.geometry(f"350x120+{x}+{y}")

            # Create content
            main_frame = ttk.Frame(popup, padding="20")
            main_frame.pack(fill=tk.BOTH, expand=True)

            # Icon and message
            if memory_freed > 0:
                message = f"✅ Memory Cleanup Complete\n\n{memory_freed:.1f} MB freed from {processor_count} processors"
            elif processor_count > 0:
                message = f"✅ Memory Cleanup Complete\n\nProcessors cleared (no memory to free)"
            else:
                message = "✅ Application Closing\n\nThank you for using Full Map Tool!"

            ttk.Label(main_frame, text=message, justify=tk.CENTER,
                     font=("Arial", 10)).pack(expand=True)

            # Auto-close after 2 seconds
            popup.after(2000, popup.destroy)

            # Allow manual close with Escape key
            popup.bind('<Escape>', lambda e: popup.destroy())
            popup.focus_set()

        except Exception as e:
            print(f"Warning: Error showing Full Map cleanup popup: {e}")

    def return_to_selector(self):
        """Return to tool selector with automatic memory cleanup"""
        try:
            # Get memory info before clearing (for accurate reporting)
            memory_before_clear = 0.0
            processor_count = len(self.processors)

            for processor in self.processors.values():
                if hasattr(processor, 'get_memory_usage_mb'):
                    memory_before_clear += processor.get_memory_usage_mb()

            # Clear memory automatically
            self.clear_memory()
            print(f"Full Map Tool - Memory cleared when returning to main menu: {memory_before_clear:.1f} MB freed")

            # Verify memory was actually cleared (processors should be empty now)
            memory_after_clear = 0.0
            for processor in self.processors.values():
                if hasattr(processor, 'get_memory_usage_mb'):
                    memory_after_clear += processor.get_memory_usage_mb()

            # Calculate actual memory freed
            actual_memory_freed = memory_before_clear - memory_after_clear
            print(f"Full Map Tool - Actual memory freed: {actual_memory_freed:.1f} MB (before: {memory_before_clear:.1f} MB, after: {memory_after_clear:.1f} MB)")

            # Show 1-second cleanup popup with actual freed memory
            self.show_return_cleanup_popup(actual_memory_freed, processor_count)

            # Return to selector after popup
            self.app_controller.root.after(1100, self.app_controller.show_tool_selector)

        except Exception as e:
            print(f"Warning: Error during Full Map return cleanup: {e}")
            # Still return to selector even if cleanup fails
            self.app_controller.show_tool_selector()

    def show_return_cleanup_popup(self, memory_freed: float, processor_count: int):
        """Show 1-second cleanup popup when returning to main menu"""
        try:
            # Create popup window
            popup = tk.Toplevel(self.app_controller.root)
            popup.title("Memory Cleanup")
            popup.geometry("350x120")
            popup.resizable(False, False)

            # Center popup on parent window
            popup.transient(self.app_controller.root)
            popup.grab_set()

            # Center popup
            x = self.app_controller.root.winfo_x() + (self.app_controller.root.winfo_width() // 2) - 175
            y = self.app_controller.root.winfo_y() + (self.app_controller.root.winfo_height() // 2) - 60
            popup.geometry(f"350x120+{x}+{y}")

            # Create content
            main_frame = ttk.Frame(popup, padding="20")
            main_frame.pack(fill=tk.BOTH, expand=True)

            # Icon and message
            if memory_freed > 0:
                message = f"🔄 Returning to Main Menu\n\n{memory_freed:.1f} MB freed from {processor_count} processors"
            elif processor_count > 0:
                message = f"🔄 Returning to Main Menu\n\nProcessors cleared (no memory to free)"
            else:
                message = "🔄 Returning to Main Menu\n\nFull Map Tool cleared"

            ttk.Label(main_frame, text=message, justify=tk.CENTER,
                     font=("Arial", 10)).pack(expand=True)

            # Auto-close after 1 second
            popup.after(1000, popup.destroy)

            # Allow manual close with Escape key
            popup.bind('<Escape>', lambda e: popup.destroy())
            popup.focus_set()

        except Exception as e:
            print(f"Warning: Error showing Full Map return cleanup popup: {e}")

    def has_unsaved_work(self):
        """Check if there's unsaved work"""
        return (len(self.processors) > 0 or
                len(self.map_files) > 0 or
                self.processing_count > 0)

    def exit_application(self):
        """Exit application with cleanup"""
        try:
            # Get memory info before clearing
            memory_freed = 0.0
            processor_count = len(self.processors)

            for processor in self.processors.values():
                if hasattr(processor, 'get_memory_usage_mb'):
                    memory_freed += processor.get_memory_usage_mb()

            # Clear all memory before exit
            self.clear_memory()
            print("Full Map Tool - Application memory cleared before exit")

            # Always show cleanup popup when exiting (user feedback)
            self.show_memory_cleanup_popup(memory_freed, processor_count)
            # Wait a moment for popup to show before quitting
            self.app_controller.root.after(2100, self.app_controller.root.quit)

        except Exception as e:
            print(f"Warning: Error during Full Map exit cleanup: {e}")
            self.app_controller.root.quit()

    def show(self):
        """Show the Full Map Tool frame"""
        if self.main_frame:
            self.main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

    def hide(self):
        """Hide the Full Map Tool frame"""
        if self.main_frame:
            self.main_frame.grid_remove()


def main():
    """Test function for standalone running"""
    root = tk.Tk()
    root.title("Full Map Tool Test")
    root.geometry("800x800")

    # Mock app controller for testing
    class MockController:
        def __init__(self, root):
            self.root = root
        def return_to_selector_with_confirmation(self, tool_name, has_unsaved):
            print(f"Would return to selector from {tool_name}, unsaved: {has_unsaved}")
            return True

    controller = MockController(root)
    full_tool = FullMapToolFrame(root, controller)
    full_tool.show()

    root.mainloop()


if __name__ == "__main__":
    main()
