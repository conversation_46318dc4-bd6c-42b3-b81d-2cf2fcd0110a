#!/usr/bin/env python3
"""
Test the GUI process_all_files method directly
"""

import os
import sys
from pathlib import Path

# Add parent directory to path to import modules
sys.path.insert(0, str(Path(__file__).parent.parent))

def test_gui_process_all_files():
    """Test the GUI process_all_files method that was causing errors"""
    print('=== GUI Process All Files Method Test ===')
    print('This simulates clicking the "Process All Files" button')
    print()
    
    try:
        from main_application import TSKMapApplication
        import tkinter as tk
        
        # Create application
        app = TSKMapApplication()
        app.root.withdraw()  # Hide window
        
        # Switch to Full Map Tool
        app.show_full_map_tool()
        full_tool = app.full_map_tool
        
        # Set up test files
        test_map_file = os.path.join('test', '3AA111-01-B4.map')
        test_config = os.path.join('test', 'test_config.xlsx')
        
        if not os.path.exists(test_map_file):
            print(f'ERROR: Test MAP file not found: {test_map_file}')
            return False
        
        # Configure the tool
        full_tool.map_files = [test_map_file]
        full_tool.config_file_path.set(test_config)
        full_tool.rotation_angle.set(0)  # 0 degrees
        full_tool.filter_empty.set(True)  # Filter empty areas
        
        print(f'✅ Configuration set:')
        print(f'   MAP files: {len(full_tool.map_files)}')
        print(f'   Config file: {os.path.basename(test_config)}')
        print(f'   Rotation: {full_tool.rotation_angle.get()}°')
        print(f'   Filter empty: {full_tool.filter_empty.get()}')
        
        # Update info display
        full_tool.update_info_display()
        print('✅ Info display updated')
        
        # Count existing Excel files before processing
        excel_files_before = [f for f in os.listdir('.') if f.startswith('3AA111_full_map_') and f.endswith('.xlsx')]
        print(f'Excel files before processing: {len(excel_files_before)}')
        
        # Call the process_all_files method directly (this was causing the error)
        print()
        print('🔄 Calling process_all_files method...')
        
        # This should work without the ConfigReader error now
        full_tool.process_all_files()
        
        # Check if new Excel file was created
        excel_files_after = [f for f in os.listdir('.') if f.startswith('3AA111_full_map_') and f.endswith('.xlsx')]
        new_files = set(excel_files_after) - set(excel_files_before)
        
        if new_files:
            new_file = list(new_files)[0]
            print(f'✅ SUCCESS: New Excel file created: {new_file}')
            
            # Verify the file
            if os.path.exists(new_file):
                file_size = os.path.getsize(new_file)
                print(f'✅ File verified: {file_size} bytes')
                
                # Quick content check
                try:
                    from openpyxl import load_workbook
                    wb = load_workbook(new_file)
                    print(f'✅ Excel file is valid with sheets: {wb.sheetnames}')
                    wb.close()
                except Exception as e:
                    print(f'⚠️ Could not verify Excel content: {e}')
                
                return True
            else:
                print(f'❌ File not found: {new_file}')
                return False
        else:
            print('❌ No new Excel file was created')
            return False
        
        app.root.destroy()
        
    except Exception as e:
        print(f'❌ FAILED: {e}')
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_gui_process_all_files()
    
    print('\n' + '=' * 50)
    if success:
        print('🎉 GUI PROCESS ALL FILES TEST PASSED!')
        print('The "Process All Files" button functionality works correctly.')
        print('No more ConfigReader errors!')
    else:
        print('❌ GUI PROCESS ALL FILES TEST FAILED!')
        print('There may still be issues with the process_all_files method.')
    
    print('\nYou can now use the Full Map Tool GUI with confidence.')
