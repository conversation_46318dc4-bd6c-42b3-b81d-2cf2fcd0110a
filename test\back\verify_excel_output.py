#!/usr/bin/env python3
"""
Verify the generated Excel file structure and content
"""

import os
import sys
from pathlib import Path

# Add parent directory to path to import modules
sys.path.insert(0, str(Path(__file__).parent.parent))

def verify_excel_output():
    """Verify the generated Excel file has the expected structure"""
    print('=== Excel Output Verification ===')
    
    # Find the generated Excel file
    excel_files = [f for f in os.listdir('.') if f.startswith('3AA111_full_map_') and f.endswith('.xlsx')]
    
    if not excel_files:
        print('ERROR: No generated Excel file found')
        return False
    
    excel_file = excel_files[0]  # Use the most recent one
    print(f'Found Excel file: {excel_file}')
    
    try:
        from openpyxl import load_workbook
        
        wb = load_workbook(excel_file)
        print(f'Excel file loaded successfully')
        print(f'Sheets: {wb.sheetnames}')
        
        # Check the main sheet
        if '3AA111-01-B4' in wb.sheetnames:
            ws = wb['3AA111-01-B4']
            print(f'Main sheet found: 3AA111-01-B4')
            print(f'Dimensions: {ws.max_row} rows x {ws.max_column} columns')
            
            # Check device information section
            print('\n--- Device Information ---')
            device_info_found = False
            for row in range(1, min(10, ws.max_row + 1)):
                cell_a = ws.cell(row=row, column=1).value
                cell_b = ws.cell(row=row, column=2).value
                if cell_a:
                    print(f'Row {row}: {cell_a} = {cell_b}')
                    if 'Device Info' in str(cell_a):
                        device_info_found = True
            
            if device_info_found:
                print('✅ Device information section found')
            else:
                print('⚠️ Device information section not clearly identified')
            
            # Check for data content (look for numeric data)
            print('\n--- Data Content Check ---')
            numeric_cells = 0
            for row in range(10, min(20, ws.max_row + 1)):
                for col in range(1, min(10, ws.max_column + 1)):
                    cell_value = ws.cell(row=row, column=col).value
                    if isinstance(cell_value, (int, float)) and cell_value != 0:
                        numeric_cells += 1
            
            print(f'Found {numeric_cells} numeric data cells in sample area')
            
            # Check for bin data structure
            print('\n--- Bin Data Structure ---')
            bin_data_found = False
            for row in range(1, min(50, ws.max_row + 1)):
                for col in range(1, min(20, ws.max_column + 1)):
                    cell_value = ws.cell(row=row, column=col).value
                    if cell_value and 'bin' in str(cell_value).lower():
                        print(f'Bin-related data found at ({row},{col}): {cell_value}')
                        bin_data_found = True
                        break
                if bin_data_found:
                    break
            
            if bin_data_found:
                print('✅ Bin data structure found')
            else:
                print('⚠️ Bin data structure not clearly identified')
            
            # Check file size and content density
            file_size = os.path.getsize(excel_file)
            content_density = (ws.max_row * ws.max_column) / file_size * 1000
            print(f'\n--- File Statistics ---')
            print(f'File size: {file_size} bytes')
            print(f'Content density: {content_density:.2f} cells per KB')
            
            if file_size > 5000:  # At least 5KB
                print('✅ File size indicates substantial content')
            else:
                print('⚠️ File size seems small for MAP data')
            
            wb.close()
            return True
            
        else:
            print('ERROR: Expected sheet "3AA111-01-B4" not found')
            wb.close()
            return False
            
    except Exception as e:
        print(f'ERROR: Failed to verify Excel file: {e}')
        import traceback
        traceback.print_exc()
        return False

def test_gui_integration():
    """Test that the GUI can also process the file correctly"""
    print('\n=== GUI Integration Test ===')
    
    try:
        from main_application import TSKMapApplication
        import tkinter as tk
        
        app = TSKMapApplication()
        app.root.withdraw()  # Hide window
        
        # Switch to Full Map Tool
        app.show_full_map_tool()
        full_tool = app.full_map_tool
        
        # Set up the test
        test_map_file = 'test/3AA111-01-B4.map'
        test_config = 'test/test_config.xlsx'
        
        if os.path.exists(test_map_file):
            full_tool.map_files = [test_map_file]
            full_tool.config_file_path.set(test_config)
            full_tool.update_info_display()
            
            print('✅ GUI setup completed')
            print(f'MAP files: {len(full_tool.map_files)}')
            print(f'Config file: {os.path.basename(full_tool.config_file_path.get())}')
            print(f'Filter empty areas: {full_tool.filter_empty.get()}')
            print(f'Rotation angle: {full_tool.rotation_angle.get()}')
            
            # The process_all_files method should work without errors
            print('✅ GUI is ready for processing (process_all_files method available)')
            
        app.root.destroy()
        return True
        
    except Exception as e:
        print(f'ERROR: GUI integration test failed: {e}')
        return False

if __name__ == "__main__":
    print('Full Map Tool - Complete Verification')
    print('=' * 50)
    
    excel_ok = verify_excel_output()
    gui_ok = test_gui_integration()
    
    print('\n' + '=' * 50)
    print('VERIFICATION RESULTS:')
    print(f'Excel Output: {"✅ PASS" if excel_ok else "❌ FAIL"}')
    print(f'GUI Integration: {"✅ PASS" if gui_ok else "❌ FAIL"}')
    
    if excel_ok and gui_ok:
        print('\n🎉 FULL MAP TOOL VERIFICATION SUCCESSFUL!')
        print('The tool correctly processes MAP files and generates Excel output.')
    else:
        print('\n⚠️ Some issues found in verification.')
