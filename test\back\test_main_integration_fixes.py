#!/usr/bin/env python3
"""
测试main.py集成修正
验证Bump Map Tool可以正常启动

Author: Yuribytes
Company: Chipone TE development Team
"""

import os
import sys
import tkinter as tk

# 添加父目录到路径
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, parent_dir)

def test_tool_selector_bump_map_option():
    """测试tool_selector中的Bump Map选项"""
    print("🧪 测试tool_selector中的Bump Map选项")
    print("=" * 50)
    
    try:
        from tool_selector import ToolSelector
        
        # 创建测试环境
        root = tk.Tk()
        root.withdraw()
        
        selector = ToolSelector()
        
        # 检查select_bump_map_tool方法
        if hasattr(selector, 'select_bump_map_tool'):
            print("✅ select_bump_map_tool方法存在")
        else:
            print("❌ select_bump_map_tool方法不存在")
            return False
        
        # 清理
        root.destroy()
        
        return True
        
    except Exception as e:
        print(f"❌ tool_selector测试失败: {e}")
        return False

def test_main_application_integration():
    """测试main_application集成"""
    print(f"\n🧪 测试main_application集成")
    print("=" * 50)
    
    try:
        from main_application import TSKMapApplication
        
        # 创建测试环境
        root = tk.Tk()
        root.withdraw()
        
        app = TSKMapApplication()
        
        # 检查show_bump_map_tool方法
        if hasattr(app, 'show_bump_map_tool'):
            print("✅ show_bump_map_tool方法存在")
        else:
            print("❌ show_bump_map_tool方法不存在")
            return False
        
        # 清理
        root.destroy()
        
        return True
        
    except Exception as e:
        print(f"❌ main_application测试失败: {e}")
        return False

def test_bump_map_enhanced_frame_import():
    """测试bump_map_enhanced_frame导入"""
    print(f"\n🧪 测试bump_map_enhanced_frame导入")
    print("=" * 50)
    
    try:
        from bump_map_enhanced_frame import BumpMapEnhancedFrame
        
        # 创建测试环境
        root = tk.Tk()
        root.withdraw()
        
        class MockController:
            def __init__(self):
                self.root = root
        
        controller = MockController()
        enhanced_frame = BumpMapEnhancedFrame(root, controller)
        
        # 检查核心方法
        core_methods = [
            'clear_memory', 'return_to_selector', 'exit_application',
            'update_format_preview', 'process_maps'
        ]
        
        for method in core_methods:
            if hasattr(enhanced_frame, method):
                print(f"✅ {method}方法存在")
            else:
                print(f"❌ {method}方法不存在")
                return False
        
        # 清理
        root.destroy()
        
        return True
        
    except Exception as e:
        print(f"❌ bump_map_enhanced_frame导入测试失败: {e}")
        return False

def test_format_preview_functionality():
    """测试格式预览功能"""
    print(f"\n🧪 测试格式预览功能")
    print("=" * 50)
    
    try:
        from bump_map_enhanced_frame import BumpMapEnhancedFrame
        
        # 创建测试环境
        root = tk.Tk()
        root.withdraw()
        
        class MockController:
            def __init__(self):
                self.root = root
        
        controller = MockController()
        enhanced_frame = BumpMapEnhancedFrame(root, controller)
        
        # 测试默认值
        if enhanced_frame.pass_value.get() == 0:
            print("✅ Pass默认值正确 (0)")
        else:
            print(f"❌ Pass默认值错误: {enhanced_frame.pass_value.get()}")
            return False
        
        if enhanced_frame.fail_value.get() == 59:
            print("✅ Fail默认值正确 (59)")
        else:
            print(f"❌ Fail默认值错误: {enhanced_frame.fail_value.get()}")
            return False
        
        # 测试格式预览
        enhanced_frame.pass_value.set(63)
        enhanced_frame.fail_value.set(59)
        enhanced_frame.detected_map_version.set(2)
        enhanced_frame.update_format_preview()
        
        preview_text = enhanced_frame.format_preview_label.cget("text")
        if "0000003F" in preview_text and "0000003B" in preview_text:
            print("✅ 格式预览显示正确")
        else:
            print(f"❌ 格式预览显示异常: {preview_text}")
            return False
        
        # 测试Map版本4格式
        enhanced_frame.detected_map_version.set(4)
        enhanced_frame.update_format_preview()
        
        preview_text = enhanced_frame.format_preview_label.cget("text")
        if "big-endian" in preview_text:
            print("✅ Map版本4格式显示正确")
        else:
            print(f"❌ Map版本4格式显示异常: {preview_text}")
            return False
        
        # 清理
        root.destroy()
        
        return True
        
    except Exception as e:
        print(f"❌ 格式预览功能测试失败: {e}")
        return False

def test_file_cleanup():
    """测试文件清理"""
    print(f"\n🧪 测试文件清理")
    print("=" * 50)
    
    try:
        # 检查冗余文件是否已移动
        if not os.path.exists("bump_map_advanced_frame.py"):
            print("✅ bump_map_advanced_frame.py已移动")
        else:
            print("❌ bump_map_advanced_frame.py仍在主目录")
            return False
        
        if os.path.exists("temp/bump_map_advanced_frame.py"):
            print("✅ bump_map_advanced_frame.py已移动到temp文件夹")
        else:
            print("❌ bump_map_advanced_frame.py未在temp文件夹中找到")
            return False
        
        # 检查主要文件存在
        main_files = [
            "bump_map_enhanced_frame.py",
            "bump_map_tool_frame.py",
            "main.py",
            "main_application.py",
            "tool_selector.py"
        ]
        
        for file in main_files:
            if os.path.exists(file):
                print(f"✅ {file}存在")
            else:
                print(f"❌ {file}不存在")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ 文件清理测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🧪 Main.py集成修正测试")
    print("=" * 70)
    
    test_results = []
    
    try:
        # 测试1: tool_selector中的Bump Map选项
        result1 = test_tool_selector_bump_map_option()
        test_results.append(("tool_selector Bump Map选项", result1))
        
        # 测试2: main_application集成
        result2 = test_main_application_integration()
        test_results.append(("main_application集成", result2))
        
        # 测试3: bump_map_enhanced_frame导入
        result3 = test_bump_map_enhanced_frame_import()
        test_results.append(("bump_map_enhanced_frame导入", result3))
        
        # 测试4: 格式预览功能
        result4 = test_format_preview_functionality()
        test_results.append(("格式预览功能", result4))
        
        # 测试5: 文件清理
        result5 = test_file_cleanup()
        test_results.append(("文件清理", result5))
        
        # 总结
        print("\n" + "=" * 70)
        print("🎉 集成修正测试结果总结:")
        
        passed = 0
        for test_name, result in test_results:
            status = "✅ 通过" if result else "❌ 失败"
            print(f"   {test_name}: {status}")
            if result:
                passed += 1
        
        print(f"\n📊 总体结果: {passed}/{len(test_results)} 测试通过")
        
        if passed == len(test_results):
            print("\n🎯 🎉 所有集成修正测试成功！")
            print("✅ 修正完成:")
            print("   1. ✅ tool_selector添加Bump Map Tool选项")
            print("   2. ✅ main_application引用修正")
            print("   3. ✅ bump_map_enhanced_frame正常导入")
            print("   4. ✅ bin格式显示根据map version变化")
            print("   5. ✅ 冗余文件已清理到temp文件夹")
            print("\n🚀 现在可以通过main.py正常启动Bump Map Tool！")
        else:
            print("⚠️  部分集成修正测试失败，需要进一步检查")
        
        return passed == len(test_results)
        
    except Exception as e:
        print(f"❌ 集成修正测试过程出错: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
