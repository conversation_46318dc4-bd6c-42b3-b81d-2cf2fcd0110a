# TSK/MAP File Processor Tool

## 软件信息
- **作者**: Yuribytes
- **版本**: 1.0 GUI Edition  
- **特点**: 纯 GUI 界面，无控制台窗口
- **支持系统**: Windows 7/10/11 (64-bit)

## 🚀 快速开始

### 启动软件
双击 `dist/TSK_MAP_Tool.exe` 即可启动

### 基本使用
1. 选择 **AB Map Tool**
2. 选择 **AB Compare** 模式
3. 选择 Amap 和 Bmap 文件
4. 点击 **PROCESS** 开始分析
5. 查看生成的 Excel 报告

## 📖 详细文档

- **[完整使用说明](SOFTWARE_USER_MANUAL.md)** - 详细的操作指南和功能说明
- **[快速入门指南](QUICK_START_GUIDE.md)** - 5分钟快速上手
- **[项目结构说明](PROJECT_STRUCTURE.md)** - 文件组织和维护指南

## 🎯 主要功能

- **TSK/MAP 文件解析**: 支持多种版本格式
- **AB 对比分析**: 生成 6 种分析视图
- **可视化报告**: 彩色编码的 Excel 输出
- **文件旋转支持**: 0°/90°/180°/270° 旋转
- **专业界面**: 纯 GUI 模式，用户友好

## 📊 分析结果

生成的 Excel 报告包含：
1. **Summary**: 整体统计
2. **Correlation**: Bin 跳转分析
3. **Map_compare**: 逐点对比
4. **Map_compare_full**: ⭐ 最直观的合并视图
5. **Amap/Bmap**: 原始数据
6. **Setup**: 配置信息

## 🔧 开发信息

### 核心文件
- `main.py` - 程序入口
- `ab_comparison_analyzer.py` - 分析核心
- `excel_output.py` - 报告生成
- `build_gui_exe.py` - GUI 构建脚本

### 构建命令
```bash
python build_gui_exe.py
```

### 依赖
```bash
pip install -r requirements.txt
```

## 📁 项目结构

```
tsk_map_develop_08072025/
├── dist/TSK_MAP_Tool.exe      # 最终产品 ⭐
├── *.py                       # Python 源代码
├── SOFTWARE_USER_MANUAL.md    # 详细说明
├── QUICK_START_GUIDE.md       # 快速指南
└── temp/                      # 非必要文件存档
```

## ⚠️ 注意事项

- 文件路径避免中文字符
- 大文件处理需要 8GB+ RAM
- 可能需要添加到防病毒软件白名单
- 支持 .tsk 和 .map 格式文件

## 📞 技术支持

- **作者**: Yuribytes
- **版本**: 1.0 GUI Edition
- **构建日期**: 2025-08-08

---
**TSK/MAP File Processor Tool - 专业的半导体测试数据分析工具**
