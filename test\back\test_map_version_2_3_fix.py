#!/usr/bin/env python3
"""
专门测试Map Version 2/3的字节序修复
验证003F0000格式是否正确

Author: Yuribytes
Company: Chipone TE development Team
"""

import os
import sys
import struct

# 添加父目录到路径
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, parent_dir)

from nepes_advanced_processor import NEPESAdvancedProcessor

def test_map_version_2_3_byte_order():
    """测试Map Version 2/3的字节序"""
    print("🧪 测试Map Version 2/3字节序修复")
    print("=" * 60)
    
    processor = NEPESAdvancedProcessor()
    
    # 设置为Map Version 2
    processor.map_version = 2
    
    pass_value = 63  # 0x3F
    fail_value = 59  # 0x3B
    
    print(f"📊 测试参数:")
    print(f"   Map Version: {processor.map_version}")
    print(f"   Pass Value: {pass_value} (0x{pass_value:02X})")
    print(f"   Fail Value: {fail_value} (0x{fail_value:02X})")
    
    # 测试当前实现
    pass_pattern = processor.get_binary_modification_pattern("00")
    fail_pattern = processor.get_binary_modification_pattern("XX")
    
    print(f"\n📊 当前实现结果:")
    print(f"   Pass模式: {pass_pattern.hex().upper()}")
    print(f"   Fail模式: {fail_pattern.hex().upper()}")
    
    # 期望的结果 (003F0000)
    expected_pass_bytes = bytes([0x00, 0x3F, 0x00, 0x00])
    expected_fail_bytes = bytes([0x00, 0x3B, 0x00, 0x00])
    
    print(f"\n📊 期望结果 (003F0000格式):")
    print(f"   期望Pass: {expected_pass_bytes.hex().upper()}")
    print(f"   期望Fail: {expected_fail_bytes.hex().upper()}")
    
    # 验证
    pass_correct = pass_pattern == expected_pass_bytes
    fail_correct = fail_pattern == expected_fail_bytes
    
    print(f"\n📊 验证结果:")
    print(f"   Pass匹配: {'✅' if pass_correct else '❌'}")
    print(f"   Fail匹配: {'✅' if fail_correct else '❌'}")
    
    if not pass_correct or not fail_correct:
        print(f"\n🔧 需要修复字节序问题")
        print(f"   当前使用: struct.pack('>HH', 0, value)")
        print(f"   应该使用: bytes([0x00, value, 0x00, 0x00])")
        return False
    else:
        print(f"\n✅ 字节序正确")
        return True

def test_different_values():
    """测试不同值的字节序"""
    print(f"\n🧪 测试不同值的字节序")
    print("=" * 60)
    
    processor = NEPESAdvancedProcessor()
    processor.map_version = 3  # 测试Version 3
    
    test_values = [0, 63, 128, 255]
    
    all_correct = True
    
    for value in test_values:
        print(f"\n📊 测试值: {value} (0x{value:02X})")
        
        # 模拟Pass值
        processor.pass_value = value
        pattern = processor.get_binary_modification_pattern("00")
        
        expected = bytes([0x00, value, 0x00, 0x00])
        
        print(f"   实际: {pattern.hex().upper()}")
        print(f"   期望: {expected.hex().upper()}")
        print(f"   匹配: {'✅' if pattern == expected else '❌'}")
        
        if pattern != expected:
            all_correct = False
    
    return all_correct

def fix_map_version_2_3():
    """修复Map Version 2/3的实现"""
    print(f"\n🔧 修复Map Version 2/3实现")
    print("=" * 60)
    
    print("📊 当前问题:")
    print("   struct.pack('>HH', 0, value) 产生 0000003F")
    print("   但我们需要 003F0000")
    
    print(f"\n📊 解决方案:")
    print("   使用 bytes([0x00, value, 0x00, 0x00])")
    
    # 测试解决方案
    test_value = 63
    
    current_method = struct.pack('>HH', 0, test_value)
    correct_method = bytes([0x00, test_value, 0x00, 0x00])
    
    print(f"\n📊 对比:")
    print(f"   当前方法: {current_method.hex().upper()}")
    print(f"   正确方法: {correct_method.hex().upper()}")
    
    return correct_method

def main():
    """主测试函数"""
    print("🧪 Map Version 2/3字节序修复测试")
    print("=" * 70)
    
    # 切换到测试目录
    os.chdir(os.path.dirname(os.path.abspath(__file__)))
    
    try:
        # 测试1: 当前字节序
        result1 = test_map_version_2_3_byte_order()
        
        # 测试2: 不同值
        result2 = test_different_values()
        
        # 测试3: 修复方案
        fix_map_version_2_3()
        
        print("\n" + "=" * 70)
        print("🎉 测试结果总结:")
        print(f"   字节序测试: {'✅ 通过' if result1 else '❌ 失败'}")
        print(f"   不同值测试: {'✅ 通过' if result2 else '❌ 失败'}")
        
        if not result1 or not result2:
            print(f"\n🔧 需要应用修复:")
            print(f"   在nepes_advanced_processor.py中")
            print(f"   将: return struct.pack('>HH', 0, target_value)")
            print(f"   改为: return bytes([0x00, target_value, 0x00, 0x00])")
        else:
            print(f"\n✅ 字节序已正确")
        
        return result1 and result2
        
    except Exception as e:
        print(f"❌ 测试过程出错: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
