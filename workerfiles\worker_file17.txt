任务流程：
针对Full Map Tool工具功能完善:
1，UI设计：
      ---增加tmp output（输出）路径选择，生成的默认文件格式(.tmb)（生成的逻辑，复用full map 功能，选择几个map文件就生成几个tmb文件，命名规则：map全名(包含名字+格式后缀）+时间戳+.tmb
      ---增加process tmb 按钮控件，用于执行输出tmb输出代码（默认如果不按这个代码，不执行tmb输出，代码设计不要影响process map功能，不要影响full map tool其他功能）
2，process tmb执行内容：
      ---tmb是一个可以用txt文本编辑器可以打开的文件。分三个主要部分（解析可以复用full map tool的解析逻辑），我称之为上中下三部分，每个部分由两个空格行隔开。
      ---上部：复用full map的代码逻辑，full map之前生成excel，tmb执行生成.tmb，上部是map呈现，横行从00开始标注，依次完成，纵列从00开始标注，依次完成。每颗测试数据的category对应位置展现。
      ---中部：部分复用full map代码加载（或者重新设置变量）我将每个部分内容，如何获取在下面描述中
============ Wafer Information (Nepes) ===========
Device Name: 加载自setup excel的D2单元格内容
Lot No.: 获取map文件的83~100总共18个bytes，依次转为ASICII码显示
Slot No: 获取map文件的103~104总共2个bytes，依次转为ASICII码显示
Wafer Id: 获取map文件的61~81总共21个bytes，依次转为ASICII码显示
Test program: 加载自setup excel的A2单元格内容
Test NO: 加载自setup excel的D5单元格内容
Probe card_id: 加载自setup excel的F5单元格内容
Operator Name : 
Wafer Size: 加载自setup excel的J5单元格内容
Flat: 180 degree
Test Start Time: 获取map文件的149~160总共12个bytes，字符格式获取显示
Test Finish Time: 获取map文件的161~172总共12个bytes，字符格式获取显示
Wafer Load Time: 获取map文件的173~184总共12个bytes，字符格式获取显示
Wafer Unload Time : 获取map文件的185~196总共12个bytes，字符格式获取显示
Gross Dice: 按照full map逻辑统计总共有效categories多少颗
Pass Die: 按照full map逻辑统计计算pass多少颗
Fail Die: 按照full map逻辑统计计算fail多少颗
Yield: 按照full map逻辑统计计算良率多少（pass/gross die，百分数显示，保留两位小数）
      ---下部：full map中不按照由大到小排列显示bin别，显示Cat 00~Cat 255 : 所占数量          

开发注意：
1，提供tmb文件供参考格式

上述功能开发注意事项：
     --- 主要功能不要破坏，架构做到统一
     --- GUI尽量统一，不要占用太多空间
     --- 复用代码部分，尽量做到代码统一

要求：
1，测试脚本和文件请放到test文件夹
2，生成的md说明文档，请放到temp文件夹，不能在主项目文件夹下生成
3，程序架构统一，框架统一，代码尽量简洁

测试文件说明：
1，测试map可以选择test下的019.3AD416-19-F4
