# TSK/MAP Tool 新功能使用指南

## 🎯 新功能概述

本次更新为 TSK/MAP Tool 添加了两个重要功能：

### 1. 📁 输出文件夹选择
- **AB Map Tool** 和 **Full Map Tool** 都支持自定义输出文件夹
- 可以将生成的Excel文件保存到指定位置
- 便于项目文件管理和组织

### 2. 📊 Bin_Summary 统计表格 (Full Map Tool)
- 新增专门的统计分析表格
- 横向显示每个MAP文件的bin分布
- 自动计算平均值，支持批量数据分析

---

## 📁 输出文件夹选择功能

### 使用方法

#### AB Map Tool 中使用
1. 启动 TSK/MAP Tool，选择 "AB Map Tool"
2. 在 "Output Options" 区域找到 "Output Folder" 选项
3. 点击 "Browse..." 按钮选择输出文件夹
4. 或直接在文本框中输入文件夹路径
5. 正常处理文件，Excel将保存到指定文件夹

#### Full Map Tool 中使用
1. 启动 TSK/MAP Tool，选择 "Full Map Tool"  
2. 在 "Output Options" 区域找到 "Output Folder" 选项
3. 点击 "Browse..." 按钮选择输出文件夹
4. 添加MAP文件并处理，Excel将保存到指定文件夹

### 界面示例
```
Output Options
┌─────────────────────────────────────────────────────────┐
│ ☑ Filter empty areas (recommended for large files)     │
│                                                         │
│ Output Folder: [C:\Projects\MAP_Results    ] [Browse...] │
└─────────────────────────────────────────────────────────┘
```

### 使用技巧
- **项目管理**: 为不同项目创建专门的输出文件夹
- **批量处理**: 处理多个文件时统一保存位置
- **路径记忆**: 软件会记住上次选择的文件夹路径
- **自动回退**: 如果指定文件夹不存在，自动保存到当前目录

---

## 📊 Bin_Summary 统计表格功能

### 功能特点
- **位置**: 作为Excel文件的第一个表格 (sheet)
- **命名**: 表格名称为 "Bin_Summary"
- **内容**: 包含每个MAP文件的详细bin统计
- **格式**: 专业的表格格式，易于阅读和分析

### 表格结构

#### 标题行 (第5行)
```
A列: LotID-waferID    B列: Yield(%)    C列: C00    D列: C01    ...    DD列: C128
```

#### 数据行 (第6行开始)
```
文件名1    85.50%    120    45    78    ...    0
文件名2    92.30%    98     52    65    ...    2
文件名3    88.75%    110    48    72    ...    1
(空行)
Average    88.85%    109.33  48.33  71.67  ...    1.00
```

### 数据说明

#### 列含义
- **A列 (LotID-waferID)**: 显示MAP文件对应的sheet名称
- **B列 (Yield%)**: 良品率百分比，保留两位小数
- **C列-DD列 (C00-C128)**: 各个bin的数量统计，共129列

#### 特殊行
- **Average行**: 所有数据的平均值
- **格式**: 百分比列显示为百分比格式，数值列保留两位小数

### 使用场景

#### 1. 批量质量分析
```
处理多个同批次MAP文件 → 查看Bin_Summary表格 → 分析良品率趋势
```

#### 2. 工艺参数对比
```
不同工艺参数的MAP文件 → 对比Bin_Summary数据 → 优化工艺参数
```

#### 3. 生产监控
```
定期处理生产MAP文件 → 监控Average行数据 → 及时发现异常
```

---

## 🔧 操作步骤详解

### 使用输出文件夹选择

#### 步骤1: 创建项目文件夹
```
建议文件夹结构:
Projects/
├── Project_A/
│   ├── MAP_Files/          # 原始MAP文件
│   ├── Results/            # 处理结果
│   └── Config/             # 配置文件
└── Project_B/
    ├── MAP_Files/
    ├── Results/
    └── Config/
```

#### 步骤2: 设置输出文件夹
1. 在工具界面找到 "Output Folder" 选项
2. 点击 "Browse..." 选择 `Projects/Project_A/Results/`
3. 确认路径显示正确

#### 步骤3: 处理文件
1. 选择MAP文件进行处理
2. 生成的Excel文件自动保存到指定文件夹
3. 文件名包含时间戳和旋转角度信息

### 使用Bin_Summary功能

#### 步骤1: 准备多个MAP文件
- 建议使用同批次或相关的MAP文件
- 文件数量: 2-20个为佳 (太多会导致表格过宽)

#### 步骤2: 使用Full Map Tool处理
1. 选择 "Full Map Tool"
2. 点击 "Add MAP Files..." 添加多个文件
3. 设置输出文件夹 (可选)
4. 点击 "Process All Files"

#### 步骤3: 查看Bin_Summary结果
1. 打开生成的Excel文件
2. 第一个表格就是 "Bin_Summary"
3. 重点关注:
   - **Yield%列**: 各文件良品率
   - **Average行**: 整体平均水平
   - **异常bin列**: 数值明显偏高的bin

---

## 📈 数据分析建议

### 1. 良品率分析
```
正常范围: 85% - 95%
关注点: 
- 低于85%的文件需要重点分析
- 波动超过5%的情况需要调查原因
```

### 2. Bin分布分析
```
重点关注:
- C01-C10: 常见的失效bin
- C20-C30: 特殊测试项目bin  
- C50+: 通常为严重失效bin
```

### 3. 趋势分析
```
对比方法:
- 时间序列: 按文件时间排序查看趋势
- 批次对比: 不同批次的Average行对比
- 工艺对比: 不同工艺参数的数据对比
```

---

## ⚠️ 注意事项

### 输出文件夹选择
- **权限检查**: 确保对选择的文件夹有写入权限
- **路径长度**: 避免使用过长的文件夹路径
- **特殊字符**: 文件夹名称避免使用特殊字符
- **网络路径**: 网络路径可能导致保存较慢

### Bin_Summary功能
- **文件数量**: 建议一次处理不超过50个文件
- **内存使用**: 大量文件处理时注意内存使用情况
- **数据准确性**: 确保MAP文件格式正确且完整
- **Excel兼容性**: 需要Excel 2010或更高版本打开

---

## 🔍 故障排除

### 常见问题

#### Q1: 输出文件夹选择后文件仍保存在当前目录
**解决方案**:
- 检查文件夹路径是否正确
- 确认文件夹是否存在
- 检查文件夹写入权限

#### Q2: Bin_Summary表格数据为空
**解决方案**:
- 确认MAP文件格式正确
- 检查文件是否成功处理
- 查看处理日志中的错误信息

#### Q3: Excel文件打开时显示格式错误
**解决方案**:
- 更新Excel到最新版本
- 尝试用其他Excel兼容软件打开
- 检查生成的文件是否完整

#### Q4: 处理大量文件时程序卡死
**解决方案**:
- 分批处理文件 (每次10-15个)
- 定期点击 "Clear Memory" 释放内存
- 关闭其他占用内存的程序

---

## 🎉 使用建议

### 最佳实践
1. **文件组织**: 建立清晰的文件夹结构
2. **批量处理**: 充分利用Bin_Summary功能进行批量分析
3. **定期清理**: 定期清理输出文件夹中的旧文件
4. **数据备份**: 重要的分析结果及时备份

### 工作流程建议
```
1. 创建项目文件夹
   ↓
2. 设置输出文件夹
   ↓  
3. 批量处理MAP文件
   ↓
4. 分析Bin_Summary数据
   ↓
5. 生成分析报告
```

---

*使用指南版本: 1.0*
*更新时间: 2025年8月8日*
