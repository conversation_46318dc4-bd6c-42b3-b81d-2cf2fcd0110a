#!/usr/bin/env python3
"""
Bump Map Analysis Script
Analyze "__" positions and identify out-of-bounds issues
"""

import re
import os

def analyze_bump_map():
    """Analyze bump map file for '__' positions and bounds"""
    print("🔍 Analyzing Bump Map D97127.09")
    print("=" * 50)
    
    # Change to test directory
    os.chdir('test')
    
    # Read bump map file
    with open('D97127.09', 'r', encoding='utf-8', errors='ignore') as f:
        content = f.read()
    
    # Extract RowData lines
    row_data_lines = re.findall(r'RowData:(.*)', content)
    print(f"找到 {len(row_data_lines)} 行数据")
    
    # Parse all data and count
    all_data = []
    underscore_positions = []
    zero_positions = []
    other_positions = []
    total_positions = 0
    
    for row_idx, line in enumerate(row_data_lines):
        values = [v.strip() for v in line.strip().split() if v.strip()]
        all_data.append(values)
        
        for col_idx, value in enumerate(values):
            if value == '__':
                underscore_positions.append((col_idx, row_idx))
            elif value == '00':
                zero_positions.append((col_idx, row_idx))
            else:
                other_positions.append((col_idx, row_idx, value))
            total_positions += 1
    
    rows = len(all_data)
    cols = len(all_data[0]) if all_data else 0
    
    print(f"📊 Bump Map 统计:")
    print(f"   尺寸: {rows} × {cols}")
    print(f"   理论总位置: {rows * cols}")
    print(f"   实际解析位置: {total_positions}")
    print(f"   '__' 位置数: {len(underscore_positions)}")
    print(f"   '00' 位置数: {len(zero_positions)}")
    print(f"   其他位置数: {len(other_positions)}")
    
    # Check for out-of-bounds positions
    print(f"\n🔍 检查越界情况:")
    out_of_bounds_underscore = []
    
    for x, y in underscore_positions:
        if x >= cols or y >= rows:
            out_of_bounds_underscore.append((x, y))
    
    if out_of_bounds_underscore:
        print(f"   ❌ 发现 {len(out_of_bounds_underscore)} 个越界'__'位置:")
        for x, y in out_of_bounds_underscore:
            print(f"      x={x}, y={y} (超出 {cols-1} × {rows-1})")
    else:
        print(f"   ✅ 所有'__'位置都在范围内")
    
    # Show boundary positions
    print(f"\n📍 边界'__'位置分析:")
    boundary_positions = []
    for x, y in underscore_positions:
        if x == cols-1 or y == rows-1 or x == 0 or y == 0:
            boundary_positions.append((x, y))
    
    print(f"   边界'__'位置数: {len(boundary_positions)}")
    if boundary_positions:
        print(f"   前5个边界位置: {boundary_positions[:5]}")
        print(f"   后5个边界位置: {boundary_positions[-5:]}")
    
    # Check maximum coordinates
    if underscore_positions:
        max_x = max(x for x, y in underscore_positions)
        max_y = max(y for x, y in underscore_positions)
        min_x = min(x for x, y in underscore_positions)
        min_y = min(y for x, y in underscore_positions)
        
        print(f"\n📏 '__'位置坐标范围:")
        print(f"   X: {min_x} ~ {max_x} (最大允许: {cols-1})")
        print(f"   Y: {min_y} ~ {max_y} (最大允许: {rows-1})")
        
        if max_x >= cols:
            print(f"   ❌ X坐标越界: {max_x} >= {cols}")
        if max_y >= rows:
            print(f"   ❌ Y坐标越界: {max_y} >= {rows}")
    
    return {
        'rows': rows,
        'cols': cols,
        'total_positions': total_positions,
        'underscore_count': len(underscore_positions),
        'underscore_positions': underscore_positions,
        'out_of_bounds': out_of_bounds_underscore
    }

def analyze_dummy_map():
    """Analyze dummy map dimensions"""
    print(f"\n🔍 Analyzing Dummy Map 009.NNS157-09-E4")
    print("=" * 50)
    
    # Import TSK processor
    import sys
    sys.path.append('..')
    from tsk_map_processor import TSKMapProcessor
    
    processor = TSKMapProcessor()
    success = processor.read_file('009.NNS157-09-E4')
    
    if success:
        header_success = processor.parse_file_header()
        if header_success:
            print(f"📊 Dummy Map 信息:")
            print(f"   文件大小: {len(processor.filearray)} bytes")
            print(f"   尺寸: {processor.rowsize} × {processor.columnsize}")
            print(f"   总dies: {processor.columnsize * processor.rowsize}")
            print(f"   Map version: {processor.May_version}")
            print(f"   TestResultCategory: {processor.TestResultCategory}")
            
            return {
                'rows': processor.rowsize,
                'cols': processor.columnsize,
                'total_dies': processor.columnsize * processor.rowsize,
                'TestResultCategory': processor.TestResultCategory
            }
    
    return None

def main():
    """Main analysis function"""
    print("🧪 Bump Map '__' 位置分析")
    print("=" * 70)
    
    try:
        # Analyze bump map
        bump_info = analyze_bump_map()
        
        # Analyze dummy map
        dummy_info = analyze_dummy_map()
        
        # Compare dimensions
        if dummy_info:
            print(f"\n🔄 尺寸对比:")
            print(f"   Bump Map: {bump_info['rows']} × {bump_info['cols']} = {bump_info['total_positions']}")
            print(f"   Dummy Map: {dummy_info['rows']} × {dummy_info['cols']} = {dummy_info['total_dies']}")
            
            if bump_info['total_positions'] != dummy_info['total_dies']:
                print(f"   ⚠️  尺寸不匹配!")
                
                # Check if it's off-by-one error
                if bump_info['total_positions'] == dummy_info['total_dies'] + 1:
                    print(f"   💡 可能是off-by-one错误 (多1个位置)")
                elif bump_info['total_positions'] == dummy_info['total_dies'] - 1:
                    print(f"   💡 可能是off-by-one错误 (少1个位置)")
            else:
                print(f"   ✅ 尺寸匹配")
        
        # Analyze the out-of-bounds issue
        if bump_info['out_of_bounds']:
            print(f"\n🔧 越界问题分析:")
            print(f"   越界'__'位置数: {len(bump_info['out_of_bounds'])}")
            print(f"   这些位置无法在dummy map中找到对应位置")
            print(f"   建议: 在处理时跳过这些越界位置")
        
        print(f"\n📋 处理建议:")
        print(f"   1. 在计算位置前检查边界")
        print(f"   2. 跳过越界位置，记录为警告")
        print(f"   3. 确保所有有效'__'位置都被正确处理")
        print(f"   4. 预期'__'位置数应该是 {bump_info['underscore_count']} 个")
        
        return True
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        return False

if __name__ == "__main__":
    main()
