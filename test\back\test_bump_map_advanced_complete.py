#!/usr/bin/env python3
"""
Bump Map Advanced Tool 完整功能测试
验证所有新功能的完整实现

Author: Yuribytes
Company: Chipone TE development Team
"""

import os
import sys
import struct

# 添加父目录到路径
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, parent_dir)

from nepes_advanced_processor import NEPESAdvancedProcessor

def test_all_map_versions():
    """测试所有Map Version的支持"""
    print("🧪 测试所有Map Version支持")
    print("=" * 60)
    
    processor = NEPESAdvancedProcessor()
    
    # 测试不同Map Version
    test_cases = [
        {
            'version': 4,
            'pass_value': 63,
            'expected_pass': '0000003F',
            'description': 'Map Version 4 (Big-Endian)'
        },
        {
            'version': 3,
            'pass_value': 63,
            'expected_pass': '003F0000',
            'description': 'Map Version 3 (Special)'
        },
        {
            'version': 2,
            'pass_value': 63,
            'expected_pass': '003F0000',
            'description': 'Map Version 2 (Special)'
        }
    ]
    
    all_passed = True
    
    for case in test_cases:
        print(f"\n📊 测试 {case['description']}:")
        
        processor.map_version = case['version']
        processor.pass_value = case['pass_value']
        
        pattern = processor.get_binary_modification_pattern("00")
        actual_hex = pattern.hex().upper()
        
        print(f"   Pass Value: {case['pass_value']}")
        print(f"   实际结果: {actual_hex}")
        print(f"   期望结果: {case['expected_pass']}")
        
        if actual_hex == case['expected_pass']:
            print(f"   结果: ✅ 通过")
        else:
            print(f"   结果: ❌ 失败")
            all_passed = False
    
    return all_passed

def test_dynamic_configuration():
    """测试动态配置功能"""
    print(f"\n🧪 测试动态配置功能")
    print("=" * 60)
    
    processor = NEPESAdvancedProcessor()
    processor.map_version = 4  # 使用Version 4测试
    
    # 测试不同的配置组合
    test_configs = [
        {'pass': 100, 'fail': 200, 'desc': '自定义值1'},
        {'pass': 0, 'fail': 255, 'desc': '边界值'},
        {'pass': 128, 'fail': 64, 'desc': '自定义值2'}
    ]
    
    all_passed = True
    
    for config in test_configs:
        print(f"\n📊 测试 {config['desc']}:")
        print(f"   Pass: {config['pass']}, Fail: {config['fail']}")
        
        try:
            processor.set_pass_value(config['pass'])
            processor.set_fail_value(config['fail'])
            
            # 验证设置
            if processor.pass_value == config['pass'] and processor.fail_value == config['fail']:
                # 测试二进制生成
                pass_pattern = processor.get_binary_modification_pattern("00")
                fail_pattern = processor.get_binary_modification_pattern("XX")
                
                expected_pass = struct.pack('>I', config['pass'])
                expected_fail = struct.pack('>I', config['fail'])
                
                if pass_pattern == expected_pass and fail_pattern == expected_fail:
                    print(f"   结果: ✅ 通过")
                else:
                    print(f"   结果: ❌ 二进制生成失败")
                    all_passed = False
            else:
                print(f"   结果: ❌ 值设置失败")
                all_passed = False
                
        except Exception as e:
            print(f"   结果: ❌ 异常 - {e}")
            all_passed = False
    
    return all_passed

def test_output_filename_format():
    """测试输出文件名格式"""
    print(f"\n🧪 测试输出文件名格式")
    print("=" * 60)
    
    processor = NEPESAdvancedProcessor()
    
    test_cases = [
        {
            'bump': 'D97127.09',
            'dummy': '009.NNS157-09-E4',
            'expected_parts': ['D97127', '009', 'NNS157-09-E4']
        },
        {
            'bump': 'test_bump.map',
            'dummy': 'dummy_file.tsk',
            'expected_parts': ['test_bump', 'dummy_file', 'tsk']
        }
    ]
    
    all_passed = True
    
    for case in test_cases:
        print(f"\n📊 测试文件名生成:")
        print(f"   Bump: {case['bump']}")
        print(f"   Dummy: {case['dummy']}")
        
        output_name = processor.generate_output_filename(case['bump'], case['dummy'])
        print(f"   生成: {output_name}")
        
        # 验证包含期望的部分
        contains_all = all(part in output_name for part in case['expected_parts'])
        
        # 验证时间戳格式
        import re
        has_timestamp = bool(re.search(r'\d{8}_\d{6}', output_name))
        
        if contains_all and has_timestamp:
            print(f"   结果: ✅ 通过")
        else:
            print(f"   结果: ❌ 格式不正确")
            all_passed = False
    
    return all_passed

def test_complete_processing_pipeline():
    """测试完整处理流水线"""
    print(f"\n🧪 测试完整处理流水线")
    print("=" * 60)
    
    bump_file = "D97127.09"
    dummy_file = "009.NNS157-09-E4"
    
    if not (os.path.exists(bump_file) and os.path.exists(dummy_file)):
        print("❌ 测试文件不存在，跳过完整流水线测试")
        return True
    
    processor = NEPESAdvancedProcessor()
    
    # 使用自定义配置
    custom_pass = 123
    custom_fail = 234
    
    print(f"📊 完整流水线测试:")
    print(f"   自定义Pass值: {custom_pass}")
    print(f"   自定义Fail值: {custom_fail}")
    
    # 执行完整处理
    success = processor.process_advanced_nepes(
        bump_file, dummy_file, None,  # 自动生成输出文件名
        pass_value=custom_pass,
        fail_value=custom_fail
    )
    
    if success:
        stats = processor.get_processing_stats()
        
        print(f"   ✅ 处理成功")
        print(f"   Map Version: {stats['map_version']}")
        print(f"   Pass Positions: {stats['processing_stats']['pass_positions']}")
        print(f"   Fail Positions: {stats['processing_stats']['fail_positions']}")
        print(f"   Binary Modifications: {stats['processing_stats']['binary_modifications']}")
        
        # 验证配置值
        if stats['pass_value'] == custom_pass and stats['fail_value'] == custom_fail:
            print(f"   ✅ 配置值正确")
            return True
        else:
            print(f"   ❌ 配置值不正确")
            return False
    else:
        print(f"   ❌ 处理失败")
        return False

def main():
    """主测试函数"""
    print("🧪 Bump Map Advanced Tool 完整功能测试")
    print("=" * 70)
    
    # 切换到测试目录
    os.chdir(os.path.dirname(os.path.abspath(__file__)))
    
    test_results = []
    
    try:
        # 测试1: 所有Map Version支持
        result1 = test_all_map_versions()
        test_results.append(("所有Map Version支持", result1))
        
        # 测试2: 动态配置功能
        result2 = test_dynamic_configuration()
        test_results.append(("动态配置功能", result2))
        
        # 测试3: 输出文件名格式
        result3 = test_output_filename_format()
        test_results.append(("输出文件名格式", result3))
        
        # 测试4: 完整处理流水线
        result4 = test_complete_processing_pipeline()
        test_results.append(("完整处理流水线", result4))
        
        # 总结
        print("\n" + "=" * 70)
        print("🎉 完整功能测试结果总结:")
        
        passed = 0
        for test_name, result in test_results:
            status = "✅ 通过" if result else "❌ 失败"
            print(f"   {test_name}: {status}")
            if result:
                passed += 1
        
        print(f"\n📊 总体结果: {passed}/{len(test_results)} 测试通过")
        
        if passed == len(test_results):
            print("\n🎯 🎉 所有功能完美实现！")
            print("✅ Map Version 2/3/4 完全支持")
            print("✅ 动态十进制输入功能")
            print("✅ 新的输出文件名格式")
            print("✅ 完整的处理流水线")
            print("✅ Bump Map Advanced Tool 准备就绪！")
        else:
            print("⚠️  部分功能需要进一步完善")
        
        return passed == len(test_results)
        
    except Exception as e:
        print(f"❌ 测试过程出错: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
