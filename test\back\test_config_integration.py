#!/usr/bin/env python3
"""
Test Configuration Integration - Verify config file integration functionality
"""

import sys
import os
sys.path.append('..')
from tsk_map_processor import TSKMapProcessor
from config_reader import ConfigReader
from excel_output import create_excel_output


def create_sample_config_file():
    """Create a sample configuration Excel file for testing"""
    try:
        from openpyxl import Workbook
        
        wb = Workbook()
        ws = wb.active
        
        # A2: Test Program Name
        ws['A2'] = "TestProgram_V1.0"
        
        # Headers for bin mapping (optional)
        ws['A5'] = "Bin Number"
        ws['B5'] = "Bin Name"
        
        # Sample bin mappings starting from row 6
        bin_mappings = [
            (1, "PASS"),
            (2, "PASS_GOOD"),
            (5, "FAIL_CONTACT"),
            (9, "FAIL_LEAKAGE"),
            (50, "FAIL_PARAMETRIC"),
            (127, "UNTESTED")
        ]
        
        for i, (bin_num, bin_name) in enumerate(bin_mappings, 6):
            ws[f'A{i}'] = bin_num
            ws[f'B{i}'] = bin_name
        
        # Save the file in test directory
        config_filename = os.path.join(current_dir, "sample_config.xlsx")
        wb.save(config_filename)
        wb.close()
        
        print(f"✅ Sample config file created: {config_filename}")
        return config_filename
        
    except Exception as e:
        print(f"❌ Error creating sample config file: {e}")
        return None


def test_config_reader():
    """Test ConfigReader functionality"""
    print("Testing ConfigReader")
    print("=" * 40)
    
    # Create sample config file
    config_file = create_sample_config_file()
    if not config_file:
        return False
    
    try:
        # Test config reader
        reader = ConfigReader()
        
        if reader.read_config_file(config_file):
            print(f"✅ Config file loaded successfully")
            
            # Test program name
            test_program = reader.get_test_program_name()
            print(f"  Test Program: '{test_program}'")
            
            # Bin mappings
            mappings = reader.get_all_bin_mappings()
            print(f"  Bin mappings: {len(mappings)} entries")
            
            for bin_num, bin_name in mappings.items():
                formatted = reader.get_formatted_bin_name(bin_num)
                print(f"    Bin {bin_num}: '{bin_name}' -> '{formatted}'")
            
            # Test non-existent bin
            non_existent = reader.get_formatted_bin_name(999)
            print(f"  Non-existent bin 999: '{non_existent}'")
            
            return True
        else:
            print(f"❌ Failed to load config file")
            return False
            
    except Exception as e:
        print(f"❌ Error testing config reader: {e}")
        return False
    finally:
        # Clean up
        if config_file and os.path.exists(config_file):
            os.remove(config_file)


def test_excel_integration(tsk_filepath):
    """Test Excel integration with configuration"""
    print(f"\nTesting Excel Integration with Config")
    print("=" * 50)
    
    if not os.path.exists(tsk_filepath):
        print(f"❌ TSK file not found: {tsk_filepath}")
        return False
    
    # Create sample config file
    config_file = create_sample_config_file()
    if not config_file:
        return False
    
    try:
        # Load TSK file
        processor = TSKMapProcessor()
        
        if not processor.read_file(tsk_filepath):
            print("❌ Failed to read TSK file")
            return False
        
        if not processor.parse_file_header():
            print("❌ Failed to parse TSK header")
            return False
        
        if not processor.process_die_data():
            print("❌ Failed to process die data")
            return False
        
        # Load config file
        config_reader = ConfigReader()
        if not config_reader.read_config_file(config_file):
            print("❌ Failed to load config file")
            return False
        
        print(f"✅ Both files loaded successfully")
        
        # Create Excel output with config in test directory
        output_filename = os.path.join(current_dir, "test_config_integration_output.xlsx")
        
        if create_excel_output(processor, "TestSheet", 0, output_filename, tsk_filepath, config_reader):
            print(f"✅ Excel output created: {output_filename}")
            
            if os.path.exists(output_filename):
                file_size = os.path.getsize(output_filename)
                print(f"  File size: {file_size:,} bytes")
                
                # Get statistics for verification
                bin_stats = processor.get_bin_statistics()
                
                print(f"\nExpected Excel Content:")
                print(f"  B3: Test Program = '{config_reader.get_test_program_name()}'")
                print(f"  Bin Statistics with Names:")
                
                for i, bin_data in enumerate(bin_stats[:5], 14):  # Show first 5
                    bin_number = int(bin_data['bin_name'].replace('bin', ''))
                    formatted_name = config_reader.get_formatted_bin_name(bin_number)
                    print(f"    Row {i}: '{formatted_name}' ({bin_data['quantity']} dies)")
                
                return True
            else:
                print("❌ Output file not found")
                return False
        else:
            print("❌ Failed to create Excel output")
            return False
            
    except Exception as e:
        print(f"❌ Error in Excel integration test: {e}")
        return False
    finally:
        # Clean up
        if config_file and os.path.exists(config_file):
            os.remove(config_file)


def main():
    """Main test function"""
    if len(sys.argv) < 2:
        print("Configuration Integration Test")
        print("Usage: python test_config_integration.py <tsk_file_path>")
        print("Example: python test_config_integration.py ../3AA111-01-B4")
        return
    
    tsk_filepath = sys.argv[1]
    
    print("TSK/MAP Configuration Integration Test")
    print("Testing configuration file integration functionality")
    print("=" * 70)
    
    # Test 1: Config reader functionality
    if not test_config_reader():
        print("❌ Config reader test failed")
        return
    
    # Test 2: Excel integration with config
    if not test_excel_integration(tsk_filepath):
        print("❌ Excel integration test failed")
        return
    
    print("\n" + "=" * 70)
    print("🎉 Configuration Integration Test Completed!")
    print("\nImplemented Features:")
    print("✅ Configuration file selection in GUI")
    print("✅ Excel A2 cell reading for test program name")
    print("✅ Bin name mapping from A6~AXX and B6~BXX")
    print("✅ B3 cell populated with test program name")
    print("✅ Bin statistics with formatted names: 'bin号(名称)'")
    print("✅ Fallback to 'bin号' when no mapping exists")
    
    print(f"\nConfiguration File Format:")
    print(f"  A2: Test program name")
    print(f"  A6+: Bin numbers (1, 2, 5, 9, 50, 127, ...)")
    print(f"  B6+: Bin names (PASS, PASS_GOOD, FAIL_CONTACT, ...)")
    
    print(f"\nGUI Usage:")
    print(f"1. Select configuration Excel file first")
    print(f"2. Select TSK/MAP file")
    print(f"3. Process file - bin names will be enhanced")


if __name__ == "__main__":
    main()
