#!/usr/bin/env python3
"""
验证所有工具的退出UI统一性
检查Bump Map Tool、Full Map Tool、TSK Map GUI的退出逻辑一致性

Author: Yuribytes
Company: Chipone TE development Team
"""

import os
import sys
import re

# 添加父目录到路径
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, parent_dir)

def analyze_exit_method(file_path, tool_name):
    """分析工具的退出方法实现"""
    print(f"\n🔍 分析 {tool_name} 退出方法")
    print("=" * 50)
    
    if not os.path.exists(file_path):
        print(f"❌ 文件不存在: {file_path}")
        return None
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 查找exit_application方法
    exit_method_pattern = r'def exit_application\(self\):(.*?)(?=\n    def|\nclass|\n\n\ndef|\Z)'
    match = re.search(exit_method_pattern, content, re.DOTALL)
    
    if not match:
        print(f"❌ 未找到exit_application方法")
        return None
    
    method_content = match.group(1)
    
    # 分析关键特征
    features = {
        'memory_calculation': 'memory_freed' in method_content,
        'clear_memory_call': 'clear_memory()' in method_content,
        'cleanup_popup': 'show_memory_cleanup_popup' in method_content or 'cleanup popup' in method_content.lower(),
        'delayed_quit': 'after(2100' in method_content,
        'exception_handling': 'try:' in method_content and 'except' in method_content,
        'console_logging': 'print(' in method_content
    }
    
    print(f"📊 {tool_name} 退出方法特征:")
    for feature, present in features.items():
        status = "✅" if present else "❌"
        print(f"   {status} {feature.replace('_', ' ').title()}")
    
    return features

def analyze_clear_memory_method(file_path, tool_name):
    """分析工具的内存清理方法实现"""
    print(f"\n🔍 分析 {tool_name} 内存清理方法")
    print("=" * 50)
    
    if not os.path.exists(file_path):
        print(f"❌ 文件不存在: {file_path}")
        return None
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 查找clear_memory方法
    clear_method_pattern = r'def clear_memory\(self\):(.*?)(?=\n    def|\nclass|\n\n\ndef|\Z)'
    match = re.search(clear_method_pattern, content, re.DOTALL)
    
    if not match:
        print(f"❌ 未找到clear_memory方法")
        return None
    
    method_content = match.group(1)
    
    # 分析关键特征
    features = {
        'memory_tracking': 'memory_freed' in method_content,
        'processor_counting': 'processor_count' in method_content,
        'status_update': 'status_var.set' in method_content,
        'console_feedback': 'print(' in method_content,
        'exception_handling': 'try:' in method_content and 'except' in method_content
    }
    
    print(f"📊 {tool_name} 内存清理方法特征:")
    for feature, present in features.items():
        status = "✅" if present else "❌"
        print(f"   {status} {feature.replace('_', ' ').title()}")
    
    return features

def verify_popup_method(file_path, tool_name):
    """验证内存清理弹窗方法"""
    print(f"\n🔍 验证 {tool_name} 内存清理弹窗")
    print("=" * 50)
    
    if not os.path.exists(file_path):
        print(f"❌ 文件不存在: {file_path}")
        return False
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 查找弹窗方法
    popup_patterns = [
        r'def show_memory_cleanup_popup',
        r'show_memory_cleanup_popup\(',
        r'messagebox\.showinfo.*Memory Cleanup'
    ]
    
    has_popup = any(re.search(pattern, content) for pattern in popup_patterns)
    
    if has_popup:
        print(f"✅ {tool_name} 有内存清理弹窗实现")
        return True
    else:
        print(f"❌ {tool_name} 缺少内存清理弹窗实现")
        return False

def main():
    """主验证函数"""
    print("🧪 验证所有工具的退出UI统一性")
    print("=" * 70)
    
    # 定义要检查的工具
    tools = [
        ("bump_map_tool_frame.py", "Bump Map Tool"),
        ("full_map_tool_frame.py", "Full Map Tool"),
        ("tsk_map_gui.py", "TSK Map GUI")
    ]
    
    exit_features = {}
    clear_features = {}
    popup_status = {}
    
    # 分析每个工具
    for file_path, tool_name in tools:
        # 分析退出方法
        exit_feat = analyze_exit_method(file_path, tool_name)
        if exit_feat:
            exit_features[tool_name] = exit_feat
        
        # 分析内存清理方法
        clear_feat = analyze_clear_memory_method(file_path, tool_name)
        if clear_feat:
            clear_features[tool_name] = clear_feat
        
        # 验证弹窗方法
        popup_status[tool_name] = verify_popup_method(file_path, tool_name)
    
    # 统一性检查
    print(f"\n🎯 统一性检查结果")
    print("=" * 70)
    
    if exit_features:
        print(f"\n📊 退出方法统一性:")
        all_features = set()
        for features in exit_features.values():
            all_features.update(features.keys())
        
        for feature in sorted(all_features):
            print(f"\n   {feature.replace('_', ' ').title()}:")
            all_consistent = True
            for tool_name, features in exit_features.items():
                status = "✅" if features.get(feature, False) else "❌"
                print(f"     {status} {tool_name}")
                if not features.get(feature, False):
                    all_consistent = False
            
            if all_consistent:
                print(f"     🎉 所有工具都实现了此特征")
            else:
                print(f"     ⚠️  存在不一致")
    
    if clear_features:
        print(f"\n📊 内存清理方法统一性:")
        all_features = set()
        for features in clear_features.values():
            all_features.update(features.keys())
        
        for feature in sorted(all_features):
            print(f"\n   {feature.replace('_', ' ').title()}:")
            all_consistent = True
            for tool_name, features in clear_features.items():
                status = "✅" if features.get(feature, False) else "❌"
                print(f"     {status} {tool_name}")
                if not features.get(feature, False):
                    all_consistent = False
            
            if all_consistent:
                print(f"     🎉 所有工具都实现了此特征")
            else:
                print(f"     ⚠️  存在不一致")
    
    print(f"\n📊 内存清理弹窗统一性:")
    all_have_popup = True
    for tool_name, has_popup in popup_status.items():
        status = "✅" if has_popup else "❌"
        print(f"   {status} {tool_name}")
        if not has_popup:
            all_have_popup = False
    
    if all_have_popup:
        print(f"   🎉 所有工具都有内存清理弹窗")
    else:
        print(f"   ⚠️  部分工具缺少弹窗实现")
    
    # 最终结论
    print(f"\n🎉 最终结论")
    print("=" * 70)
    
    total_tools = len(tools)
    consistent_tools = len([t for t in exit_features.values() if all(t.values())])
    
    if consistent_tools == total_tools and all_have_popup:
        print(f"✅ 完美！所有 {total_tools} 个工具的退出UI已完全统一")
        print(f"✅ 退出流程一致")
        print(f"✅ 内存清理统一")
        print(f"✅ 弹窗样式统一")
        print(f"✅ 用户体验一致")
        print(f"\n🎯 Bump Map Tool退出按钮修改成功！")
    else:
        print(f"⚠️  仍有 {total_tools - consistent_tools} 个工具需要调整")
        print(f"   建议继续优化以达到完全统一")

if __name__ == "__main__":
    main()
