#!/usr/bin/env python3
"""
Test script for the complete header border functionality in Bin_Summary sheet
Tests the complete grid lines with thick black outer border for header area (A1:L2)
"""

import os
import sys
import tempfile

# Add parent directory to path to import modules
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_complete_header_borders():
    """Test the complete grid lines and thick outer border functionality"""
    print("🖼️  Testing Complete Header Border Functionality")
    print("=" * 60)
    
    try:
        from full_map_processor import FullMapProcessor
        from config_reader import ConfigReader
        from openpyxl import Workbook
        
        # Create test setup
        processor = FullMapProcessor()
        
        # Create mock config
        config_reader = ConfigReader()
        config_reader.device_name = "CompleteGrid_TestChip"
        config_reader.vendor_name = "GridBorder_Corp"
        processor.set_config_reader(config_reader)
        
        # Create test workbook
        workbook = Workbook()
        worksheet = workbook.active
        worksheet.title = "Bin_Summary"
        
        # Create mock processors
        mock_processors = {
            "GRID_TEST-W01.map": {
                "processor": MockTSKProcessor(2000, 1900),  # 95% yield
                "file_path": "GRID_TEST-W01.map"
            },
            "GRID_TEST-W02.map": {
                "processor": MockTSKProcessor(2100, 1680),  # 80% yield
                "file_path": "GRID_TEST-W02.map"
            }
        }
        
        # Create mock sheets for data rows
        for filename in mock_processors.keys():
            sheet_name = processor._sanitize_sheet_name(os.path.splitext(filename)[0])
            mock_sheet = workbook.create_sheet(title=sheet_name)
            mock_sheet['A1'] = f"Mock data for {sheet_name}"
        
        print("1️⃣ Creating header with complete grid borders...")
        processor._create_summary_header(worksheet, mock_processors)
        
        # Verify border properties for all cells in header area
        print("2️⃣ Verifying complete grid border properties...")
        
        border_test_results = []
        
        # Test all cells in header area (A1:L2)
        for row in [1, 2]:
            for col in range(1, 13):  # A to L (columns 1-12)
                cell_ref = f"{chr(64+col)}{row}"
                cell = worksheet[cell_ref]
                border = cell.border
                
                # Expected border styles
                expected_left = 'thick' if col == 1 else 'thin'
                expected_right = 'thick' if col == 12 else 'thin'
                expected_top = 'thick' if row == 1 else 'thin'
                expected_bottom = 'thick' if row == 2 else 'thin'
                
                # Actual border styles
                actual_left = border.left.style if border.left else 'none'
                actual_right = border.right.style if border.right else 'none'
                actual_top = border.top.style if border.top else 'none'
                actual_bottom = border.bottom.style if border.bottom else 'none'
                
                # Check if borders match expectations
                borders_correct = (
                    actual_left == expected_left and
                    actual_right == expected_right and
                    actual_top == expected_top and
                    actual_bottom == expected_bottom
                )
                
                border_test_results.append({
                    'cell': cell_ref,
                    'correct': borders_correct,
                    'expected': f"L:{expected_left} R:{expected_right} T:{expected_top} B:{expected_bottom}",
                    'actual': f"L:{actual_left} R:{actual_right} T:{actual_top} B:{actual_bottom}"
                })
        
        # Report results
        correct_count = sum(1 for result in border_test_results if result['correct'])
        total_count = len(border_test_results)
        
        print(f"   Border verification: {correct_count}/{total_count} cells correct")
        
        # Show details for any incorrect cells
        incorrect_cells = [result for result in border_test_results if not result['correct']]
        if incorrect_cells:
            print("   ❌ Incorrect border cells:")
            for result in incorrect_cells[:5]:  # Show first 5 incorrect cells
                print(f"     {result['cell']}: Expected {result['expected']}, Got {result['actual']}")
        else:
            print("   ✅ All cells have correct border patterns")
        
        # Test specific corner and edge cases
        print("3️⃣ Testing specific border cases...")
        
        test_cases = [
            {'cell': 'A1', 'description': 'Top-left corner', 'expected_thick': ['left', 'top']},
            {'cell': 'L1', 'description': 'Top-right corner', 'expected_thick': ['right', 'top']},
            {'cell': 'A2', 'description': 'Bottom-left corner', 'expected_thick': ['left', 'bottom']},
            {'cell': 'L2', 'description': 'Bottom-right corner', 'expected_thick': ['right', 'bottom']},
            {'cell': 'F1', 'description': 'Top edge center', 'expected_thick': ['top']},
            {'cell': 'F2', 'description': 'Bottom edge center', 'expected_thick': ['bottom']},
            {'cell': 'B1', 'description': 'Top edge (not corner)', 'expected_thick': ['top']},
            {'cell': 'B2', 'description': 'Bottom edge (not corner)', 'expected_thick': ['bottom']},
        ]
        
        all_cases_passed = True
        for case in test_cases:
            cell = worksheet[case['cell']]
            border = cell.border
            
            # Check thick borders
            thick_borders = []
            if border.left and border.left.style == 'thick':
                thick_borders.append('left')
            if border.right and border.right.style == 'thick':
                thick_borders.append('right')
            if border.top and border.top.style == 'thick':
                thick_borders.append('top')
            if border.bottom and border.bottom.style == 'thick':
                thick_borders.append('bottom')
            
            # No need to adjust expected thick borders as they are already correctly defined
            
            case_passed = set(thick_borders) == set(case['expected_thick'])
            status = "✅" if case_passed else "❌"
            print(f"   {status} {case['description']} ({case['cell']}): {thick_borders}")
            
            if not case_passed:
                all_cases_passed = False
        
        workbook.close()
        return correct_count == total_count and all_cases_passed
        
    except Exception as e:
        print(f"❌ Error during border testing: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_visual_grid_demo():
    """Create a visual demo showing the complete grid border effect"""
    print("\n🖼️  Creating Complete Grid Border Demo")
    print("=" * 60)
    
    try:
        from full_map_processor import FullMapProcessor
        from config_reader import ConfigReader
        from openpyxl import Workbook
        
        # Create processor
        processor = FullMapProcessor()
        
        # Setup config
        config_reader = ConfigReader()
        config_reader.device_name = "GridDemo_Chip_V4.0"
        config_reader.vendor_name = "CompleteGrid_Solutions"
        processor.set_config_reader(config_reader)
        
        # Create workbook
        workbook = Workbook()
        worksheet = workbook.active
        worksheet.title = "Bin_Summary"
        
        # Mock data for demonstration
        mock_processors = {
            "GRID_DEMO-W01.map": {"processor": MockTSKProcessor(3000, 2850), "file_path": "GRID_DEMO-W01.map"},
            "GRID_DEMO-W02.map": {"processor": MockTSKProcessor(3100, 2945), "file_path": "GRID_DEMO-W02.map"},
            "GRID_DEMO-W03.map": {"processor": MockTSKProcessor(2950, 2803), "file_path": "GRID_DEMO-W03.map"},
        }
        
        # Create mock sheets for data rows
        for filename in mock_processors.keys():
            sheet_name = processor._sanitize_sheet_name(os.path.splitext(filename)[0])
            mock_sheet = workbook.create_sheet(title=sheet_name)
            mock_sheet['A1'] = f"Mock data for {sheet_name}"
        
        # Create the complete formatted sheet with grid borders
        print("1️⃣ Creating complete Bin_Summary with grid borders...")
        processor._create_bin_summary_sheet(worksheet, mock_processors, workbook)
        
        # Save sample file
        sample_file = os.path.join(tempfile.gettempdir(), "Bin_Summary_Complete_Grid_Demo.xlsx")
        workbook.save(sample_file)
        workbook.close()
        
        print(f"✅ Complete grid demo file created: {sample_file}")
        print("   Features demonstrated:")
        print("   • Complete grid lines for all header cells (A1:L2)")
        print("   • Thick black outer border around header area")
        print("   • Thin black inner grid lines between cells")
        print("   • Professional table appearance")
        print("   • Clear cell separation and structure")
        
        return True
        
    except Exception as e:
        print(f"❌ Error creating grid demo: {e}")
        return False


class MockTSKProcessor:
    """Mock TSKMapProcessor for testing"""
    def __init__(self, total_tested, pass_count):
        self.total_tested = total_tested
        self.pass_count = pass_count
    
    def get_test_statistics(self):
        return {
            'total_tested': self.total_tested,
            'pass_count': self.pass_count,
            'yield_percentage': (self.pass_count / self.total_tested * 100) if self.total_tested > 0 else 0.0
        }


def main():
    """Run all complete header border tests"""
    print("🖼️  COMPLETE HEADER BORDER TEST SUITE")
    print("=" * 80)
    
    # Run tests
    test1 = test_complete_header_borders()
    test2 = test_visual_grid_demo()
    
    # Summary
    print("\n" + "=" * 80)
    print("📋 COMPLETE BORDER TEST RESULTS")
    print("=" * 80)
    print(f"   Complete Border Functionality: {'PASS' if test1 else 'FAIL'}")
    print(f"   Visual Grid Demo: {'PASS' if test2 else 'FAIL'}")
    
    all_passed = test1 and test2
    print(f"\nOVERALL: {'ALL TESTS PASSED' if all_passed else 'SOME TESTS FAILED'}")
    
    if all_passed:
        print("\n🎉 SUCCESS! Complete header border functionality is working perfectly!")
        print("\n🖼️  COMPLETE GRID FEATURES:")
        print("   ✅ Complete grid lines for all header cells (A1:L2)")
        print("   ✅ Thick black outer border around header area")
        print("   ✅ Thin black inner grid lines between cells")
        print("   ✅ Professional table structure")
        print("   ✅ Clear visual cell separation")
        print("   ✅ Matches the specification exactly")
    else:
        print("\n❌ Some border tests failed. Please check the implementation.")
    
    return all_passed


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
