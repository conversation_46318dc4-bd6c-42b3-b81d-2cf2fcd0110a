#!/usr/bin/env python3
"""
Test script for mousewheel support in TSK/MAP File Processor Tool
Tests the mousewheel functionality in specified areas:
1. AB Map Tool - File Information area
2. Full Map Tool - Map File Selection area  
3. Full Map Tool - Processing Information area
4. Bump Map Tool - Test House Selection area (already implemented)

Author: Yuribytes
Company: Chipone TE development Team
"""

import sys
import os
import tkinter as tk
from tkinter import ttk

# Add parent directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


class MousewheelTester:
    """Test class for mousewheel functionality"""
    
    def __init__(self):
        self.root = None
        self.test_results = {}
    
    def test_ab_map_tool_mousewheel(self):
        """Test AB Map Tool File Information mousewheel"""
        print("🧪 Testing AB Map Tool - File Information Mousewheel")
        print("-" * 50)
        
        try:
            from tsk_map_gui import ABMapGUI
            
            # Create test window
            test_root = tk.Tk()
            test_root.title("AB Map Tool - Mousewheel Test")
            test_root.geometry("900x800")
            test_root.withdraw()  # Hide for automated test
            
            # Create AB Map GUI
            ab_gui = ABMapGUI(test_root)
            
            # Check if mousewheel binding exists
            info_text_bindings = ab_gui.info_text.bind()
            mousewheel_bound = "<MouseWheel>" in info_text_bindings
            
            if mousewheel_bound:
                print("✅ AB Map Tool File Information has mousewheel support")
                self.test_results["AB_Map_FileInfo"] = True
            else:
                print("❌ AB Map Tool File Information missing mousewheel support")
                self.test_results["AB_Map_FileInfo"] = False
            
            test_root.destroy()
            return mousewheel_bound
            
        except Exception as e:
            print(f"❌ AB Map Tool test failed: {e}")
            self.test_results["AB_Map_FileInfo"] = False
            return False
    
    def test_full_map_tool_mousewheel(self):
        """Test Full Map Tool mousewheel support"""
        print("\n🧪 Testing Full Map Tool - Mousewheel Support")
        print("-" * 50)
        
        try:
            from full_map_tool_frame import FullMapToolFrame
            
            # Create test window
            test_root = tk.Tk()
            test_root.title("Full Map Tool - Mousewheel Test")
            test_root.geometry("800x820")
            test_root.withdraw()  # Hide for automated test
            
            # Mock app controller
            class MockController:
                def __init__(self, root):
                    self.root = root
                def return_to_selector_with_confirmation(self, tool_name, has_unsaved):
                    return True
            
            controller = MockController(test_root)
            full_gui = FullMapToolFrame(test_root, controller)
            
            # Test 1: Map File Selection mousewheel
            files_listbox_bindings = full_gui.files_listbox.bind()
            files_mousewheel_bound = "<MouseWheel>" in files_listbox_bindings
            
            if files_mousewheel_bound:
                print("✅ Full Map Tool Map File Selection has mousewheel support")
                self.test_results["Full_Map_FileSelection"] = True
            else:
                print("❌ Full Map Tool Map File Selection missing mousewheel support")
                self.test_results["Full_Map_FileSelection"] = False
            
            # Test 2: Processing Information mousewheel
            info_text_bindings = full_gui.info_text.bind()
            info_mousewheel_bound = "<MouseWheel>" in info_text_bindings
            
            if info_mousewheel_bound:
                print("✅ Full Map Tool Processing Information has mousewheel support")
                self.test_results["Full_Map_ProcessingInfo"] = True
            else:
                print("❌ Full Map Tool Processing Information missing mousewheel support")
                self.test_results["Full_Map_ProcessingInfo"] = False
            
            test_root.destroy()
            return files_mousewheel_bound and info_mousewheel_bound
            
        except Exception as e:
            print(f"❌ Full Map Tool test failed: {e}")
            self.test_results["Full_Map_FileSelection"] = False
            self.test_results["Full_Map_ProcessingInfo"] = False
            return False
    
    def test_bump_map_tool_mousewheel(self):
        """Test Bump Map Tool mousewheel support"""
        print("\n🧪 Testing Bump Map Tool - Test House Selection Mousewheel")
        print("-" * 50)
        
        try:
            from bump_map_tool_frame import BumpMapToolFrame
            
            # Create test window
            test_root = tk.Tk()
            test_root.title("Bump Map Tool - Mousewheel Test")
            test_root.geometry("1000x800")
            test_root.withdraw()  # Hide for automated test
            
            # Mock app controller
            class MockController:
                def __init__(self, root):
                    self.root = root
                def return_to_selector_with_confirmation(self, tool_name, has_unsaved):
                    return True
            
            controller = MockController(test_root)
            bump_gui = BumpMapToolFrame(test_root, controller)
            
            # Find the canvas in the test house selection area
            # The canvas should be accessible through the frame structure
            canvas_found = False
            mousewheel_bound = False
            
            # Search for canvas widget with mousewheel binding
            def find_canvas_with_mousewheel(widget):
                nonlocal canvas_found, mousewheel_bound
                if isinstance(widget, tk.Canvas):
                    bindings = widget.bind()
                    if "<MouseWheel>" in bindings:
                        canvas_found = True
                        mousewheel_bound = True
                        return True
                
                # Recursively check children
                for child in widget.winfo_children():
                    if find_canvas_with_mousewheel(child):
                        return True
                return False
            
            find_canvas_with_mousewheel(test_root)
            
            if canvas_found and mousewheel_bound:
                print("✅ Bump Map Tool Test House Selection has mousewheel support")
                self.test_results["Bump_Map_TestHouse"] = True
            else:
                print("❌ Bump Map Tool Test House Selection missing mousewheel support")
                self.test_results["Bump_Map_TestHouse"] = False
            
            test_root.destroy()
            return mousewheel_bound
            
        except Exception as e:
            print(f"❌ Bump Map Tool test failed: {e}")
            self.test_results["Bump_Map_TestHouse"] = False
            return False
    
    def run_all_tests(self):
        """Run all mousewheel tests"""
        print("🚀 TSK/MAP File Processor Tool - Mousewheel Support Test Suite")
        print("=" * 70)
        
        # Run individual tests
        ab_result = self.test_ab_map_tool_mousewheel()
        full_result = self.test_full_map_tool_mousewheel()
        bump_result = self.test_bump_map_tool_mousewheel()
        
        # Summary
        print("\n" + "=" * 70)
        print("📋 Test Results Summary")
        print("=" * 70)
        
        total_tests = len(self.test_results)
        passed_tests = sum(self.test_results.values())
        
        for test_name, result in self.test_results.items():
            status = "✅ PASS" if result else "❌ FAIL"
            print(f"{test_name:<30} {status}")
        
        print("-" * 70)
        print(f"Total Tests: {total_tests}")
        print(f"Passed: {passed_tests}")
        print(f"Failed: {total_tests - passed_tests}")
        print(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
        
        if passed_tests == total_tests:
            print("\n🎉 All mousewheel support tests PASSED!")
            print("✅ Users can now use mouse wheel to scroll in all specified areas:")
            print("   • AB Map Tool - File Information area")
            print("   • Full Map Tool - Map File Selection area")
            print("   • Full Map Tool - Processing Information area")
            print("   • Bump Map Tool - Test House Selection area")
        else:
            print(f"\n⚠️  {total_tests - passed_tests} test(s) failed. Please check the implementation.")
        
        return passed_tests == total_tests


def main():
    """Main test function"""
    tester = MousewheelTester()
    success = tester.run_all_tests()
    
    if success:
        print("\n🎯 Mousewheel support implementation is complete and working!")
    else:
        print("\n🔧 Some mousewheel implementations need attention.")
    
    return success


if __name__ == "__main__":
    main()
