任务流程：
针对Bump map To Tsk map Tool工具进行完善:
    1，输出output路径格式修改，bump map名字_dummy map名字_时间戳+dummy map+dummy map格式不变（不是创建tsk或者map格式）（同时保留可以手动修改）
    2，增加GUI一个输入框，可以书写十进制数，这个作用是用于动态调整bump map中检索的"00"是替换成dummy map中的什么十进制数（最后转为二进制写进dummy map，转换逻辑不变）（当输入十进制需要转换为现在格式二进制：如63，map version=4下为0000003F)
    3，检查Bump map To Tsk map Tool是否有针对map version=2或者3的替换逻辑。如果没有需要增加这个处理逻辑代码
（   -----Map Version= 2或者3，
        按照例子是：Modified position x=79, y=0, 之前为00000000替换为 003F0000 (63)(新增加的功能可以通过GUI定义替换为什么）
                              Modified position x=149, y=2,之前为00000000替换为003B0000 (59) 
   )
    4, UI 请按照你的理解设计合理化。美观易用。		  
	
根据描述分开代码执行map version=2或者3和map version =4的两种改写逻辑，完成全部修改的代码设计。

上述功能开发注意事项：
     --- 其他两个功能不要破坏，代码功能不要破坏
     --- GUI尽量统一，不要占用太多空间
     --- 复用代码部分，尽量做到代码统一

要求：
1，测试脚本和文件请放到test文件夹
2，生成的md说明文档，请放到temp文件夹
3，程序架构统一，框架统一，代码尽量简洁

测试文件说明：
1，可以使用test文件夹下的D97127.09作为bump map的load
2，可以使用test文件夹下的009.NNS157-09-E4作为dummy map的文件