"""
TSK/MAP File Processor - Core Data Processing Module
Replicates the functionality of UserForm1.frm Visual Basic code
"""

import struct
import os
from typing import Tuple, List, Optional, Dict, Any


class TSKMapProcessor:
    """Core class for processing TSK/MAP binary files"""
    
    def __init__(self):
        self.May_version = 0
        self.columnsize = 0
        self.rowsize = 0
        self.RefdieX = 0
        self.RefdieY = 0
        self.TestResultStartPos = 0
        self.TestResultCategory = 0
        self.filearray = bytearray()
        self.map_data = None

        # Device information fields
        self.devicename = ""
        self.lotid = ""
        self.waferslotid = ""
        self.waferstarttime = ""
        self.waferendtime = ""

        # Memory management flags
        self._data_loaded = False
        self._file_size = 0
        
    def byte2binary(self, onebyte: int) -> str:
        """
        Convert byte value to binary string (8 bits)
        Replicates VB Byte2Binary function
        """
        binary_str = ""
        temp_byte = onebyte
        
        while temp_byte >= 2:
            temp_dbl = temp_byte % 2
            binary_str = str(temp_dbl) + binary_str
            temp_byte = (temp_byte - temp_dbl) // 2
            
        temp_dbl = temp_byte % 2
        binary_str = str(temp_dbl) + binary_str
        
        # Pad to 8 bits
        while len(binary_str) < 8:
            binary_str = "0" + binary_str
            
        return binary_str
    
    def binary2val(self, binary_str: str) -> int:
        """
        Convert binary string to decimal value
        Replicates VB Binary2Val function
        """
        result = 0
        quan = 1
        str_length = len(binary_str)
        
        for i in range(str_length):
            temp_char = binary_str[str_length - i - 1]
            if temp_char == "1":
                result += quan
            quan *= 2
            
        return result
    
    def get_binary(self, src_arr: bytearray, start_pos: int, end_pos: int) -> str:
        """
        Extract binary string from byte array between positions
        Replicates VB GetBinary function EXACTLY
        Note: VB arrays are 1-based, Python arrays are 0-based
        VB code does NOT reverse byte order - it concatenates in original order
        """
        binary_result = ""
        # VB: For i = StartPos To EndPos
        #       GetBinary = GetBinary + Byte2Binary(SrcArr(i))
        for i in range(start_pos - 1, end_pos):  # Convert to 0-based indexing
            if i < len(src_arr):
                binary_result += self.byte2binary(src_arr[i])
        return binary_result

    def binary_to_ascii_string(self, binary_str: str) -> str:
        """
        Convert binary string to ASCII string
        """
        result = ""
        # Process 8 bits at a time (1 byte = 1 ASCII character)
        for i in range(0, len(binary_str), 8):
            if i + 8 <= len(binary_str):
                byte_str = binary_str[i:i+8]
                ascii_val = self.binary2val(byte_str)
                if 32 <= ascii_val <= 126:  # Printable ASCII range
                    result += chr(ascii_val)
                elif ascii_val == 0:  # Null terminator
                    break
                else:
                    # For non-printable characters, try to convert as number
                    if ascii_val > 0:
                        result += str(ascii_val)
        return result.strip()

    def binary_to_character_string(self, binary_str: str) -> str:
        """
        Convert binary string to character string (similar to ASCII but more flexible)
        """
        return self.binary_to_ascii_string(binary_str)

    def parse_datetime_from_binary(self, binary_str: str) -> str:
        """
        Parse datetime from binary string (12 bytes: year, month, day, hour, minute, second)
        Each component is 2 bytes
        """
        if len(binary_str) < 96:  # 12 bytes * 8 bits = 96 bits
            return ""

        try:
            # Try different parsing approaches
            # Method 1: Each 2-byte component as little-endian
            components = []
            for i in range(0, 96, 16):  # Every 16 bits (2 bytes)
                if i + 16 <= len(binary_str):
                    # Try little-endian interpretation
                    byte1 = self.binary2val(binary_str[i:i+8])
                    byte2 = self.binary2val(binary_str[i+8:i+16])
                    value = byte1 + (byte2 << 8)  # Little-endian
                    components.append(value)

            if len(components) >= 6:
                year, month, day, hour, minute, second = components[:6]

                # Validate and adjust ranges
                if year > 3000 or year < 1900:
                    # Try alternative interpretation - maybe it's BCD or different format
                    return ""

                if month > 12 or month < 1:
                    month = max(1, min(12, month))
                if day > 31 or day < 1:
                    day = max(1, min(31, day))
                if hour > 23:
                    hour = hour % 24
                if minute > 59:
                    minute = minute % 60
                if second > 59:
                    second = second % 60

                # Format as Excel-compatible datetime string
                return f"{year:04d}-{month:02d}-{day:02d} {hour:02d}:{minute:02d}:{second:02d}"

            return ""
        except Exception as e:
            # If parsing fails, try to extract readable parts
            try:
                # Alternative: treat as ASCII timestamp or other format
                ascii_attempt = self.binary_to_ascii_string(binary_str)
                if len(ascii_attempt) > 8:  # If we got some readable text
                    return ascii_attempt
            except:
                pass
            return ""

    def parse_datetime_from_binary_alternative(self, binary_str: str) -> str:
        """
        Alternative datetime parsing - try different interpretations
        """
        if len(binary_str) < 96:
            return ""

        try:
            # Method: Parse as 6 components, each 2 bytes, try different interpretations
            components = []
            for i in range(0, 96, 16):
                if i + 16 <= len(binary_str):
                    # Try big-endian first
                    byte1 = self.binary2val(binary_str[i:i+8])
                    byte2 = self.binary2val(binary_str[i+8:i+16])
                    value_be = (byte1 << 8) + byte2  # Big-endian
                    components.append(value_be)

            if len(components) >= 6:
                # Try to interpret as timestamp components
                # Sometimes the format might be different, let's try various combinations

                # Check if any component looks like a reasonable year (2000-2030)
                year_candidates = [c for c in components if 2000 <= c <= 2030]
                if year_candidates:
                    # Found a reasonable year, try to build timestamp
                    year = year_candidates[0]
                    year_idx = components.index(year)

                    # Try to extract other components
                    remaining = components[:year_idx] + components[year_idx+1:]

                    # Look for reasonable month (1-12)
                    month_candidates = [c for c in remaining if 1 <= c <= 12]
                    month = month_candidates[0] if month_candidates else 1

                    # Look for reasonable day (1-31)
                    day_candidates = [c for c in remaining if 1 <= c <= 31]
                    day = day_candidates[0] if day_candidates else 1

                    # Look for reasonable hour (0-23)
                    hour_candidates = [c for c in remaining if 0 <= c <= 23]
                    hour = hour_candidates[0] if hour_candidates else 0

                    # Look for reasonable minute/second (0-59)
                    time_candidates = [c for c in remaining if 0 <= c <= 59]
                    minute = time_candidates[0] if len(time_candidates) > 0 else 0
                    second = time_candidates[1] if len(time_candidates) > 1 else 0

                    return f"{year:04d}-{month:02d}-{day:02d} {hour:02d}:{minute:02d}:{second:02d}"

                # If no reasonable year found, try to format as raw data
                if all(c < 10000 for c in components):  # All components are reasonable numbers
                    # Format as space-separated values for debugging
                    return " ".join([f"{c:04d}" for c in components])

            return ""
        except:
            return ""

    def parse_datetime_as_character(self, binary_str: str) -> str:
        """
        Parse datetime from binary string as Character format
        12 bytes total, 2 bytes per component (year, month, day, hour, minute, second)
        按照Character格式展示
        """
        if len(binary_str) < 96:  # 12 bytes * 8 bits = 96 bits
            return ""

        try:
            # Extract 6 components, each 2 bytes, as Character format
            components = []
            for i in range(0, 96, 16):  # Every 16 bits (2 bytes)
                if i + 16 <= len(binary_str):
                    # Extract 2-byte component as Character
                    component_binary = binary_str[i:i+16]
                    # Try to convert as character string first
                    char_str = self.binary_to_character_string(component_binary)
                    if char_str and char_str.strip():
                        components.append(char_str.strip())
                    else:
                        # If no readable characters, convert as number
                        value = self.binary2val(component_binary)
                        components.append(str(value))

            if len(components) >= 6:
                year, month, day, hour, minute, second = components[:6]

                # Try to format as Excel time format if components look like numbers
                try:
                    year_num = int(year) if year.isdigit() else 0
                    month_num = int(month) if month.isdigit() else 0
                    day_num = int(day) if day.isdigit() else 0
                    hour_num = int(hour) if hour.isdigit() else 0
                    minute_num = int(minute) if minute.isdigit() else 0
                    second_num = int(second) if second.isdigit() else 0

                    # Handle 2-digit year format (25 = 2025)
                    if year_num < 100:
                        year_num = 2000 + year_num

                    # Validate ranges for time format
                    if (2000 <= year_num <= 2100 and 1 <= month_num <= 12 and
                        1 <= day_num <= 31 and 0 <= hour_num <= 23 and
                        0 <= minute_num <= 59 and 0 <= second_num <= 59):
                        # Format as Excel-compatible datetime
                        return f"{year_num:04d}-{month_num:02d}-{day_num:02d} {hour_num:02d}:{minute_num:02d}:{second_num:02d}"
                except:
                    pass

                # If not valid time format, display as Character string
                return " ".join(components)

            # If less than 6 components, try to display as character string
            char_result = self.binary_to_character_string(binary_str)
            if char_result and len(char_result.strip()) > 0:
                return char_result.strip()

            return ""

        except Exception:
            # Fallback: try to display as character string
            try:
                char_result = self.binary_to_character_string(binary_str)
                if char_result and len(char_result.strip()) > 0:
                    return char_result.strip()
            except:
                pass
            return ""

    def read_file(self, filepath: str) -> bool:
        """
        Read binary file into filearray
        Returns True if successful, False otherwise
        """
        try:
            if not os.path.exists(filepath):
                return False

            with open(filepath, 'rb') as file:
                self.filearray = bytearray(file.read())
            self._file_size = len(self.filearray)
            self._data_loaded = True
            return True
        except Exception as e:
            print(f"Error reading file: {e}")
            return False
    
    def parse_file_header(self) -> bool:
        """
        Parse file header to extract metadata
        Replicates the header parsing logic from VB code
        """
        try:
            if len(self.filearray) < 220:
                return False

            # Extract header information (positions from VB code)
            self.May_version = self.binary2val(self.get_binary(self.filearray, 52, 52))
            self.columnsize = self.binary2val(self.get_binary(self.filearray, 53, 54))
            self.rowsize = self.binary2val(self.get_binary(self.filearray, 55, 56))
            self.RefdieX = self.binary2val(self.get_binary(self.filearray, 117, 118))
            self.RefdieY = self.binary2val(self.get_binary(self.filearray, 119, 120))
            self.TestResultStartPos = self.binary2val(self.get_binary(self.filearray, 217, 220))

            # Validate dimensions to prevent Excel overflow
            max_excel_cols = 16384  # Excel maximum columns (XFD)
            max_excel_rows = 1048576  # Excel maximum rows

            if self.columnsize > max_excel_cols:
                print(f"Warning: Column size ({self.columnsize}) exceeds Excel limit ({max_excel_cols})")
                print("Consider using data filtering or splitting the output")

            if self.rowsize > max_excel_rows:
                print(f"Warning: Row size ({self.rowsize}) exceeds Excel limit ({max_excel_rows})")
                print("Consider using data filtering or splitting the output")

            # Calculate TestResultCategory position
            # VB: TestResultCategory = TestResultStartPos + 1 + rowsize * columnsize * 6 + 172
            self.TestResultCategory = (self.TestResultStartPos + 1 +
                                     self.rowsize * self.columnsize * 6 + 172)

            # Parse device information
            self.parse_device_info()

            return True
        except Exception as e:
            print(f"Error parsing file header: {e}")
            return False

    def parse_device_info(self):
        """
        Parse device information from binary file
        """
        try:
            # Device Name (位置20-35, 16 bytes, Character格式) follow VB code
            device_binary = self.get_binary(self.filearray, 21, 36)
            self.devicename = self.binary_to_character_string(device_binary)

            # Lot ID (位置82-99, 18 bytes, ASCII格式)
            lot_binary = self.get_binary(self.filearray, 83, 100)
            self.lotid = self.binary_to_ascii_string(lot_binary)

            # Wafer Slot ID (位置102-103, 2 bytes, 直接转换为数字)
            wafer_binary = self.get_binary(self.filearray, 103, 104)
            wafer_val = self.binary2val(wafer_binary)
            self.waferslotid = str(wafer_val) if wafer_val > 0 else ""

            # Start Time (位置149-160, 12 bytes, Character格式)
            start_time_binary = self.get_binary(self.filearray, 149, 160)
            self.waferstarttime = self.parse_datetime_as_character(start_time_binary)

            # End Time (位置161-172, 12 bytes, Character格式)
            end_time_binary = self.get_binary(self.filearray, 161, 172)
            self.waferendtime = self.parse_datetime_as_character(end_time_binary)

        except Exception as e:
            print(f"Error parsing device info: {e}")
            # Set default values if parsing fails
            self.devicename = ""
            self.lotid = ""
            self.waferslotid = ""
            self.waferstarttime = ""
            self.waferendtime = ""
    
    def get_data_from_die_result_update(self, map_version: int, onedieresult: str,
                                      onedieresult_category: str) -> Tuple[int, int, int, bool, bool]:
        """
        Extract die data from result strings
        Replicates VB GetDataFromDieResult_update function EXACTLY
        VB Mid(string, start, length) is 1-based
        Returns: (x, y, category, is_test_die, is_pass_die)
        """
        is_test_die = False
        is_pass_die = False

        # VB: x = Binary2Val(Mid(Onedieresult, 8, 9))
        # Mid(string, 8, 9) means start at position 8, take 9 characters
        # In Python 0-based: [7:16]
        if len(onedieresult) >= 16:
            x = self.binary2val(onedieresult[7:16])
        else:
            x = 0

        # VB: y = Binary2Val(Mid(Onedieresult, 24, 9))
        # Mid(string, 24, 9) means start at position 24, take 9 characters
        # In Python 0-based: [23:32]
        if len(onedieresult) >= 32:
            y = self.binary2val(onedieresult[23:32])
        else:
            y = 0

        # Extract category based on version
        if map_version == 0:
            # VB: Category = Binary2Val(Mid(Onedieresult, 43, 6))
            if len(onedieresult) >= 48:
                category = self.binary2val(onedieresult[42:48])
            else:
                category = 0
        elif map_version == 2 or map_version == 3:
            # VB: Category = Binary2Val(Mid(OnedieresultCategory, 1, 16))
            if len(onedieresult_category) >= 16:
                category = self.binary2val(onedieresult_category[0:16])
            else:
                category = 0
        else:
            # VB: Category = Binary2Val(Mid(OnedieresultCategory, 23, 10))
            if len(onedieresult_category) >= 32:
                category = self.binary2val(onedieresult_category[22:32])
            else:
                category = 0

        # Check test and pass status
        # VB: If (Binary2Val(Mid(Onedieresult, 17, 2)) = 1) Then IsTestDie = True
        if len(onedieresult) >= 18:
            if self.binary2val(onedieresult[16:18]) == 1:
                is_test_die = True

        # VB: If (Binary2Val(Mid(Onedieresult, 1, 2)) = 1) Then IsPassDie = True
        if len(onedieresult) >= 2:
            if self.binary2val(onedieresult[0:2]) == 1:
                is_pass_die = True

        return x, y, category, is_test_die, is_pass_die
    
    def process_die_data(self, max_dies: int = 0) -> bool:
        """
        Process all die data and create 3D map array
        Replicates the main data processing loop from VB code
        max_dies: Maximum number of dies to process (0 = all, for memory management)
        """
        try:
            # Check for extremely large files
            total_dies = self.columnsize * self.rowsize
            if max_dies == 0:
                max_dies = total_dies

            if total_dies > 1000000:  # 1M dies
                print(f"Warning: Large file with {total_dies:,} dies. This may take time and memory.")
                if max_dies > 1000000:
                    print(f"Consider using max_dies parameter to limit processing.")

            # Initialize 3D map array: [row][col][data_type]
            # data_type: 0 = category, 1 = pass/fail status
            self.map_data = [[[0, 0] for _ in range(self.columnsize)]
                           for _ in range(self.rowsize)]
            
            for i in range(1, self.rowsize + 1):  # VB uses 1-based indexing
                for j in range(1, self.columnsize + 1):
                    # Calculate die result position
                    die_index = (i - 1) * self.columnsize + j - 1
                    result_start = self.TestResultStartPos + 6 * die_index + 1
                    result_end = result_start + 5  # 6 bytes total (0-5)
                    
                    onedieresult = self.get_binary(self.filearray, result_start, result_end + 1)
                    
                    # Calculate category position
                    category_start_pos = self.TestResultCategory + 4 * die_index
                    onedieresult_category = self.get_binary(self.filearray, 
                                                          category_start_pos, 
                                                          category_start_pos + 3)
                    
                    # Extract die data
                    x, y, category, is_test_die, is_pass_die = self.get_data_from_die_result_update(
                        self.May_version, onedieresult, onedieresult_category)
                    
                    if is_test_die:
                        # VB: map(y - RefdieY + 1, x - RefdieX + 1, 1) = Category
                        # Convert to 0-based indexing: map[y - RefdieY][x - RefdieX]
                        map_row = y - self.RefdieY
                        map_col = x - self.RefdieX

                        # Check bounds (same as VB implicit bounds checking)
                        if 0 <= map_row < self.rowsize and 0 <= map_col < self.columnsize:
                            self.map_data[map_row][map_col][0] = category

                            # Special handling for category 59 - always mark as fail (red)
                            if category == 59:
                                self.map_data[map_row][map_col][1] = 3  # Force fail color for category 59
                            elif is_pass_die:
                                self.map_data[map_row][map_col][1] = 4  # Pass color (green)
                            else:
                                self.map_data[map_row][map_col][1] = 3  # Fail color (red)
            
            return True
        except Exception as e:
            print(f"Error processing die data: {e}")
            return False

    def clear_memory(self):
        """
        Clear large memory structures to free up RAM
        Call this after processing is complete to prevent memory accumulation
        """
        try:
            # Clear large data structures
            if self.filearray:
                self._file_size = len(self.filearray)
                self.filearray = bytearray()

            if self.map_data:
                # Calculate approximate memory usage before clearing
                rows = len(self.map_data)
                cols = len(self.map_data[0]) if rows > 0 else 0
                estimated_mb = (rows * cols * 2 * 4) / (1024 * 1024)  # 2 ints per cell, 4 bytes per int

                self.map_data = None
                print(f"Memory cleared: ~{estimated_mb:.1f} MB freed")

            self._data_loaded = False

        except Exception as e:
            print(f"Warning: Error during memory cleanup: {e}")

    def get_memory_usage_mb(self) -> float:
        """
        Estimate current memory usage in MB
        """
        try:
            memory_mb = 0.0

            # File array memory
            if self.filearray:
                memory_mb += len(self.filearray) / (1024 * 1024)

            # Map data memory
            if self.map_data:
                rows = len(self.map_data)
                cols = len(self.map_data[0]) if rows > 0 else 0
                memory_mb += (rows * cols * 2 * 4) / (1024 * 1024)  # 2 ints per cell

            return memory_mb

        except Exception:
            return 0.0

    def is_data_loaded(self) -> bool:
        """Check if data is currently loaded in memory"""
        return self._data_loaded and (self.filearray or self.map_data)
    
    def get_rotated_data(self, rotation_angle: int) -> List[List[Tuple[int, int]]]:
        """
        Get map data rotated by specified angle
        rotation_angle: 0, 90, 180, or 270 degrees
        Returns: List of [row][col] with (category, color) tuples
        """
        if self.map_data is None:
            return []
        
        if rotation_angle == 0:
            # No rotation - original orientation
            return [[(self.map_data[i][j][0], self.map_data[i][j][1]) 
                    for j in range(self.columnsize)] 
                   for i in range(self.rowsize)]
        
        elif rotation_angle == 90:
            # 90 degree clockwise rotation
            rotated = []
            for j in range(self.columnsize):
                row = []
                for i in range(self.rowsize - 1, -1, -1):
                    row.append((self.map_data[i][j][0], self.map_data[i][j][1]))
                rotated.append(row)
            return rotated
        
        elif rotation_angle == 180:
            # 180 degree rotation
            rotated = []
            for i in range(self.rowsize - 1, -1, -1):
                row = []
                for j in range(self.columnsize - 1, -1, -1):
                    row.append((self.map_data[i][j][0], self.map_data[i][j][1]))
                rotated.append(row)
            return rotated
        
        elif rotation_angle == 270:
            # 270 degree clockwise rotation
            rotated = []
            for j in range(self.columnsize - 1, -1, -1):
                row = []
                for i in range(self.rowsize):
                    row.append((self.map_data[i][j][0], self.map_data[i][j][1]))
                rotated.append(row)
            return rotated
        
        else:
            raise ValueError("Invalid rotation angle. Must be 0, 90, 180, or 270.")
    
    def get_active_data_bounds(self) -> Tuple[int, int, int, int]:
        """
        Get the bounds of the area containing actual test data
        Returns: (min_row, max_row, min_col, max_col) - 0-based indices
        """
        if self.map_data is None:
            return 0, 0, 0, 0

        min_row, max_row = self.rowsize, -1
        min_col, max_col = self.columnsize, -1

        for i in range(self.rowsize):
            for j in range(self.columnsize):
                if self.map_data[i][j][1] != 0:  # Has test data
                    min_row = min(min_row, i)
                    max_row = max(max_row, i)
                    min_col = min(min_col, j)
                    max_col = max(max_col, j)

        # If no data found, return full range
        if max_row == -1:
            return 0, self.rowsize - 1, 0, self.columnsize - 1

        return min_row, max_row, min_col, max_col

    def get_filtered_rotated_data(self, rotation_angle: int, filter_empty: bool = True) -> List[List[Tuple[int, int]]]:
        """
        Get rotated data with optional filtering of empty areas
        """
        if not filter_empty:
            return self.get_rotated_data(rotation_angle)

        # Get active data bounds
        min_row, max_row, min_col, max_col = self.get_active_data_bounds()

        if self.map_data is None:
            return []

        # Extract only the active area
        filtered_data = []
        for i in range(min_row, max_row + 1):
            row = []
            for j in range(min_col, max_col + 1):
                row.append((self.map_data[i][j][0], self.map_data[i][j][1]))
            filtered_data.append(row)

        # Create temporary processor with filtered data for rotation
        temp_processor = TSKMapProcessor()
        temp_processor.rowsize = max_row - min_row + 1
        temp_processor.columnsize = max_col - min_col + 1
        temp_processor.map_data = filtered_data

        return temp_processor.get_rotated_data(rotation_angle)

    def get_test_statistics(self) -> Dict[str, Any]:
        """
        Calculate test statistics from map data
        Returns: dict with total_tested, pass_count, yield_percentage
        """
        if self.map_data is None:
            return {'total_tested': 0, 'pass_count': 0, 'yield_percentage': 0.0}

        total_tested = 0
        pass_count = 0

        for i in range(self.rowsize):
            for j in range(self.columnsize):
                color = self.map_data[i][j][1]
                if color != 0:  # Has test data
                    total_tested += 1
                    if color == 4:  # Pass color (green)
                        pass_count += 1

        # Calculate yield percentage
        yield_percentage = (pass_count / total_tested * 100) if total_tested > 0 else 0.0

        return {
            'total_tested': total_tested,
            'pass_count': pass_count,
            'yield_percentage': yield_percentage
        }

    def get_bin_statistics(self, sort_by_quantity: bool = True) -> List[Dict[str, Any]]:
        """
        Calculate bin statistics from map data
        Returns: List of dict with bin_name, quantity, yield_percentage

        Args:
            sort_by_quantity: If True, sort by quantity (descending). If False, sort by bin number (ascending from 0 to 255)
        """
        if self.map_data is None:
            return []

        # Count each category (bin)
        bin_counts = {}
        total_tested = 0

        for i in range(self.rowsize):
            for j in range(self.columnsize):
                category = self.map_data[i][j][0]
                color = self.map_data[i][j][1]

                if color != 0:  # Has test data
                    total_tested += 1
                    bin_name = f"bin{category}"
                    bin_counts[bin_name] = bin_counts.get(bin_name, 0) + 1

        # Convert to list and calculate yield percentages
        bin_stats = []

        if sort_by_quantity:
            # Original behavior: only include bins that have data, sorted by quantity (descending)
            for bin_name, quantity in bin_counts.items():
                yield_percentage = (quantity / total_tested * 100) if total_tested > 0 else 0.0
                bin_stats.append({
                    'bin_name': bin_name,
                    'quantity': quantity,
                    'yield_percentage': yield_percentage
                })
            # Sort by quantity (descending)
            bin_stats.sort(key=lambda x: x['quantity'], reverse=True)
        else:
            # New behavior: include all bins from 0 to 255, sorted by bin number (ascending)
            for bin_number in range(256):  # bin0 to bin255
                bin_name = f"bin{bin_number}"
                quantity = bin_counts.get(bin_name, 0)  # 0 if bin has no fail data
                yield_percentage = (quantity / total_tested * 100) if total_tested > 0 else 0.0
                bin_stats.append({
                    'bin_name': bin_name,
                    'quantity': quantity,
                    'yield_percentage': yield_percentage
                })

        return bin_stats

    def quick_analysis(self) -> Dict[str, Any]:
        """
        Perform quick analysis without processing all die data
        Useful for very large files
        """
        info = self.get_file_info()

        # Sample a small area to estimate data density
        sample_size = min(100, self.rowsize, self.columnsize)
        tested_dies = 0
        passed_dies = 0

        try:
            for i in range(1, min(sample_size + 1, self.rowsize + 1)):
                for j in range(1, min(sample_size + 1, self.columnsize + 1)):
                    die_index = (i - 1) * self.columnsize + j - 1
                    result_start = self.TestResultStartPos + 6 * die_index + 1
                    result_end = result_start + 5

                    if result_end < len(self.filearray):
                        onedieresult = self.get_binary(self.filearray, result_start, result_end + 1)

                        # Quick check for test status
                        if len(onedieresult) >= 18:
                            is_test_die = self.binary2val(onedieresult[16:18]) == 1
                            is_pass_die = self.binary2val(onedieresult[0:2]) == 1

                            if is_test_die:
                                tested_dies += 1
                                if is_pass_die:
                                    passed_dies += 1

            # Estimate statistics
            sample_total = min(sample_size * sample_size, self.rowsize * self.columnsize)
            if sample_total > 0:
                test_rate = tested_dies / sample_total
                pass_rate = passed_dies / tested_dies if tested_dies > 0 else 0

                info['sample_analysis'] = {
                    'sample_size': f"{sample_size}x{sample_size}",
                    'tested_dies': tested_dies,
                    'passed_dies': passed_dies,
                    'test_rate': f"{test_rate:.1%}",
                    'pass_rate': f"{pass_rate:.1%}",
                    'estimated_tested_dies': int(info['total_dies'] * test_rate),
                    'estimated_passed_dies': int(info['total_dies'] * test_rate * pass_rate)
                }

        except Exception as e:
            info['sample_analysis'] = {'error': str(e)}

        return info

    def get_file_info(self) -> Dict[str, Any]:
        """
        Get file information dictionary
        """
        info = {
            'version': self.May_version,
            'columnsize': self.columnsize,
            'rowsize': self.rowsize,
            'RefdieX': self.RefdieX,
            'RefdieY': self.RefdieY,
            'TestResultStartPos': self.TestResultStartPos,
            'TestResultCategory': self.TestResultCategory
        }

        # Add Excel compatibility warnings
        max_excel_cols = 16384
        max_excel_rows = 1048576

        info['excel_compatible'] = (self.columnsize <= max_excel_cols and
                                   self.rowsize <= max_excel_rows)
        info['exceeds_excel_cols'] = self.columnsize > max_excel_cols
        info['exceeds_excel_rows'] = self.rowsize > max_excel_rows

        # Add detailed size information
        info['total_dies'] = self.columnsize * self.rowsize

        # Add active data bounds if data is processed
        if self.map_data is not None:
            min_row, max_row, min_col, max_col = self.get_active_data_bounds()
            info['active_bounds'] = {
                'min_row': min_row,
                'max_row': max_row,
                'min_col': min_col,
                'max_col': max_col,
                'active_rows': max_row - min_row + 1,
                'active_cols': max_col - min_col + 1
            }

        return info
