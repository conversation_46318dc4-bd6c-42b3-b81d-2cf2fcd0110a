#!/usr/bin/env python3
"""
Database Configuration Reader
Replaces Excel-based configuration with database-driven configuration
Supports automatic filtering and dynamic configuration loading
"""

import sqlite3
import os
from typing import Dict, List, Optional, Tuple
from datetime import datetime
import json


class DatabaseConfigReader:
    """Database-driven configuration reader with filtering capabilities"""
    
    def __init__(self, db_path: str = "config_database.db"):
        self.db_path = db_path
        self.connection = None
        
        # Configuration data (same structure as Excel reader)
        self.test_program_name = ""
        self.bin_name_mapping = {}  # {bin_number: bin_name}
        self.device_name = ""
        self.vendor_name = ""
        self.tester_name = ""
        self.probe_card_no = ""
        self.test_flow = ""
        self.wafer_size = ""
        self.operator_name = ""
        
        # Additional database-specific fields
        self.config_id = None
        self.config_version = ""
        self.last_updated = None
        
        self._init_database()
    
    def _init_database(self):
        """Initialize database with required tables"""
        try:
            self.connection = sqlite3.connect(self.db_path)
            self.connection.row_factory = sqlite3.Row  # Enable column access by name
            
            # Create tables if they don't exist
            self._create_tables()
            
        except Exception as e:
            print(f"Error initializing database: {e}")
            self.connection = None
    
    def _create_tables(self):
        """Create database tables for configuration storage"""
        cursor = self.connection.cursor()
        
        # Main configuration table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS test_configurations (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                config_name VARCHAR(100) NOT NULL UNIQUE,
                test_program_name VARCHAR(200),
                device_name VARCHAR(100),
                vendor_name VARCHAR(100),
                tester_name VARCHAR(100),
                probe_card_no VARCHAR(100),
                test_flow VARCHAR(50),
                wafer_size VARCHAR(50),
                operator_name VARCHAR(100),
                version VARCHAR(20) DEFAULT '1.0',
                is_active BOOLEAN DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Bin mapping table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS bin_mappings (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                config_id INTEGER,
                bin_number INTEGER NOT NULL,
                bin_name VARCHAR(200),
                bin_description TEXT,
                is_active BOOLEAN DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (config_id) REFERENCES test_configurations (id),
                UNIQUE(config_id, bin_number)
            )
        ''')
        
        # Configuration filters table (for automatic filtering)
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS config_filters (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                filter_name VARCHAR(100) NOT NULL,
                filter_type VARCHAR(50) NOT NULL,  -- 'device', 'test_flow', 'vendor', etc.
                filter_value VARCHAR(200) NOT NULL,
                priority INTEGER DEFAULT 0,
                is_active BOOLEAN DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Configuration usage log
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS config_usage_log (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                config_id INTEGER,
                used_by VARCHAR(100),
                usage_context TEXT,
                used_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (config_id) REFERENCES test_configurations (id)
            )
        ''')
        
        self.connection.commit()
    
    def migrate_from_excel(self, excel_file_path: str, config_name: str) -> bool:
        """Migrate existing Excel configuration to database"""
        if not os.path.exists(excel_file_path):
            print(f"Excel file not found: {excel_file_path}")
            return False
        
        try:
            # Import the original Excel reader to read the file
            from config_reader import ConfigReader
            
            excel_reader = ConfigReader()
            if not excel_reader.read_config_file(excel_file_path):
                print("Failed to read Excel configuration")
                return False
            
            # Insert configuration into database
            cursor = self.connection.cursor()
            
            # Insert main configuration
            cursor.execute('''
                INSERT OR REPLACE INTO test_configurations 
                (config_name, test_program_name, device_name, vendor_name, 
                 tester_name, probe_card_no, test_flow, wafer_size, operator_name)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                config_name,
                excel_reader.get_test_program_name(),
                excel_reader.get_device_name(),
                excel_reader.get_vendor_name(),
                excel_reader.get_tester_name(),
                excel_reader.get_probe_card_no(),
                excel_reader.get_test_flow(),
                excel_reader.get_wafer_size(),
                excel_reader.get_operator_name()
            ))
            
            config_id = cursor.lastrowid
            
            # Insert bin mappings
            bin_mappings = excel_reader.get_all_bin_mappings()
            for bin_number, bin_name in bin_mappings.items():
                cursor.execute('''
                    INSERT OR REPLACE INTO bin_mappings 
                    (config_id, bin_number, bin_name)
                    VALUES (?, ?, ?)
                ''', (config_id, bin_number, bin_name))
            
            self.connection.commit()
            
            print(f"Successfully migrated Excel config '{config_name}' to database")
            print(f"Config ID: {config_id}, Bin mappings: {len(bin_mappings)}")
            
            return True
            
        except Exception as e:
            print(f"Error migrating Excel to database: {e}")
            if self.connection:
                self.connection.rollback()
            return False
    
    def load_config_by_name(self, config_name: str) -> bool:
        """Load configuration by name"""
        try:
            cursor = self.connection.cursor()
            
            # Get main configuration
            cursor.execute('''
                SELECT * FROM test_configurations 
                WHERE config_name = ? AND is_active = 1
            ''', (config_name,))
            
            config_row = cursor.fetchone()
            if not config_row:
                print(f"Configuration '{config_name}' not found")
                return False
            
            # Load main configuration data
            self.config_id = config_row['id']
            self.test_program_name = config_row['test_program_name'] or ""
            self.device_name = config_row['device_name'] or ""
            self.vendor_name = config_row['vendor_name'] or ""
            self.tester_name = config_row['tester_name'] or ""
            self.probe_card_no = config_row['probe_card_no'] or ""
            self.test_flow = config_row['test_flow'] or ""
            self.wafer_size = config_row['wafer_size'] or ""
            self.operator_name = config_row['operator_name'] or ""
            self.config_version = config_row['version'] or ""
            self.last_updated = config_row['updated_at']
            
            # Load bin mappings
            cursor.execute('''
                SELECT bin_number, bin_name FROM bin_mappings 
                WHERE config_id = ? AND is_active = 1
                ORDER BY bin_number
            ''', (self.config_id,))
            
            self.bin_name_mapping = {}
            for row in cursor.fetchall():
                if row['bin_name']:  # Only add if name is not empty
                    self.bin_name_mapping[row['bin_number']] = row['bin_name']
            
            # Log usage
            self._log_config_usage(config_name, "load_by_name")
            
            print(f"Configuration '{config_name}' loaded: {len(self.bin_name_mapping)} bin mappings")
            return True
            
        except Exception as e:
            print(f"Error loading configuration: {e}")
            return False
    
    def auto_select_config(self, filters: Dict[str, str]) -> Optional[str]:
        """
        Automatically select configuration based on filters
        filters: {'device_name': 'ICNA3509WAA-P1-P', 'test_flow': 'CP1', ...}
        """
        try:
            cursor = self.connection.cursor()
            
            # Build dynamic query based on filters
            where_conditions = ["is_active = 1"]
            params = []
            
            for field, value in filters.items():
                if field in ['device_name', 'vendor_name', 'test_flow', 'tester_name', 
                           'probe_card_no', 'wafer_size', 'operator_name']:
                    where_conditions.append(f"{field} = ?")
                    params.append(value)
            
            query = f'''
                SELECT config_name, 
                       COUNT(*) as match_score,
                       updated_at
                FROM test_configurations 
                WHERE {' AND '.join(where_conditions)}
                GROUP BY config_name
                ORDER BY match_score DESC, updated_at DESC
                LIMIT 1
            '''
            
            cursor.execute(query, params)
            result = cursor.fetchone()
            
            if result:
                config_name = result['config_name']
                print(f"Auto-selected configuration: '{config_name}' (match score: {result['match_score']})")
                return config_name
            else:
                print("No matching configuration found for auto-selection")
                return None
                
        except Exception as e:
            print(f"Error in auto-selection: {e}")
            return None
    
    def load_config_auto(self, filters: Dict[str, str]) -> bool:
        """Load configuration automatically based on filters"""
        config_name = self.auto_select_config(filters)
        if config_name:
            return self.load_config_by_name(config_name)
        return False
    
    def list_available_configs(self) -> List[Dict]:
        """List all available configurations"""
        try:
            cursor = self.connection.cursor()
            cursor.execute('''
                SELECT config_name, device_name, test_flow, vendor_name, 
                       version, updated_at, 
                       (SELECT COUNT(*) FROM bin_mappings WHERE config_id = test_configurations.id) as bin_count
                FROM test_configurations 
                WHERE is_active = 1
                ORDER BY updated_at DESC
            ''')
            
            configs = []
            for row in cursor.fetchall():
                configs.append({
                    'config_name': row['config_name'],
                    'device_name': row['device_name'],
                    'test_flow': row['test_flow'],
                    'vendor_name': row['vendor_name'],
                    'version': row['version'],
                    'updated_at': row['updated_at'],
                    'bin_count': row['bin_count']
                })
            
            return configs
            
        except Exception as e:
            print(f"Error listing configurations: {e}")
            return []
    
    def _log_config_usage(self, config_name: str, context: str):
        """Log configuration usage for analytics"""
        try:
            cursor = self.connection.cursor()
            cursor.execute('''
                INSERT INTO config_usage_log (config_id, used_by, usage_context)
                VALUES (?, ?, ?)
            ''', (self.config_id, "TMB_Processor", context))
            self.connection.commit()
        except:
            pass  # Don't fail if logging fails
    
    # Same interface methods as original ConfigReader
    def get_test_program_name(self) -> str:
        return self.test_program_name
    
    def get_device_name(self) -> str:
        return self.device_name
    
    def get_vendor_name(self) -> str:
        return self.vendor_name
    
    def get_tester_name(self) -> str:
        return self.tester_name
    
    def get_probe_card_no(self) -> str:
        return self.probe_card_no
    
    def get_test_flow(self) -> str:
        return self.test_flow
    
    def get_wafer_size(self) -> str:
        return self.wafer_size
    
    def get_operator_name(self) -> str:
        return self.operator_name
    
    def get_bin_name(self, bin_number: int) -> str:
        return self.bin_name_mapping.get(bin_number, "")
    
    def get_formatted_bin_name(self, bin_number: int) -> str:
        bin_name = self.get_bin_name(bin_number)
        if bin_name:
            return f"{bin_number}({bin_name})"
        else:
            return f"{bin_number}"
    
    def get_all_bin_mappings(self) -> Dict[int, str]:
        return self.bin_name_mapping.copy()
    
    def has_config_loaded(self) -> bool:
        return bool(self.test_program_name or self.bin_name_mapping)
    
    def close(self):
        """Close database connection"""
        if self.connection:
            self.connection.close()
            self.connection = None
    
    def __del__(self):
        """Cleanup database connection"""
        self.close()


def test_database_config():
    """Test function for DatabaseConfigReader"""
    print("Testing DatabaseConfigReader...")
    
    # Initialize database reader
    db_reader = DatabaseConfigReader("test_config.db")
    
    # Test migration from Excel
    excel_file = "test/CP1_program_bin.xlsx"
    if os.path.exists(excel_file):
        print(f"\n1. Migrating from Excel: {excel_file}")
        if db_reader.migrate_from_excel(excel_file, "CP1_Test_Config"):
            print("✅ Migration successful")
        else:
            print("❌ Migration failed")
    
    # Test listing configurations
    print("\n2. Available configurations:")
    configs = db_reader.list_available_configs()
    for config in configs:
        print(f"  - {config['config_name']}: {config['device_name']} ({config['bin_count']} bins)")
    
    # Test auto-selection
    print("\n3. Auto-selection test:")
    filters = {
        'device_name': 'ICNA3509WAA-P1-P',
        'test_flow': 'CP1'
    }
    if db_reader.load_config_auto(filters):
        print(f"✅ Auto-loaded config: {db_reader.get_test_program_name()}")
        print(f"   Device: {db_reader.get_device_name()}")
        print(f"   Test Flow: {db_reader.get_test_flow()}")
        print(f"   Bin mappings: {len(db_reader.get_all_bin_mappings())}")
    else:
        print("❌ Auto-selection failed")
    
    # Test manual loading
    print("\n4. Manual loading test:")
    if db_reader.load_config_by_name("CP1_Test_Config"):
        print("✅ Manual loading successful")
        
        # Test bin name formatting
        test_bins = [127, 115, 64, 999]
        for bin_num in test_bins:
            formatted = db_reader.get_formatted_bin_name(bin_num)
            print(f"   Bin {bin_num}: {formatted}")
    else:
        print("❌ Manual loading failed")
    
    db_reader.close()


if __name__ == "__main__":
    test_database_config()
