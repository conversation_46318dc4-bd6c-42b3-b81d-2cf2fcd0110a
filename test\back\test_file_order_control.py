#!/usr/bin/env python3
"""
Test File Order Control - Verify the file order adjustment functionality
"""

import sys
import os
import shutil
import tkinter as tk

# Add parent directory to path
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.insert(0, parent_dir)

from full_map_gui import FullMapGUI
from full_map_processor import FullMapProcessor


def create_test_files_with_different_names():
    """Create test files with different names for order testing"""
    original_file = os.path.join(parent_dir, "3AA111-01-B4")
    
    if not os.path.exists(original_file):
        print(f"❌ Original file not found: {original_file}")
        return []
    
    test_files = []
    test_names = [
        "AAA111-01-B1.map",  # Should generate AAA111_* filename
        "BBB222-02-C2.map",  # Should generate BBB222_* filename  
        "CCC333-03-D3.map"   # Should generate CCC333_* filename
    ]
    
    for test_name in test_names:
        test_path = os.path.join(current_dir, test_name)
        try:
            shutil.copy2(original_file, test_path)
            test_files.append(test_path)
            print(f"✅ Created test file: {test_name}")
        except Exception as e:
            print(f"❌ Failed to create {test_name}: {e}")
    
    return test_files


def test_filename_generation_logic():
    """Test the filename generation logic with different file orders"""
    print("Testing Filename Generation Logic")
    print("=" * 50)
    
    test_files = create_test_files_with_different_names()
    
    if len(test_files) < 3:
        print("❌ Need at least 3 test files")
        return False
    
    try:
        processor = FullMapProcessor()
        
        # Test different orders
        test_orders = [
            (test_files, "AAA111"),  # Original order
            ([test_files[1], test_files[0], test_files[2]], "BBB222"),  # BBB first
            ([test_files[2], test_files[1], test_files[0]], "CCC333"),  # CCC first
        ]
        
        print(f"Testing filename generation with different file orders:")
        
        all_correct = True
        
        for i, (file_order, expected_base) in enumerate(test_orders, 1):
            print(f"\nTest {i}: First file = {os.path.basename(file_order[0])}")
            
            # Generate filename
            filename = processor._generate_output_filename(file_order, len(file_order))
            
            print(f"  Generated: {filename}")
            print(f"  Expected base: {expected_base}")
            
            # Check if filename starts with expected base
            if filename.startswith(expected_base):
                print(f"  ✅ Correct base name")
            else:
                print(f"  ❌ Incorrect base name")
                all_correct = False
            
            # Check timestamp format
            import re
            if re.search(r'_\d{8}_\d{6}\.xlsx$', filename):
                print(f"  ✅ Correct timestamp format")
            else:
                print(f"  ❌ Incorrect timestamp format")
                all_correct = False
        
        return all_correct
        
    except Exception as e:
        print(f"❌ Error in filename generation test: {e}")
        return False
    finally:
        # Clean up test files
        for test_file in test_files:
            try:
                if os.path.exists(test_file):
                    os.remove(test_file)
            except:
                pass


def test_gui_file_order_methods():
    """Test the GUI file order manipulation methods"""
    print(f"\nTesting GUI File Order Methods")
    print("=" * 40)
    
    test_files = create_test_files_with_different_names()
    
    if len(test_files) < 3:
        print("❌ Need at least 3 test files")
        return False
    
    try:
        # Create GUI instance (without showing window)
        root = tk.Tk()
        root.withdraw()  # Hide the window
        
        gui = FullMapGUI(root)
        
        # Add test files to GUI
        gui.map_files = test_files.copy()
        gui.refresh_file_listbox()
        
        print(f"Initial file order:")
        for i, file_path in enumerate(gui.map_files):
            print(f"  {i+1}. {os.path.basename(file_path)}")
        
        # Test move up functionality
        print(f"\nTesting Move Up:")
        
        # Select second file (index 1) and move up
        gui.files_listbox.selection_set(1)
        original_second = gui.map_files[1]
        gui.move_file_up()
        
        if gui.map_files[0] == original_second:
            print(f"  ✅ Move up successful")
            print(f"    New first file: {os.path.basename(gui.map_files[0])}")
        else:
            print(f"  ❌ Move up failed")
            return False
        
        # Test move down functionality
        print(f"\nTesting Move Down:")
        
        # Select first file (index 0) and move down
        gui.files_listbox.selection_set(0)
        original_first = gui.map_files[0]
        gui.move_file_down()
        
        if gui.map_files[1] == original_first:
            print(f"  ✅ Move down successful")
            print(f"    New first file: {os.path.basename(gui.map_files[0])}")
        else:
            print(f"  ❌ Move down failed")
            return False
        
        # Test filename preview update
        print(f"\nTesting Filename Preview:")
        gui.update_filename_preview()
        preview = gui.filename_preview.get()
        print(f"  Preview: {preview}")
        
        if "Output:" in preview and os.path.basename(gui.map_files[0]).split('-')[0] in preview:
            print(f"  ✅ Filename preview correct")
        else:
            print(f"  ❌ Filename preview incorrect")
            return False
        
        root.destroy()
        return True
        
    except Exception as e:
        print(f"❌ Error in GUI methods test: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        # Clean up test files
        for test_file in test_files:
            try:
                if os.path.exists(test_file):
                    os.remove(test_file)
            except:
                pass


def test_actual_processing_with_order():
    """Test actual processing with different file orders"""
    print(f"\nTesting Actual Processing with Different Orders")
    print("=" * 50)
    
    test_files = create_test_files_with_different_names()
    
    if len(test_files) < 2:
        print("❌ Need at least 2 test files")
        return False
    
    try:
        processor = FullMapProcessor()
        processor.set_rotation_angle(0)
        processor.set_filter_empty(True)
        
        # Test with original order (AAA first)
        print(f"Processing with AAA111 first:")
        output1 = processor.process_multiple_files(test_files)
        
        if output1:
            print(f"  Output: {os.path.basename(output1)}")
            if "AAA111" in output1:
                print(f"  ✅ Correct filename with AAA111")
            else:
                print(f"  ❌ Incorrect filename")
                return False
            
            # Move to test directory
            test_output1 = os.path.join(current_dir, f"order_test_AAA_{os.path.basename(output1)}")
            if os.path.exists(output1):
                os.rename(output1, test_output1)
        
        # Test with reversed order (CCC first)
        reversed_files = test_files[::-1]  # Reverse the order
        print(f"\nProcessing with CCC333 first:")
        output2 = processor.process_multiple_files(reversed_files)
        
        if output2:
            print(f"  Output: {os.path.basename(output2)}")
            if "CCC333" in output2:
                print(f"  ✅ Correct filename with CCC333")
            else:
                print(f"  ❌ Incorrect filename")
                return False
            
            # Move to test directory
            test_output2 = os.path.join(current_dir, f"order_test_CCC_{os.path.basename(output2)}")
            if os.path.exists(output2):
                os.rename(output2, test_output2)
        
        return True
        
    except Exception as e:
        print(f"❌ Error in processing test: {e}")
        return False
    finally:
        # Clean up test files
        for test_file in test_files:
            try:
                if os.path.exists(test_file):
                    os.remove(test_file)
            except:
                pass


def main():
    """Main test function"""
    print("TSK/MAP File Order Control Test")
    print("Testing file order adjustment and filename generation")
    print("=" * 70)
    
    success_count = 0
    total_tests = 3
    
    # Test 1: Filename generation logic
    if test_filename_generation_logic():
        success_count += 1
        print("✅ Test 1 passed: Filename generation logic")
    else:
        print("❌ Test 1 failed: Filename generation logic")
    
    # Test 2: GUI file order methods
    if test_gui_file_order_methods():
        success_count += 1
        print("✅ Test 2 passed: GUI file order methods")
    else:
        print("❌ Test 2 failed: GUI file order methods")
    
    # Test 3: Actual processing with order
    if test_actual_processing_with_order():
        success_count += 1
        print("✅ Test 3 passed: Actual processing with order")
    else:
        print("❌ Test 3 failed: Actual processing with order")
    
    print("\n" + "=" * 70)
    print(f"File Order Control Test Results: {success_count}/{total_tests} tests passed")
    
    if success_count == total_tests:
        print("🎉 All file order control features working!")
        print("\nImplemented Features:")
        print("✅ Move Up/Move Down buttons for file reordering")
        print("✅ Real-time filename preview based on first file")
        print("✅ Visual indication of first file in info display")
        print("✅ Filename generation respects file order")
        print("✅ GUI methods handle edge cases (top/bottom)")
        
        print(f"\nUser Experience:")
        print(f"• Select a file and click 'Move Up' to move it higher in the list")
        print(f"• Select a file and click 'Move Down' to move it lower in the list")
        print(f"• First file in the list determines the output Excel filename")
        print(f"• Filename preview updates automatically when order changes")
        print(f"• Clear visual feedback shows which file determines the filename")
        
        print(f"\nFilename Examples:")
        print(f"• AAA111-01-B1 first → AAA111_all_maps_summary_YYYYMMDD_HHMMSS.xlsx")
        print(f"• BBB222-02-C2 first → BBB222_all_maps_summary_YYYYMMDD_HHMMSS.xlsx")
        print(f"• CCC333-03-D3 first → CCC333_all_maps_summary_YYYYMMDD_HHMMSS.xlsx")
        
    else:
        print(f"⚠️  {total_tests - success_count} tests failed")


if __name__ == "__main__":
    main()
