任务流程：
完善Bump map To Tsk map Tool功能:
 1, 调整GUI设计部分：
      --对勾选择测试厂部分（调整选择测试厂顺序，优先：Chipmore Technologies, Unisem Group, TongFu Microelectronics, ChipMOS Technologies, NEPES Corporation, Chipbond Technology Corporation. 其他测试厂请动态调整顺序（按照你的想法)(选中的测试厂，请在右侧显示哪个测试厂被选中的文本提示）
    --Output 路径选择部分(保存格式优先按照All files格式之后是.map格式之后是.tsk格式。  
 2, Clear memory 功能点击后，只显示信息在GUI中，可以不弹窗提示。
 3, 从Bump map To Tsk map Tool GUI 返回主界面（选择tool界面，查看GUI发现，返回在右上角），需要检查代码，尽量复用之前tool的代码。保持返回部分在Clear Memory控件后面，保持GUI风格一致。
 4, Back返回控件设计完成后，增加Exit控件，从Bump map To Tsk map Tool可以直接退出工具（复用之前代码，退出时清理内存）
 
上述功能开发注意事项：
     --- 其他两个功能不要破坏，代码功能不要破坏
     --- GUI尽量统一，不要占用太多空间
     ---复用代码部分，尽量做到代码统一
	 ---先开发GUI部分，稍后会设计这个Tool的主要功能，请提前根据GUI，分析代码实现方式，提前设计架构。

要求：
1，测试脚本和文件请放到test文件夹
2，生成的md说明文档，请放到temp文件夹
3，程序架构统一，框架统一，代码尽量简洁