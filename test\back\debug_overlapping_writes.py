#!/usr/bin/env python3
"""
调试重叠写入问题
找出为什么新旧位置都被写入了

Author: Yuribytes
Company: Chipone TE development Team
"""

import os
import sys

# 添加父目录到路径
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, parent_dir)

from nepes_enhanced_processor import NEPESEnhancedProcessor

def create_tracking_processor():
    """创建一个跟踪写入的处理器"""
    
    class TrackingNEPESProcessor(NEPESEnhancedProcessor):
        def __init__(self):
            super().__init__()
            self.write_log = []
        
        def apply_enhanced_conversion(self):
            """重写以跟踪所有写入操作"""
            print(f"🔄 开始跟踪写入操作...")
            
            # 只跟踪关键位置
            target_positions = {
                (79, 0): "x=79,y=0",
                (149, 2): "x=149,y=2"
            }
            
            for i in range(1, self.rowsize + 1):
                for j in range(1, self.columnsize + 1):
                    row_index = i - 1
                    col_index = j - 1
                    
                    if row_index >= len(self.bump_data) or col_index >= len(self.bump_data[row_index]):
                        continue
                    
                    bump_value = self.bump_data[row_index][col_index]
                    die_index = (i - 1) * self.columnsize + j - 1
                    category_pos = self.TestResultCategory + 4 * die_index - 1
                    
                    display_x = j - 1
                    display_y = i - 1
                    
                    # 只跟踪目标位置
                    if (display_x, display_y) in target_positions:
                        pos_name = target_positions[(display_x, display_y)]
                        
                        print(f"\n📍 处理 {pos_name}:")
                        print(f"   坐标: x={display_x}, y={display_y}")
                        print(f"   die_index: {die_index}")
                        print(f"   bump_value: '{bump_value}'")
                        print(f"   category_pos: {category_pos}")
                        print(f"   写入范围: [{category_pos}:{category_pos+4}]")
                        
                        if category_pos + 4 <= len(self.tsk_processor.filearray):
                            # 记录写入前的状态
                            before_bytes = self.tsk_processor.filearray[category_pos:category_pos+4].copy()
                            
                            binary_pattern = self.get_binary_modification_pattern(bump_value)
                            
                            if binary_pattern is not None:
                                print(f"   写入模式: {binary_pattern.hex().upper()}")
                                
                                # 执行写入并记录每个字节
                                for k in range(4):
                                    old_value = self.tsk_processor.filearray[category_pos + k]
                                    new_value = binary_pattern[k]
                                    self.tsk_processor.filearray[category_pos + k] = new_value
                                    
                                    print(f"     位置{category_pos + k}: {old_value:02X} → {new_value:02X}")
                                    
                                    # 记录写入日志
                                    self.write_log.append({
                                        'position': category_pos + k,
                                        'old_value': old_value,
                                        'new_value': new_value,
                                        'source': pos_name
                                    })
                                
                                after_bytes = self.tsk_processor.filearray[category_pos:category_pos+4]
                                print(f"   写入结果: {after_bytes.hex().upper()}")
                            else:
                                print(f"   无修改 ('{bump_value}')")
                        else:
                            print(f"   ❌ 越界")
                    
                    # 正常处理其他位置
                    elif category_pos + 4 <= len(self.tsk_processor.filearray):
                        self.processing_stats['total_positions'] += 1
                        
                        binary_pattern = self.get_binary_modification_pattern(bump_value)
                        
                        if binary_pattern is None:
                            self.processing_stats['unchanged_positions'] += 1
                        else:
                            for k in range(4):
                                self.tsk_processor.filearray[category_pos + k] = binary_pattern[k]
                                self.processing_stats['binary_modifications'] += 1
                            
                            if bump_value == "00":
                                self.processing_stats['pass_positions'] += 1
                            else:
                                self.processing_stats['fail_positions'] += 1
            
            return True
    
    return TrackingNEPESProcessor()

def analyze_write_conflicts():
    """分析写入冲突"""
    print("🔍 分析写入冲突")
    print("=" * 40)
    
    bump_file = "D97127.09"
    dummy_file = "009.NNS157-09-E4"
    output_file = "tracking_writes.tsk"
    
    # 使用跟踪处理器
    processor = create_tracking_processor()
    success = processor.process_nepes_enhanced(bump_file, dummy_file, output_file)
    
    if not success:
        print("❌ 处理失败")
        return False
    
    # 分析写入日志
    print(f"\n📊 写入日志分析:")
    print(f"   总写入次数: {len(processor.write_log)}")
    
    # 检查位置冲突
    position_writes = {}
    for write in processor.write_log:
        pos = write['position']
        if pos not in position_writes:
            position_writes[pos] = []
        position_writes[pos].append(write)
    
    conflicts = {pos: writes for pos, writes in position_writes.items() if len(writes) > 1}
    
    if conflicts:
        print(f"\n⚠️  发现位置冲突:")
        for pos, writes in conflicts.items():
            print(f"   位置{pos}被写入{len(writes)}次:")
            for write in writes:
                print(f"     {write['source']}: {write['old_value']:02X} → {write['new_value']:02X}")
    else:
        print(f"\n✅ 无位置冲突")
    
    # 检查目标位置的最终状态
    with open(output_file, 'rb') as f:
        final_data = f.read()
    
    print(f"\n📊 最终状态检查:")
    
    # 检查新位置
    new_pos_79 = final_data[15364:15368]
    new_pos_149 = final_data[18084:18088]
    
    print(f"   新位置15364-15367: {new_pos_79.hex().upper()}")
    print(f"   新位置18084-18087: {new_pos_149.hex().upper()}")
    
    # 检查旧位置
    old_pos_79 = final_data[15365:15369]
    old_pos_149 = final_data[18085:18089]
    
    print(f"   旧位置15365-15368: {old_pos_79.hex().upper()}")
    print(f"   旧位置18085-18088: {old_pos_149.hex().upper()}")
    
    # 检查重叠区域
    overlap_79 = final_data[15365:15368]  # 重叠区域
    overlap_149 = final_data[18085:18088]  # 重叠区域
    
    print(f"   重叠区域15365-15367: {overlap_79.hex().upper()}")
    print(f"   重叠区域18085-18087: {overlap_149.hex().upper()}")
    
    return True

def main():
    """主调试函数"""
    print("🧪 重叠写入问题调试")
    print("=" * 70)
    
    # 切换到测试目录
    os.chdir(os.path.dirname(os.path.abspath(__file__)))
    
    try:
        # 分析写入冲突
        result = analyze_write_conflicts()
        
        print("\n" + "=" * 70)
        print("🎉 调试结果:")
        if result:
            print("✅ 调试完成，请检查上述输出找出重叠写入的原因")
        else:
            print("❌ 调试失败")
            
        return result
            
    except Exception as e:
        print(f"❌ 调试过程出错: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
