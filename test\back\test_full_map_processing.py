#!/usr/bin/env python3
"""
Real processing test for Full Map Tool with 3AA111-01-B4.map file
"""

import os
import sys
from pathlib import Path

# Add parent directory to path to import modules
sys.path.insert(0, str(Path(__file__).parent.parent))

def test_full_map_processing():
    """Test actual MAP file processing and Excel generation"""
    print('=== Full Map Tool - Real Processing Test ===')
    print('Testing with 3AA111-01-B4.map file')
    print()

    try:
        # Import required modules
        from full_map_processor import FullMapProcessor
        from config_reader import ConfigReader
        
        # Check if test MAP file exists
        test_map_file = os.path.join(os.path.dirname(__file__), '3AA111-01-B4.map')
        if not os.path.exists(test_map_file):
            print(f'ERROR: Test MAP file not found: {test_map_file}')
            return False
        
        print(f'Found test MAP file: {os.path.basename(test_map_file)}')
        
        # Get file size
        file_size = os.path.getsize(test_map_file)
        print(f'File size: {file_size} bytes')
        
        # Create FullMapProcessor
        processor = FullMapProcessor()
        print('FullMapProcessor created')
        
        # Set processing options
        processor.set_rotation_angle(0)  # 0 degrees (Original)
        processor.set_filter_empty(True)  # Filter empty areas
        print('Processing options set: rotation=0, filter_empty=True')
        
        # Load configuration if available
        test_config = os.path.join(os.path.dirname(__file__), 'test_config.xlsx')
        if os.path.exists(test_config):
            config_reader = ConfigReader()
            if config_reader.read_config_file(test_config):
                processor.set_config_reader(config_reader)
                print('Configuration loaded from test_config.xlsx')
            else:
                print('Configuration file exists but failed to load')
        else:
            print('No configuration file found, proceeding without bin name mapping')
        
        # Process the MAP file
        print()
        print('Starting MAP file processing...')
        output_file = processor.process_multiple_files([test_map_file])
        
        if output_file:
            print(f'SUCCESS: Excel file generated: {output_file}')
            
            # Check if output file exists
            if os.path.exists(output_file):
                file_size = os.path.getsize(output_file)
                print(f'Output file verified: {file_size} bytes')
                
                # Try to read the Excel file to verify it's valid
                try:
                    from openpyxl import load_workbook
                    wb = load_workbook(output_file)
                    sheet_names = wb.sheetnames
                    print(f'Excel file is valid with sheets: {sheet_names}')
                    
                    # Get some basic info about the first sheet
                    if sheet_names:
                        ws = wb[sheet_names[0]]
                        max_row = ws.max_row
                        max_col = ws.max_column
                        print(f'First sheet dimensions: {max_row} rows x {max_col} columns')
                        
                        # Check some cell values
                        if max_row > 1 and max_col > 1:
                            print('Sample data from first few cells:')
                            for r in range(1, min(4, max_row + 1)):
                                for c in range(1, min(4, max_col + 1)):
                                    cell_value = ws.cell(row=r, column=c).value
                                    print(f'  Cell({r},{c}): {cell_value}')
                    
                    wb.close()
                    return True
                    
                except Exception as e:
                    print(f'Could not verify Excel file content: {e}')
                    return False
            else:
                print(f'ERROR: Output file not found: {output_file}')
                return False
        else:
            print('FAILED: No output file generated')
            return False
            
    except Exception as e:
        print(f'FAILED: Processing error: {e}')
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_full_map_processing()
    if success:
        print('\n=== TEST PASSED ===')
        print('Full Map Tool successfully processed the MAP file and generated Excel output')
    else:
        print('\n=== TEST FAILED ===')
        print('Full Map Tool failed to process the MAP file correctly')
