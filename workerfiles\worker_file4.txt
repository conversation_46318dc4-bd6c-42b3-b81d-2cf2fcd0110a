任务流程：
1，Full Map Tool 功能增加：
     ---每个map生成的excel sheet中A14~AXX，B14~BXX，C14~CXX分别展示了bin的名字，数量，良率。并且我们按照了A14~AXX，C14~CXX 跟随B14~BXX由大到小排列，同步显示。现在需要在Full Map Tool 中增加一个选择框，用对勾来决定是否选择。
     --- 这个对勾选择的功能是:决定是否按照由大到小排列。默认是打勾（按照由大到小排列），用户可以选择不打勾 ，不按照由大到小排列(这部分内容都已经代码实现，不要修改破环这部分代码），需要修改直接按照A14~AXX从0这个bin开始逐个增加，一致到255停止，每个bin的数量不要统计错了，如果有bin没有fail显示，那么这个bin fail数量就按照0记录。
    ---打勾觉得的这个功能同样已经代码实现，请不要修改这部分，只修改要实现的逻辑功能即可。
2, 上述功能开发注意事项：
     --- bin别数量和良率统计不要破坏，代码功能不要破坏   
     --- GUI尽量统一，不要占用太多空间。

如果代码生效后要求：
1，测试脚本和文件请放到test文件夹
2，生成的md说明文档，请放到temp文件夹
3，程序架构统一，框架统一，代码尽量简洁