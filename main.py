#!/usr/bin/env python3
"""
TSK/MAP File Processor - Main Entry Point
Python implementation of Visual Basic UserForm1.frm functionality

This application processes TSK/MAP binary files from semiconductor testing
and outputs the data to Excel with various rotation angles and color coding.

Author: Python Implementation of VB UserForm1
Date: 2025
"""

import sys
import os
import tkinter as tk
from tkinter import messagebox

# Add current directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from tool_selector import ToolSelector
    from tsk_map_gui import ABMapGUI
    from full_map_gui import FullMapGUI
    from tsk_map_processor import TSKMapProcessor
    from excel_output import ExcelOutputHandler
except ImportError as e:
    print(f"Import error: {e}")
    print("Please ensure all required modules are in the same directory.")
    sys.exit(1)


def check_dependencies():
    """Check if required dependencies are available"""
    missing_deps = []
    
    try:
        import openpyxl
    except ImportError:
        missing_deps.append("openpyxl")
    
    try:
        import tkinter
    except ImportError:
        missing_deps.append("tkinter")
    
    if missing_deps:
        print("Missing required dependencies:")
        for dep in missing_deps:
            print(f"  - {dep}")
        print("\nPlease install missing dependencies:")
        print("pip install openpyxl")
        return False
    
    return True


def print_usage():
    """Print usage information"""
    print("TSK/MAP File Processor")
    print("=" * 50)
    print("Python implementation of Visual Basic UserForm1.frm")
    print()
    print("Usage:")
    print("  python main.py              - Launch GUI application")
    print("  python main.py --help       - Show this help message")
    print("  python main.py --version    - Show version information")
    print()
    print("Features:")
    print("  - Read TSK/MAP binary files")
    print("  - Parse semiconductor test data")
    print("  - Support multiple rotation angles (0°, 90°, 180°, 270°)")
    print("  - Export to Excel with color coding")
    print("  - Support for Amap and Bmap worksheets")
    print()
    print("File Format Support:")
    print("  - TSK files (*.tsk)")
    print("  - MAP files (*.map)")
    print("  - Binary format with version support (0, 2, 3, and others)")


def print_version():
    """Print version information"""
    print("TSK/MAP File Processor v1.0")
    print("Python implementation of Visual Basic UserForm1.frm")
    print("Compatible with Python 3.6+")


def run_command_line_test():
    """Run a simple command line test (for development/debugging)"""
    print("Running command line test...")
    
    # Create processor instance
    processor = TSKMapProcessor()
    
    # Test file path (you can modify this for testing)
    test_file = "test.tsk"  # Replace with actual test file
    
    if os.path.exists(test_file):
        print(f"Testing with file: {test_file}")
        
        if processor.read_file(test_file):
            print("✓ File read successfully")
            
            if processor.parse_file_header():
                print("✓ Header parsed successfully")
                info = processor.get_file_info()
                print(f"  Version: {info['version']}")
                print(f"  Dimensions: {info['columnsize']} x {info['rowsize']}")
                print(f"  Reference Die: ({info['RefdieX']}, {info['RefdieY']})")
                
                if processor.process_die_data():
                    print("✓ Die data processed successfully")
                    
                    # Test Excel output
                    from excel_output import create_excel_output
                    if create_excel_output(processor, "Amap", 0, "test_output.xlsx", test_file):
                        print("✓ Excel output created successfully")
                    else:
                        print("✗ Excel output failed")
                else:
                    print("✗ Die data processing failed")
            else:
                print("✗ Header parsing failed")
        else:
            print("✗ File reading failed")
    else:
        print(f"Test file not found: {test_file}")
        print("Skipping command line test")


def main():
    """Main entry point"""
    
    # Parse command line arguments
    if len(sys.argv) > 1:
        arg = sys.argv[1].lower()
        
        if arg in ['--help', '-h', 'help']:
            print_usage()
            return
        elif arg in ['--version', '-v', 'version']:
            print_version()
            return
        elif arg in ['--test', '-t', 'test']:
            if check_dependencies():
                run_command_line_test()
            return
        else:
            print(f"Unknown argument: {sys.argv[1]}")
            print("Use --help for usage information")
            return
    
    # Check dependencies before launching GUI
    if not check_dependencies():
        return
    
    try:
        # Launch main application with frame switching
        print("Launching TSK/MAP File Processor with frame switching...")

        from main_application import TSKMapApplication
        app = TSKMapApplication()
        app.run()

    except KeyboardInterrupt:
        print("\nApplication interrupted by user")
    except Exception as e:
        print(f"Error launching application: {e}")
        # Only show messagebox if we have a valid Tk root
        try:
            messagebox.showerror("Error", f"Failed to launch application:\n{str(e)}")
        except:
            # If messagebox fails, just print the error
            pass


if __name__ == "__main__":
    main()
