#!/usr/bin/env python3
"""
Integrated test for the complete bump map processing system
Tests both the specialized NEPES processor and the main processor framework

Author: Yuribytes
Company: Chipone TE development Team
"""

import os
import sys
from bump_map_processor import BumpMapProcessor


class IntegratedBumpProcessorTester:
    """Test class for integrated bump map processing system"""
    
    def __init__(self):
        self.main_processor = BumpMapProcessor()
        self.test_results = {}
    
    def test_processor_initialization(self):
        """Test main processor initialization"""
        print("🧪 Test 1: Processor Initialization")
        print("-" * 40)

        try:
            # Check supported test houses
            supported_houses = self.main_processor.get_supported_test_houses()
            print(f"   Supported test houses: {len(supported_houses)}")

            # Check if NEPES is supported
            if "NEPES" in supported_houses:
                print(f"✅ NEPES processor is supported")

                # Set NEPES as current processor
                success = self.main_processor.set_test_house("NEPES")
                if success:
                    print(f"✅ NEPES processor set successfully")

                    # Check current processor
                    current = self.main_processor.current_processor
                    if current and hasattr(current, '_nepes_processor') and current._nepes_processor:
                        print(f"   ✅ Specialized NEPES processor available")
                        print(f"   Processor name: {current.name}")
                        self.test_results["processor_init"] = True
                    else:
                        print(f"   ❌ Specialized NEPES processor not available")
                        self.test_results["processor_init"] = False
                else:
                    print(f"❌ Failed to set NEPES processor")
                    self.test_results["processor_init"] = False
            else:
                print(f"❌ NEPES processor not supported")
                self.test_results["processor_init"] = False

        except Exception as e:
            print(f"❌ Processor initialization failed: {e}")
            self.test_results["processor_init"] = False

        return self.test_results.get("processor_init", False)
    
    def test_main_processor_pipeline(self):
        """Test main processor pipeline"""
        print("\n🧪 Test 2: Main Processor Pipeline")
        print("-" * 40)

        try:
            bump_file = "test/D97127.09"
            dummy_file = "test/009.NNS157-09-E4"
            output_file = "test/integrated_test_output.tsk"

            # Remove existing output file
            if os.path.exists(output_file):
                os.remove(output_file)

            # Set NEPES processor first
            if not self.main_processor.set_test_house("NEPES"):
                print(f"❌ Failed to set NEPES processor")
                self.test_results["main_pipeline"] = False
                return False

            # Test the main processor pipeline
            success = self.main_processor.process_maps(
                bump_file, dummy_file, output_file
            )

            if success:
                print(f"✅ Main processor pipeline successful")

                # Verify output file
                if os.path.exists(output_file):
                    file_size = os.path.getsize(output_file)
                    print(f"   Output file: {output_file} ({file_size} bytes)")
                    self.test_results["main_pipeline"] = True
                else:
                    print(f"   ❌ Output file not created")
                    self.test_results["main_pipeline"] = False
            else:
                print(f"❌ Main processor pipeline failed")
                self.test_results["main_pipeline"] = False

        except Exception as e:
            print(f"❌ Main processor pipeline test failed: {e}")
            self.test_results["main_pipeline"] = False

        return self.test_results.get("main_pipeline", False)
    
    def test_direct_nepes_access(self):
        """Test direct access to NEPES processor"""
        print("\n🧪 Test 3: Direct NEPES Processor Access")
        print("-" * 40)

        try:
            # Set NEPES processor and get it
            self.main_processor.set_test_house("NEPES")
            nepes_processor = self.main_processor.current_processor

            if nepes_processor and hasattr(nepes_processor, 'process_complete_pipeline'):
                bump_file = "test/D97127.09"
                dummy_file = "test/009.NNS157-09-E4"
                output_file = "test/direct_nepes_output.tsk"

                # Remove existing output file
                if os.path.exists(output_file):
                    os.remove(output_file)

                # Test direct NEPES processing
                success = nepes_processor.process_complete_pipeline(
                    bump_file, dummy_file, output_file
                )

                if success:
                    print(f"✅ Direct NEPES processing successful")

                    # Get processing statistics
                    stats = nepes_processor.get_processing_stats()
                    print(f"   Statistics: {stats.get('processing_stats', {})}")

                    # Verify output file
                    if os.path.exists(output_file):
                        file_size = os.path.getsize(output_file)
                        print(f"   Output file: {output_file} ({file_size} bytes)")
                        self.test_results["direct_nepes"] = True
                    else:
                        print(f"   ❌ Output file not created")
                        self.test_results["direct_nepes"] = False
                else:
                    print(f"❌ Direct NEPES processing failed")
                    self.test_results["direct_nepes"] = False
            else:
                print(f"❌ Direct NEPES access not available")
                self.test_results["direct_nepes"] = False

        except Exception as e:
            print(f"❌ Direct NEPES access test failed: {e}")
            self.test_results["direct_nepes"] = False

        return self.test_results.get("direct_nepes", False)
    
    def test_processor_compatibility(self):
        """Test processor compatibility and error handling"""
        print("\n🧪 Test 4: Processor Compatibility")
        print("-" * 40)

        try:
            # Test getting supported test houses
            supported_houses = self.main_processor.get_supported_test_houses()
            print(f"   Total supported test houses: {len(supported_houses)}")

            # Test setting different processors
            available_processors = []
            test_houses = ["NEPES", "ChipMOS", "Unisem", "TongFu", "Custom"]

            for test_house in test_houses:
                if test_house in supported_houses:
                    success = self.main_processor.set_test_house(test_house)
                    if success:
                        processor = self.main_processor.current_processor
                        available_processors.append(test_house)
                        print(f"   ✅ {test_house}: {processor.name}")
                    else:
                        print(f"   ❌ {test_house}: Failed to set")
                else:
                    print(f"   ❌ {test_house}: Not supported")

            # Test invalid processor
            invalid_success = self.main_processor.set_test_house("NonExistent")
            if not invalid_success:
                print(f"   ✅ Invalid processor correctly rejected")
            else:
                print(f"   ❌ Invalid processor should be rejected")

            if len(available_processors) >= 3:  # Should have at least a few processors
                print(f"✅ Processor compatibility test passed")
                print(f"   Available processors: {available_processors}")
                self.test_results["compatibility"] = True
            else:
                print(f"❌ Too few processors available")
                self.test_results["compatibility"] = False

        except Exception as e:
            print(f"❌ Processor compatibility test failed: {e}")
            self.test_results["compatibility"] = False

        return self.test_results.get("compatibility", False)
    
    def test_error_handling(self):
        """Test error handling with invalid inputs"""
        print("\n🧪 Test 5: Error Handling")
        print("-" * 40)

        try:
            # Set NEPES processor first
            self.main_processor.set_test_house("NEPES")

            # Test with non-existent files
            success = self.main_processor.process_maps(
                "non_existent_bump.map",
                "non_existent_dummy.map",
                "test/error_test_output.tsk"
            )

            if not success:
                print(f"✅ Error handling works correctly for non-existent files")
                self.test_results["error_handling"] = True
            else:
                print(f"❌ Should have failed with non-existent files")
                self.test_results["error_handling"] = False

        except Exception as e:
            print(f"✅ Exception properly caught: {type(e).__name__}")
            self.test_results["error_handling"] = True

        return self.test_results.get("error_handling", False)
    
    def run_all_tests(self):
        """Run all integrated tests"""
        print("🚀 Integrated Bump Map Processor - Comprehensive Test Suite")
        print("=" * 65)
        
        # Run tests in sequence
        self.test_processor_initialization()
        self.test_main_processor_pipeline()
        self.test_direct_nepes_access()
        self.test_processor_compatibility()
        self.test_error_handling()
        
        # Summary
        print("\n" + "=" * 65)
        print("📋 Integrated Test Results Summary")
        print("=" * 65)
        
        total_tests = len(self.test_results)
        passed_tests = sum(self.test_results.values())
        
        for test_name, result in self.test_results.items():
            status = "✅ PASS" if result else "❌ FAIL"
            print(f"{test_name:<20} {status}")
        
        print("-" * 65)
        print(f"Total Tests: {total_tests}")
        print(f"Passed: {passed_tests}")
        print(f"Failed: {total_tests - passed_tests}")
        print(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
        
        if passed_tests == total_tests:
            print("\n🎉 All integrated tests PASSED!")
            print("✅ System ready for production use!")
            print("🔧 Binary operations working correctly!")
            print("📊 NEPES processor fully integrated!")
        else:
            print(f"\n⚠️  {total_tests - passed_tests} test(s) failed.")
        
        return passed_tests == total_tests


def main():
    """Main test function"""
    print("🧪 Starting Integrated Bump Map Processor Test")
    print("Testing both specialized NEPES processor and main framework\n")
    
    tester = IntegratedBumpProcessorTester()
    success = tester.run_all_tests()
    
    if success:
        print("\n🎯 Integrated system development completed successfully!")
        print("🔧 Binary operations fully functional!")
        print("📊 Ready for GUI integration!")
    else:
        print("\n🔧 Some integrated tests failed. Review implementation.")
    
    return success


if __name__ == "__main__":
    main()
