"""
Excel Output Module for TSK/MAP File Processor
Handles Excel file creation and formatting with color coding
"""

import openpyxl
from openpyxl.styles import <PERSON><PERSON><PERSON><PERSON>, Font, Alignment
from openpyxl.utils import get_column_letter
from typing import List, Tuple, Optional
import os


class ExcelOutputHandler:
    """Handles Excel output with color coding and formatting"""
    
    def __init__(self):
        self.workbook = None
        self.worksheet = None
        
        # Color definitions matching VB ColorIndex values
        self.color_map = {
            3: "FF0000",  # Red for failed dies (ColorIndex 3)
            4: "00FF00",  # Green for passed dies (ColorIndex 4)
        }
        
        # Font styles
        self.header_font = Font(bold=True, size=10)
        self.data_font = Font(size=9)
        self.center_alignment = Alignment(horizontal='center', vertical='center')
    
    def create_workbook(self, filename: str = "TSK_MAP_Output.xlsx") -> bool:
        """
        Create a new Excel workbook
        """
        try:
            self.workbook = openpyxl.Workbook()
            # Remove default sheet
            if "Sheet" in self.workbook.sheetnames:
                self.workbook.remove(self.workbook["Sheet"])
            return True
        except Exception as e:
            print(f"Error creating workbook: {e}")
            return False
    
    def create_worksheet(self, sheet_name: str) -> bool:
        """
        Create a new worksheet with specified name
        """
        try:
            if self.workbook is None:
                return False
                
            # Remove existing sheet with same name if exists
            if sheet_name in self.workbook.sheetnames:
                self.workbook.remove(self.workbook[sheet_name])
                
            self.worksheet = self.workbook.create_sheet(sheet_name)
            return True
        except Exception as e:
            print(f"Error creating worksheet: {e}")
            return False
    
    def write_rotated_data(self, data: List[List[Tuple[int, int]]],
                          rotation_angle: int, sheet_name: str, processor=None, config_reader=None,
                          sort_by_quantity=True) -> bool:
        """
        Write rotated data to Excel worksheet
        data: 2D list of (category, color_index) tuples
        rotation_angle: 0, 90, 180, or 270 degrees
        sort_by_quantity: If True, sort bins by quantity (descending). If False, sort by bin number (ascending)
        """
        try:
            if not self.create_worksheet(sheet_name):
                return False

            if not data:
                return False

            rows = len(data)
            cols = len(data[0]) if rows > 0 else 0

            # Calculate original dimensions (before rotation) from processor if available
            if processor:
                original_rows = processor.rowsize
                original_cols = processor.columnsize
            else:
                # Fallback: calculate from rotated data dimensions
                if rotation_angle == 0 or rotation_angle == 180:
                    original_rows = rows
                    original_cols = cols
                else:  # 90 or 270 degrees
                    original_rows = cols  # After rotation, cols represent original rows
                    original_cols = rows  # After rotation, rows represent original cols

            # Check Excel limits
            max_excel_cols = 16384  # Excel XFD column limit
            max_excel_rows = 1048576  # Excel row limit

            # Adjust dimensions if they exceed Excel limits
            effective_rows = min(rows, max_excel_rows - 1)  # -1 for header row
            effective_cols = min(cols, max_excel_cols - 1)  # -1 for header column

            if rows > effective_rows or cols > effective_cols:
                print(f"Warning: Data truncated to fit Excel limits")
                print(f"Original: {rows}x{cols}, Effective: {effective_rows}x{effective_cols}")

            # Clear worksheet
            self.worksheet.delete_rows(1, self.worksheet.max_row)
            self.worksheet.delete_cols(1, self.worksheet.max_column)

            # Write device information in columns A-D if processor is provided
            if processor:
                self._write_device_info(processor, config_reader, sort_by_quantity)

            # Write headers based on rotation (using effective dimensions)
            # Headers start from column E (5) to leave space for device info
            # Pass the rotated data dimensions to header function
            header_rows, header_cols = self._get_header_dimensions(effective_rows, effective_cols, rotation_angle)
            self._write_headers(header_rows, header_cols, rotation_angle, start_col=4,
                              original_rows=original_rows, original_cols=original_cols)

            # Write data starting from row 2, column F (6) to leave space for device info
            for i in range(effective_rows):
                for j in range(effective_cols):
                    category, color_index = data[i][j]

                    # Only write data for tested dies (non-zero color_index)
                    if color_index != 0:
                        cell_row = i + 2  # +2 because headers are in row 1, and Excel is 1-based
                        cell_col = j + 5  # +5 because device info takes columns A-D, data starts from E

                        # Double-check Excel limits before writing
                        if cell_row <= max_excel_rows and cell_col <= max_excel_cols:
                            cell = self.worksheet.cell(row=cell_row, column=cell_col)
                            cell.value = category
                            cell.font = self.data_font
                            cell.alignment = self.center_alignment

                            # Apply color based on pass/fail status
                            if color_index in self.color_map:
                                fill_color = self.color_map[color_index]
                                cell.fill = PatternFill(start_color=fill_color,
                                                      end_color=fill_color,
                                                      fill_type="solid")

            # Auto-fit columns (with safety check)
            self._auto_fit_columns_safe(effective_cols)

            # Apply Excel styling for better readability
            self._apply_worksheet_styling()

            return True

        except Exception as e:
            print(f"Error writing data to Excel: {e}")
            return False

    def _get_header_dimensions(self, rotated_rows: int, rotated_cols: int, rotation_angle: int) -> tuple:
        """
        Get the dimensions for headers based on rotation angle
        Returns (header_rows, header_cols) which are the dimensions of the rotated data
        The input parameters are already the rotated data dimensions
        """
        # Simply return the rotated dimensions as they are
        return rotated_rows, rotated_cols

    def _calculate_rotated_headers(self, orig_rows: int, orig_cols: int, rotation_angle: int) -> tuple:
        """
        Calculate header values based on rotation logic
        Returns (row_headers_list, col_headers_list) containing the actual numbers to display
        """
        if rotation_angle == 0:
            # 0° - No rotation
            # Row headers: 1, 2, 3, ..., orig_rows
            # Col headers: 1, 2, 3, ..., orig_cols
            row_headers = list(range(1, orig_rows + 1))
            col_headers = list(range(1, orig_cols + 1))

        elif rotation_angle == 90:
            # 90° Clockwise rotation
            # Original grid: orig_rows x orig_cols (9 x 271)
            # Rotated grid: orig_cols x orig_rows (271 x 9)
            # Row headers: 1, 2, 3, ..., orig_cols (1 to 271, representing new row positions)
            # Col headers: orig_rows, orig_rows-1, ..., 1 (9, 8, 7, ..., 1, original row numbers in reverse)
            row_headers = list(range(1, orig_cols + 1))  # 1 to 271
            col_headers = list(range(orig_rows, 0, -1))  # 9 to 1

        elif rotation_angle == 180:
            # 180° rotation
            # Original grid: orig_rows x orig_cols
            # Rotated grid: orig_rows x orig_cols (same dimensions)
            # Row headers: orig_rows, orig_rows-1, ..., 1 (reverse order)
            # Col headers: orig_cols, orig_cols-1, ..., 1 (reverse order)
            row_headers = list(range(orig_rows, 0, -1))
            col_headers = list(range(orig_cols, 0, -1))

        elif rotation_angle == 270:
            # 270° Clockwise rotation (90° Counter-clockwise)
            # Original grid: orig_rows x orig_cols
            # Rotated grid: orig_cols x orig_rows
            # Row headers: orig_cols, orig_cols-1, ..., 1 (original col numbers in reverse)
            # Col headers: 1, 2, 3, ..., orig_rows (representing new col positions)
            row_headers = list(range(orig_cols, 0, -1))
            col_headers = list(range(1, orig_rows + 1))

        else:
            # Invalid angle, default to 0°
            row_headers = list(range(1, orig_rows + 1))
            col_headers = list(range(1, orig_cols + 1))

        return row_headers, col_headers

    def _write_device_info(self, processor, config_reader=None, sort_by_quantity=True):
        """
        Write device information in columns A-D

        Args:
            processor: TSKMapProcessor instance
            config_reader: ConfigReader instance (optional)
            sort_by_quantity: If True, sort bins by quantity (descending). If False, sort by bin number (ascending)
        """
        try:
            # Get test statistics
            stats = processor.get_test_statistics()

            # Column A: Labels
            labels = [
                "Device Info.:",
                "Device Name",
                "Test Program",
                "Lot ID",
                "Wafer ID.",
                "Test Start Time",
                "Test Stop Time",
                "Total Tested",
                "Pass",
                "Yield(%)",
                "",
                "Test Summary:"
            ]

            for i, label in enumerate(labels, 1):
                cell = self.worksheet.cell(row=i, column=1)  # Column A
                cell.value = label
                cell.font = self.header_font
                cell.alignment = self.center_alignment

            # Column B: Values
            # Get test program name from config file (A2) if available
            test_program_name = ""
            if config_reader and config_reader.has_config_loaded():
                test_program_name = config_reader.get_test_program_name()

            values = [
                "",  # Device Info.: (header, no value)
                processor.devicename,  # B2: Device Name
                test_program_name,  # B3: Test Program (from config file A2)
                processor.lotid,  # B4: Lot ID
                processor.waferslotid,  # B5: Wafer ID
                processor.waferstarttime,  # B6: Start Time
                processor.waferendtime,  # B7: End Time
                stats['total_tested'],  # B8: Total Tested
                stats['pass_count'],  # B9: Pass
                f"{stats['yield_percentage']:.2f}%",  # B10: Yield(%)
                "",  # Empty row
                ""   # Test Summary: (header, no value)
            ]

            for i, value in enumerate(values, 1):
                if value != "":  # Skip empty values
                    cell = self.worksheet.cell(row=i, column=2)  # Column B
                    cell.value = value
                    cell.font = self.data_font
                    cell.alignment = self.center_alignment

                    # Special formatting for percentage (B10)
                    if i == 10 and isinstance(value, str) and value.endswith('%'):
                        try:
                            # Convert percentage string back to decimal for Excel
                            percent_val = float(value.replace('%', '')) / 100
                            cell.value = percent_val
                            cell.number_format = "0.00%"  # Direct format assignment
                            # Set B10 to center alignment as required
                            cell.alignment = self.center_alignment
                        except:
                            pass  # Keep as string if conversion fails

            # Add bin statistics table starting from A13
            self._write_bin_statistics(processor, config_reader, sort_by_quantity)

        except Exception as e:
            print(f"Error writing device info: {e}")

    def _write_bin_statistics(self, processor, config_reader=None, sort_by_quantity=True):
        """
        Write bin statistics table starting from A13

        Args:
            processor: TSKMapProcessor instance
            config_reader: ConfigReader instance (optional)
            sort_by_quantity: If True, sort by quantity (descending). If False, sort by bin number (ascending)
        """
        try:
            # Get bin statistics with sorting option
            bin_stats = processor.get_bin_statistics(sort_by_quantity=sort_by_quantity)

            # Write headers in A13, B13, C13
            headers = ["Category", "QTY", "Yield(%)"]
            for col, header in enumerate(headers, 1):  # A=1, B=2, C=3
                cell = self.worksheet.cell(row=13, column=col)
                cell.value = header
                cell.font = self.header_font
                cell.alignment = self.center_alignment

            # Write bin data starting from row 14
            for row_idx, bin_data in enumerate(bin_stats, 14):  # Start from row 14
                # Column A: bin name with config mapping
                bin_number = int(bin_data['bin_name'].replace('bin', ''))

                if config_reader and config_reader.has_config_loaded():
                    # Use formatted name from config: "数字(名称)"
                    formatted_bin_name = config_reader.get_formatted_bin_name(bin_number)
                else:
                    # Use just the number without "bin" prefix: "数字"
                    formatted_bin_name = str(bin_number)

                cell_a = self.worksheet.cell(row=row_idx, column=1)
                cell_a.value = formatted_bin_name
                # Use bold font for bin names (A14~AXX)
                cell_a.font = self.header_font  # header_font is already bold
                cell_a.alignment = self.center_alignment

                # Column B: quantity
                cell_b = self.worksheet.cell(row=row_idx, column=2)
                cell_b.value = bin_data['quantity']
                cell_b.font = self.data_font
                cell_b.alignment = self.center_alignment

                # Column C: yield percentage
                cell_c = self.worksheet.cell(row=row_idx, column=3)
                # Format as percentage with 2 decimal places
                cell_c.value = bin_data['yield_percentage'] / 100  # Convert to decimal for Excel
                cell_c.number_format = "0.00%"
                cell_c.font = self.data_font
                cell_c.alignment = self.center_alignment

        except Exception as e:
            print(f"Error writing bin statistics: {e}")

    def _write_headers(self, rows: int, cols: int, rotation_angle: int, start_col: int = 2,
                      original_rows: int = None, original_cols: int = None):
        """
        Write row and column headers based on rotation angle
        New approach: Calculate header values based on rotation logic, then write to Excel
        """
        # Use original dimensions if provided, otherwise assume rows/cols are original
        orig_rows = original_rows if original_rows is not None else rows
        orig_cols = original_cols if original_cols is not None else cols

        # Calculate header values based on rotation logic
        row_headers, col_headers = self._calculate_rotated_headers(orig_rows, orig_cols, rotation_angle)

        # Write row headers (Column D)
        for i, header_value in enumerate(row_headers):
            if i + 1 <= rows:  # Don't exceed actual rotated dimensions
                cell = self.worksheet.cell(row=i + 2, column=4)  # Row i+2 (skip header row)
                cell.value = header_value
                cell.font = self.header_font
                cell.alignment = self.center_alignment

        # Write column headers (Row 1, starting from Column E)
        for j, header_value in enumerate(col_headers):
            if j + 1 <= cols:  # Don't exceed actual rotated dimensions
                cell = self.worksheet.cell(row=1, column=j + 5)  # Column j+5 (skip device info columns)
                cell.value = header_value
                cell.font = self.header_font
                cell.alignment = self.center_alignment
    
    def _auto_fit_columns(self):
        """
        Auto-fit column widths (legacy method, use _auto_fit_columns_safe for large data)
        """
        try:
            for column in self.worksheet.columns:
                max_length = 0
                column_letter = get_column_letter(column[0].column)

                for cell in column:
                    if cell.value:
                        max_length = max(max_length, len(str(cell.value)))

                # Set minimum width and add some padding
                adjusted_width = min(max(max_length + 2, 8), 15)
                self.worksheet.column_dimensions[column_letter].width = adjusted_width

        except Exception as e:
            print(f"Error auto-fitting columns: {e}")

    def _auto_fit_columns_safe(self, max_cols: int):
        """
        Auto-fit column widths with safety limits for large datasets
        """
        try:
            max_excel_cols = 16384
            safe_max_cols = min(max_cols + 2, max_excel_cols)  # +2 for headers

            for col_idx in range(1, safe_max_cols + 1):
                try:
                    column_letter = get_column_letter(col_idx)
                    max_length = 0

                    # Check a sample of cells to determine width (for performance)
                    sample_rows = min(100, self.worksheet.max_row)  # Sample first 100 rows

                    for row_idx in range(1, sample_rows + 1):
                        cell = self.worksheet.cell(row=row_idx, column=col_idx)
                        if cell.value:
                            max_length = max(max_length, len(str(cell.value)))

                    # Set minimum width and add some padding
                    adjusted_width = min(max(max_length + 2, 8), 15)
                    self.worksheet.column_dimensions[column_letter].width = adjusted_width

                except Exception as col_error:
                    # Skip problematic columns
                    print(f"Skipping column {col_idx}: {col_error}")
                    continue

        except Exception as e:
            print(f"Error in safe auto-fitting columns: {e}")

    def _apply_worksheet_styling(self):
        """
        Apply Excel styling for better readability
        """
        try:
            from openpyxl.styles import Border, Side

            # Define border style
            thin_border = Border(
                left=Side(style='thin'),
                right=Side(style='thin'),
                top=Side(style='thin'),
                bottom=Side(style='thin')
            )

            # Apply borders to device info area (A1:C12)
            for row in range(1, 13):  # Rows 1-12
                for col in range(1, 4):  # Columns A-C
                    cell = self.worksheet.cell(row=row, column=col)
                    cell.border = thin_border

            # Apply borders to bin statistics headers (A13:C13)
            for col in range(1, 4):  # Columns A-C
                cell = self.worksheet.cell(row=13, column=col)
                cell.border = thin_border
                # Make header background light gray
                from openpyxl.styles import PatternFill
                cell.fill = PatternFill(start_color="E0E0E0", end_color="E0E0E0", fill_type="solid")

            # Apply borders to bin statistics data (find the range dynamically)
            max_row = self.worksheet.max_row
            if max_row > 13:  # If there's bin data
                for row in range(14, max_row + 1):
                    for col in range(1, 4):  # Columns A-C
                        cell = self.worksheet.cell(row=row, column=col)
                        cell.border = thin_border

            # Set column widths for better readability
            self.worksheet.column_dimensions['A'].width = 15  # Device info labels
            self.worksheet.column_dimensions['B'].width = 20  # Values
            self.worksheet.column_dimensions['C'].width = 12  # Yield column

        except Exception as e:
            print(f"Error applying worksheet styling: {e}")
    
    def save_workbook(self, filename: str) -> bool:
        """
        Save the workbook to file
        """
        try:
            if self.workbook is None:
                return False
                
            # Ensure .xlsx extension
            if not filename.lower().endswith('.xlsx'):
                filename += '.xlsx'
                
            self.workbook.save(filename)
            return True
            
        except Exception as e:
            print(f"Error saving workbook: {e}")
            return False
    
    def create_setup_sheet(self, file_paths: dict) -> bool:
        """
        Create Setup sheet to store file paths (replicates VB Setup sheet functionality)
        file_paths: dict with 'Amap' and 'Bmap' keys
        """
        try:
            if not self.create_worksheet("Setup"):
                return False
            
            # Write headers
            self.worksheet.cell(row=1, column=1, value="Sheet")
            self.worksheet.cell(row=1, column=2, value="File Path")
            
            # Write Amap path
            self.worksheet.cell(row=2, column=1, value="Amap")
            self.worksheet.cell(row=2, column=2, value=file_paths.get('Amap', ''))
            
            # Write Bmap path
            self.worksheet.cell(row=3, column=1, value="Bmap")
            self.worksheet.cell(row=3, column=2, value=file_paths.get('Bmap', ''))
            
            # Format headers
            for col in range(1, 3):
                cell = self.worksheet.cell(row=1, column=col)
                cell.font = self.header_font
                cell.alignment = self.center_alignment
            
            # Auto-fit columns
            self._auto_fit_columns()
            
            return True
            
        except Exception as e:
            print(f"Error creating setup sheet: {e}")
            return False
    
    def close_workbook(self):
        """
        Close the workbook and clean up resources
        """
        try:
            if self.workbook:
                self.workbook.close()
                self.workbook = None
                self.worksheet = None
                print("Excel workbook closed and memory freed")
        except Exception as e:
            print(f"Warning: Error closing workbook: {e}")
            # Force cleanup even if close fails
            self.workbook = None
            self.worksheet = None


def create_excel_output(processor, sheet_name: str, rotation_angle: int,
                       output_filename: str, file_path: str = "", config_reader=None) -> bool:
    """
    Convenience function to create Excel output from TSKMapProcessor
    """
    try:
        excel_handler = ExcelOutputHandler()
        
        if not excel_handler.create_workbook():
            return False
        
        # Get rotated data
        rotated_data = processor.get_rotated_data(rotation_angle)
        
        if not rotated_data:
            print("No data to write to Excel")
            return False
        
        # Write data to worksheet (pass processor and config_reader for device info)
        if not excel_handler.write_rotated_data(rotated_data, rotation_angle, sheet_name, processor, config_reader):
            return False
        
        # Create setup sheet if file path provided
        if file_path:
            file_paths = {sheet_name: file_path}
            excel_handler.create_setup_sheet(file_paths)
        
        # Save workbook
        success = excel_handler.save_workbook(output_filename)
        excel_handler.close_workbook()
        
        return success
        
    except Exception as e:
        print(f"Error creating Excel output: {e}")
        return False
