#!/usr/bin/env python3
"""
最终二进制数据验证
确认有1938个bin63和2个bin59

Author: Yuribytes
Company: Chipone TE development Team
"""

import os
import sys

# 添加父目录到路径
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, parent_dir)

from nepes_enhanced_processor import NEPESEnhancedProcessor

def verify_final_binary_data():
    """验证最终二进制数据"""
    print("🧪 最终二进制数据验证")
    print("=" * 50)
    
    bump_file = "D97127.09"
    dummy_file = "009.NNS157-09-E4"
    output_file = "final_binary_verification.tsk"
    
    # 读取原始数据
    with open(dummy_file, 'rb') as f:
        original_data = f.read()
    
    # 执行NEPES处理
    processor = NEPESEnhancedProcessor()
    success = processor.process_nepes_enhanced(bump_file, dummy_file, output_file)
    
    if not success:
        print("❌ 处理失败")
        return False
    
    # 读取处理后数据
    with open(output_file, 'rb') as f:
        processed_data = f.read()
    
    # 分析二进制数据
    print(f"📊 二进制数据分析:")
    
    bin63_count = 0  # 0000003F
    bin59_count = 0  # 0000003B
    unchanged_count = 0
    other_changes = {}
    
    total_4byte_positions = len(original_data) // 4
    
    for i in range(0, len(original_data), 4):
        if i + 4 <= len(processed_data):
            original_4bytes = original_data[i:i+4]
            processed_4bytes = processed_data[i:i+4]
            
            if original_4bytes == processed_4bytes:
                unchanged_count += 1
            else:
                processed_hex = processed_4bytes.hex().upper()
                
                if processed_hex == "0000003F":
                    bin63_count += 1
                elif processed_hex == "0000003B":
                    bin59_count += 1
                else:
                    if processed_hex not in other_changes:
                        other_changes[processed_hex] = 0
                    other_changes[processed_hex] += 1
    
    print(f"   总4字节位置: {total_4byte_positions}")
    print(f"   未修改位置: {unchanged_count}")
    print(f"   bin63 (0000003F): {bin63_count}")
    print(f"   bin59 (0000003B): {bin59_count}")
    print(f"   其他修改: {len(other_changes)}")
    
    if other_changes:
        print(f"   其他修改详情:")
        for hex_val, count in other_changes.items():
            print(f"     {hex_val}: {count}次")
    
    # 验证预期结果
    print(f"\n🎯 预期结果验证:")
    print(f"   预期bin63: 1938")
    print(f"   实际bin63: {bin63_count}")
    print(f"   bin63正确: {'✅' if bin63_count == 1938 else '❌'}")
    
    print(f"   预期bin59: 2")
    print(f"   实际bin59: {bin59_count}")
    print(f"   bin59正确: {'✅' if bin59_count == 2 else '❌'}")
    
    # 总体验证
    total_modifications = bin63_count + bin59_count
    expected_modifications = 1940
    
    print(f"   总修改: {total_modifications}")
    print(f"   预期修改: {expected_modifications}")
    print(f"   修改正确: {'✅' if total_modifications == expected_modifications else '❌'}")
    
    # 检查关键位置
    print(f"\n📊 关键位置验证:")
    
    # x=79,y=0 (应该是bin63)
    pos_79_data = processed_data[15364:15368]
    actual_79 = pos_79_data.hex().upper()
    print(f"   x=79,y=0 (位置15364): {actual_79} {'✅' if actual_79 == '0000003F' else '❌'}")
    
    # x=149,y=2 (应该是bin59)
    pos_149_data = processed_data[18084:18088]
    actual_149 = pos_149_data.hex().upper()
    print(f"   x=149,y=2 (位置18084): {actual_149} {'✅' if actual_149 == '0000003B' else '❌'}")
    
    # 最终结论
    all_correct = (bin63_count == 1938 and 
                   bin59_count == 2 and 
                   len(other_changes) == 0 and
                   actual_79 == '0000003F' and
                   actual_149 == '0000003B')
    
    print(f"\n🎉 最终验证结果:")
    if all_correct:
        print(f"✅ 完美！所有数据都正确")
        print(f"✅ 1938个bin63 (Pass)")
        print(f"✅ 2个bin59 (Fail)")
        print(f"✅ 关键位置验证通过")
        print(f"✅ 无异常修改")
        print(f"✅ NEPES处理器与Full Map Tool逻辑完全统一")
    else:
        print(f"❌ 仍有问题需要解决")
    
    return all_correct

def main():
    """主验证函数"""
    print("🧪 最终二进制数据验证")
    print("=" * 70)
    
    # 切换到测试目录
    os.chdir(os.path.dirname(os.path.abspath(__file__)))
    
    try:
        # 最终验证
        result = verify_final_binary_data()
        
        print("\n" + "=" * 70)
        if result:
            print("🎯 **恭喜！NEPES处理器完全正确！**")
            print("🎉 所有问题都已解决，逻辑完美无缺！")
        else:
            print("❌ 仍需进一步调试")
            
        return result
            
    except Exception as e:
        print(f"❌ 验证过程出错: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
