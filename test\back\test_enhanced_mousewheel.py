#!/usr/bin/env python3
"""
Enhanced test script for area-wide mousewheel support
Tests that mousewheel works anywhere in the specified areas, not just on scrollbars

Author: Yuribytes
Company: Chipone TE development Team
"""

import sys
import os
import tkinter as tk
from tkinter import ttk

# Add parent directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


class EnhancedMousewheelTester:
    """Test class for enhanced area-wide mousewheel functionality"""
    
    def __init__(self):
        self.test_results = {}
    
    def count_mousewheel_bindings(self, widget):
        """Recursively count mousewheel bindings in a widget tree"""
        count = 0
        bindings = widget.bind()
        if "<MouseWheel>" in bindings:
            count += 1
        
        for child in widget.winfo_children():
            count += self.count_mousewheel_bindings(child)
        
        return count
    
    def test_ab_map_enhanced_mousewheel(self):
        """Test AB Map Tool enhanced area-wide mousewheel"""
        print("🧪 Testing AB Map Tool - Enhanced Area-wide Mousewheel")
        print("-" * 55)
        
        try:
            from tsk_map_gui import ABMapGUI
            
            # Create test window
            test_root = tk.Tk()
            test_root.title("AB Map Tool - Enhanced Mousewheel Test")
            test_root.geometry("900x800")
            test_root.withdraw()  # Hide for automated test
            
            # Create AB Map GUI
            ab_gui = ABMapGUI(test_root)
            
            # Find the info_frame (File Information area)
            # AB Map Tool uses root directly, so search in root's children
            info_frame = None
            def find_info_frame(widget):
                nonlocal info_frame
                if isinstance(widget, ttk.LabelFrame):
                    try:
                        if hasattr(widget, 'cget') and 'File Information' in str(widget.cget('text')):
                            info_frame = widget
                            return True
                    except:
                        pass

                for child in widget.winfo_children():
                    if find_info_frame(child):
                        return True
                return False

            find_info_frame(test_root)
            
            if info_frame:
                # Count mousewheel bindings in the entire area
                binding_count = self.count_mousewheel_bindings(info_frame)
                
                if binding_count >= 2:  # Should have multiple bindings now
                    print(f"✅ AB Map Tool File Information has enhanced mousewheel support")
                    print(f"   📊 Found {binding_count} mousewheel bindings in the area")
                    self.test_results["AB_Map_FileInfo_Enhanced"] = True
                else:
                    print(f"❌ AB Map Tool File Information has limited mousewheel support")
                    print(f"   📊 Only found {binding_count} mousewheel bindings")
                    self.test_results["AB_Map_FileInfo_Enhanced"] = False
            else:
                print("❌ Could not find File Information frame")
                self.test_results["AB_Map_FileInfo_Enhanced"] = False
            
            test_root.destroy()
            return self.test_results.get("AB_Map_FileInfo_Enhanced", False)
            
        except Exception as e:
            print(f"❌ AB Map Tool enhanced test failed: {e}")
            self.test_results["AB_Map_FileInfo_Enhanced"] = False
            return False
    
    def test_full_map_enhanced_mousewheel(self):
        """Test Full Map Tool enhanced area-wide mousewheel"""
        print("\n🧪 Testing Full Map Tool - Enhanced Area-wide Mousewheel")
        print("-" * 55)
        
        try:
            from full_map_tool_frame import FullMapToolFrame
            
            # Create test window
            test_root = tk.Tk()
            test_root.title("Full Map Tool - Enhanced Mousewheel Test")
            test_root.geometry("800x820")
            test_root.withdraw()  # Hide for automated test
            
            # Mock app controller
            class MockController:
                def __init__(self, root):
                    self.root = root
                def return_to_selector_with_confirmation(self, tool_name, has_unsaved):
                    return True
            
            controller = MockController(test_root)
            full_gui = FullMapToolFrame(test_root, controller)
            
            # Test 1: Map File Selection area
            files_frame = None
            info_frame = None
            
            for widget in full_gui.main_frame.winfo_children():
                if isinstance(widget, ttk.LabelFrame):
                    text = str(widget.cget('text'))
                    if 'MAP Files Selection' in text:
                        files_frame = widget
                    elif 'Processing Information' in text:
                        info_frame = widget
            
            # Test Map File Selection area
            if files_frame:
                files_binding_count = self.count_mousewheel_bindings(files_frame)
                if files_binding_count >= 3:  # Should have multiple bindings
                    print(f"✅ Full Map Tool Map File Selection has enhanced mousewheel support")
                    print(f"   📊 Found {files_binding_count} mousewheel bindings in the area")
                    self.test_results["Full_Map_FileSelection_Enhanced"] = True
                else:
                    print(f"❌ Full Map Tool Map File Selection has limited mousewheel support")
                    print(f"   📊 Only found {files_binding_count} mousewheel bindings")
                    self.test_results["Full_Map_FileSelection_Enhanced"] = False
            else:
                print("❌ Could not find Map Files Selection frame")
                self.test_results["Full_Map_FileSelection_Enhanced"] = False
            
            # Test Processing Information area
            if info_frame:
                info_binding_count = self.count_mousewheel_bindings(info_frame)
                if info_binding_count >= 2:  # Should have multiple bindings
                    print(f"✅ Full Map Tool Processing Information has enhanced mousewheel support")
                    print(f"   📊 Found {info_binding_count} mousewheel bindings in the area")
                    self.test_results["Full_Map_ProcessingInfo_Enhanced"] = True
                else:
                    print(f"❌ Full Map Tool Processing Information has limited mousewheel support")
                    print(f"   📊 Only found {info_binding_count} mousewheel bindings")
                    self.test_results["Full_Map_ProcessingInfo_Enhanced"] = False
            else:
                print("❌ Could not find Processing Information frame")
                self.test_results["Full_Map_ProcessingInfo_Enhanced"] = False
            
            test_root.destroy()
            return (self.test_results.get("Full_Map_FileSelection_Enhanced", False) and 
                   self.test_results.get("Full_Map_ProcessingInfo_Enhanced", False))
            
        except Exception as e:
            print(f"❌ Full Map Tool enhanced test failed: {e}")
            self.test_results["Full_Map_FileSelection_Enhanced"] = False
            self.test_results["Full_Map_ProcessingInfo_Enhanced"] = False
            return False
    
    def test_bump_map_enhanced_mousewheel(self):
        """Test Bump Map Tool enhanced area-wide mousewheel"""
        print("\n🧪 Testing Bump Map Tool - Enhanced Area-wide Mousewheel")
        print("-" * 55)
        
        try:
            from bump_map_tool_frame import BumpMapToolFrame
            
            # Create test window
            test_root = tk.Tk()
            test_root.title("Bump Map Tool - Enhanced Mousewheel Test")
            test_root.geometry("1000x800")
            test_root.withdraw()  # Hide for automated test
            
            # Mock app controller
            class MockController:
                def __init__(self, root):
                    self.root = root
                def return_to_selector_with_confirmation(self, tool_name, has_unsaved):
                    return True
            
            controller = MockController(test_root)
            bump_gui = BumpMapToolFrame(test_root, controller)
            
            # Find the test house selection frame
            test_house_frame = None
            for widget in bump_gui.main_frame.winfo_children():
                if isinstance(widget, ttk.LabelFrame):
                    if hasattr(widget, 'cget') and 'Test House Selection' in str(widget.cget('text')):
                        test_house_frame = widget
                        break
            
            if test_house_frame:
                # Count mousewheel bindings in the entire test house area
                binding_count = self.count_mousewheel_bindings(test_house_frame)
                
                if binding_count >= 3:  # Should have multiple bindings now
                    print(f"✅ Bump Map Tool Test House Selection has enhanced mousewheel support")
                    print(f"   📊 Found {binding_count} mousewheel bindings in the area")
                    self.test_results["Bump_Map_TestHouse_Enhanced"] = True
                else:
                    print(f"❌ Bump Map Tool Test House Selection has limited mousewheel support")
                    print(f"   📊 Only found {binding_count} mousewheel bindings")
                    self.test_results["Bump_Map_TestHouse_Enhanced"] = False
            else:
                print("❌ Could not find Test House Selection frame")
                self.test_results["Bump_Map_TestHouse_Enhanced"] = False
            
            test_root.destroy()
            return self.test_results.get("Bump_Map_TestHouse_Enhanced", False)
            
        except Exception as e:
            print(f"❌ Bump Map Tool enhanced test failed: {e}")
            self.test_results["Bump_Map_TestHouse_Enhanced"] = False
            return False
    
    def run_all_enhanced_tests(self):
        """Run all enhanced mousewheel tests"""
        print("🚀 TSK/MAP File Processor Tool - Enhanced Area-wide Mousewheel Test Suite")
        print("=" * 75)
        
        # Run individual tests
        ab_result = self.test_ab_map_enhanced_mousewheel()
        full_result = self.test_full_map_enhanced_mousewheel()
        bump_result = self.test_bump_map_enhanced_mousewheel()
        
        # Summary
        print("\n" + "=" * 75)
        print("📋 Enhanced Test Results Summary")
        print("=" * 75)
        
        total_tests = len(self.test_results)
        passed_tests = sum(self.test_results.values())
        
        for test_name, result in self.test_results.items():
            status = "✅ PASS" if result else "❌ FAIL"
            print(f"{test_name:<35} {status}")
        
        print("-" * 75)
        print(f"Total Tests: {total_tests}")
        print(f"Passed: {passed_tests}")
        print(f"Failed: {total_tests - passed_tests}")
        print(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
        
        if passed_tests == total_tests:
            print("\n🎉 All enhanced area-wide mousewheel tests PASSED!")
            print("✅ Users can now use mouse wheel ANYWHERE in the specified areas:")
            print("   • AB Map Tool - Entire File Information area")
            print("   • Full Map Tool - Entire Map File Selection area")
            print("   • Full Map Tool - Entire Processing Information area")
            print("   • Bump Map Tool - Entire Test House Selection area")
            print("\n🎯 No need to aim for scrollbars - wheel works everywhere in these areas!")
        else:
            print(f"\n⚠️  {total_tests - passed_tests} enhanced test(s) failed.")
        
        return passed_tests == total_tests


def main():
    """Main test function"""
    tester = EnhancedMousewheelTester()
    success = tester.run_all_enhanced_tests()
    
    if success:
        print("\n🎯 Enhanced area-wide mousewheel support is complete and working!")
        print("🖱️  Users can scroll anywhere in the target areas, not just on scrollbars!")
    else:
        print("\n🔧 Some enhanced mousewheel implementations need attention.")
    
    return success


if __name__ == "__main__":
    main()
