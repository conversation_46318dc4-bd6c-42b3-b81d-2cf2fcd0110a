#!/usr/bin/env python3
"""
NEPES Corporation Bump Map to TSK Map Processor
Specialized processor for NEPES format bump maps with binary TSK map output

Author: Yuribytes
Company: Chipone TE development Team
"""

import os
import struct
import re
from typing import Dict, List, Tuple, Optional, Any


class NEPESBumpProcessor:
    """NEPES Corporation specific bump map processor"""
    
    def __init__(self):
        self.bump_data = []  # 2D array of bump map values
        self.dummy_data = None  # Binary dummy map data
        self.header_info = {}
        self.rows = 0
        self.cols = 0
        self.processing_stats = {
            'total_positions': 0,
            'converted_to_63': 0,  # "00" positions
            'converted_to_59': 0,  # non-"00" positions
            'errors': 0
        }
    
    def parse_bump_map(self, file_path: str) -> bool:
        """Parse NEPES bump map file (text format with RowData lines)"""
        try:
            print(f"📖 Parsing NEPES bump map: {file_path}")
            
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
            
            # Extract header information
            self._extract_header_info(content)
            
            # Find and parse RowData lines
            row_data_lines = re.findall(r'RowData:(.*)', content)
            
            if not row_data_lines:
                print("❌ Error: No RowData found in bump map file")
                return False
            
            # Parse each row of data
            self.bump_data = []
            for i, line in enumerate(row_data_lines):
                # Split by spaces and clean up
                values = [v.strip() for v in line.strip().split() if v.strip()]
                self.bump_data.append(values)
                
                if i == 0:  # Log first row as sample
                    print(f"   Sample row data: {values[:10]}...")
            
            self.rows = len(self.bump_data)
            self.cols = len(self.bump_data[0]) if self.bump_data else 0
            
            print(f"✅ Parsed NEPES bump map: {self.rows} rows × {self.cols} columns")
            print(f"   Header info: {self.header_info}")
            
            return True
            
        except Exception as e:
            print(f"❌ Error parsing NEPES bump map: {e}")
            return False
    
    def _extract_header_info(self, content: str) -> None:
        """Extract header information from NEPES bump map"""
        header_patterns = {
            'DEVICE': r'DEVICE:(.+)',
            'LOT': r'LOT:(.+)',
            'WAFER': r'WAFER:(.+)',
            'ROWCT': r'ROWCT:(\d+)',
            'COLCT': r'COLCT:(\d+)',
            'REFPX': r'REFPX:(.+)',
            'REFPY': r'REFPY:(.+)',
            'FNLOC': r'FNLOC:(\d+)',
            'BCEQU': r'BCEQU:(.+)'
        }
        
        for key, pattern in header_patterns.items():
            match = re.search(pattern, content)
            if match:
                self.header_info[key] = match.group(1).strip()
    
    def load_dummy_map(self, file_path: str) -> bool:
        """Load dummy map file (binary TSK format)"""
        try:
            print(f"📖 Loading dummy map: {file_path}")
            
            with open(file_path, 'rb') as f:
                self.dummy_data = bytearray(f.read())
            
            print(f"✅ Loaded dummy map: {len(self.dummy_data)} bytes")
            return True
            
        except Exception as e:
            print(f"❌ Error loading dummy map: {e}")
            return False
    
    def _find_data_start_position(self) -> int:
        """Find the start position of map data in dummy file"""
        if not self.dummy_data:
            return -1
        
        # For TSK format, data typically starts after header
        # Look for patterns that indicate map data start
        
        # Method 1: Look for expected header size based on file structure
        # TSK files typically have headers of 1-4KB
        potential_starts = [1024, 2048, 4096, 512]
        
        for start_pos in potential_starts:
            if start_pos < len(self.dummy_data):
                # Check if this position has reasonable map data
                sample_size = min(100, len(self.dummy_data) - start_pos)
                sample = self.dummy_data[start_pos:start_pos + sample_size]
                
                # Count unique values - map data should have limited unique values
                unique_values = len(set(sample))
                if 2 <= unique_values <= 20:  # Reasonable range for map data
                    print(f"   Data start position detected: {start_pos}")
                    return start_pos
        
        # Fallback: use 2KB offset
        fallback_pos = 2048
        print(f"   Using fallback data start position: {fallback_pos}")
        return fallback_pos
    
    def apply_conversion_rules(self) -> bool:
        """Apply NEPES bump-to-TSK conversion rules"""
        if not self.bump_data or not self.dummy_data:
            print("❌ Error: Bump data or dummy data not loaded")
            return False
        
        try:
            print("🔄 Applying NEPES conversion rules...")
            
            data_start = self._find_data_start_position()
            if data_start == -1:
                print("❌ Error: Could not find data start position")
                return False
            
            # Reset statistics
            self.processing_stats = {
                'total_positions': 0,
                'converted_to_63': 0,
                'converted_to_59': 0,
                'errors': 0
            }
            
            # Apply conversion rules
            for row in range(min(self.rows, len(self.bump_data))):
                for col in range(min(self.cols, len(self.bump_data[row]))):
                    bump_value = self.bump_data[row][col]
                    
                    # Calculate position in dummy map
                    # Assuming row-major order mapping
                    map_position = data_start + (row * self.cols) + col
                    
                    if map_position < len(self.dummy_data):
                        self.processing_stats['total_positions'] += 1
                        
                        # Apply NEPES conversion rules:
                        # "00" -> 63 (0x3F) - valid bin positions
                        # everything else ("__", "0C", etc.) -> 59 (0x3B) - invalid/empty positions
                        if bump_value == "00":
                            self.dummy_data[map_position] = 63  # 0x3F
                            self.processing_stats['converted_to_63'] += 1
                        else:
                            self.dummy_data[map_position] = 59  # 0x3B
                            self.processing_stats['converted_to_59'] += 1
                    else:
                        self.processing_stats['errors'] += 1
            
            print(f"✅ Conversion completed:")
            print(f"   Total positions processed: {self.processing_stats['total_positions']}")
            print(f"   Converted to 63 ('00' positions): {self.processing_stats['converted_to_63']}")
            print(f"   Converted to 59 (other positions): {self.processing_stats['converted_to_59']}")
            print(f"   Errors: {self.processing_stats['errors']}")
            
            return True
            
        except Exception as e:
            print(f"❌ Error applying conversion rules: {e}")
            return False
    
    def save_output_map(self, output_path: str) -> bool:
        """Save the converted TSK map"""
        try:
            print(f"💾 Saving converted TSK map: {output_path}")
            
            with open(output_path, 'wb') as f:
                f.write(self.dummy_data)
            
            file_size = os.path.getsize(output_path)
            print(f"✅ Saved converted TSK map: {file_size} bytes")
            return True
            
        except Exception as e:
            print(f"❌ Error saving output map: {e}")
            return False
    
    def process_maps(self, bump_map_path: str, dummy_map_path: str, output_path: str) -> bool:
        """Complete NEPES processing pipeline"""
        print("🚀 Starting NEPES Corporation bump map processing...")
        print("=" * 60)
        
        try:
            # Step 1: Parse bump map
            print("📋 Step 1: Parsing bump map...")
            if not self.parse_bump_map(bump_map_path):
                return False
            
            # Step 2: Load dummy map
            print("\n📋 Step 2: Loading dummy map...")
            if not self.load_dummy_map(dummy_map_path):
                return False
            
            # Step 3: Apply conversion rules
            print("\n📋 Step 3: Applying conversion rules...")
            if not self.apply_conversion_rules():
                return False
            
            # Step 4: Save output
            print("\n📋 Step 4: Saving output...")
            if not self.save_output_map(output_path):
                return False
            
            print("\n" + "=" * 60)
            print("🎉 NEPES processing completed successfully!")
            print(f"📊 Processing Summary:")
            print(f"   Input bump map: {bump_map_path}")
            print(f"   Input dummy map: {dummy_map_path}")
            print(f"   Output TSK map: {output_path}")
            print(f"   Map dimensions: {self.rows} × {self.cols}")
            print(f"   Valid bins (63): {self.processing_stats['converted_to_63']}")
            print(f"   Invalid/Empty (59): {self.processing_stats['converted_to_59']}")
            
            return True
            
        except Exception as e:
            print(f"❌ NEPES processing failed: {e}")
            return False
    
    def get_processing_stats(self) -> Dict[str, Any]:
        """Get detailed processing statistics"""
        return {
            'test_house': 'NEPES Corporation',
            'dimensions': f"{self.rows} × {self.cols}",
            'header_info': self.header_info,
            'processing_stats': self.processing_stats
        }


def main():
    """Test function for standalone NEPES processor"""
    print("🧪 NEPES Bump Processor - Standalone Test")
    print("=" * 50)
    
    processor = NEPESBumpProcessor()
    
    # Test with provided files
    bump_file = "test/D97127.09"
    dummy_file = "test/009.NNS157-09-E4"
    output_file = "test/nepes_converted_output.tsk"
    
    if os.path.exists(bump_file) and os.path.exists(dummy_file):
        success = processor.process_maps(bump_file, dummy_file, output_file)
        
        if success:
            stats = processor.get_processing_stats()
            print(f"\n📊 Final Statistics: {stats}")
        
        return success
    else:
        print(f"❌ Test files not found!")
        print(f"   Bump file: {bump_file} - {'✅' if os.path.exists(bump_file) else '❌'}")
        print(f"   Dummy file: {dummy_file} - {'✅' if os.path.exists(dummy_file) else '❌'}")
        return False


if __name__ == "__main__":
    main()
