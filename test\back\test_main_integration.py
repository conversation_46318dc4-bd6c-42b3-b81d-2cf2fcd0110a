#!/usr/bin/env python3
"""
测试主应用程序集成
验证Bump Map Advanced Tool是否正确集成到主应用程序中

Author: Yuribytes
Company: Chipone TE development Team
"""

import os
import sys
import tkinter as tk

# 添加父目录到路径
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, parent_dir)

def test_main_application_integration():
    """测试主应用程序集成"""
    print("🧪 测试主应用程序集成")
    print("=" * 60)
    
    try:
        # 导入主应用程序
        from main_application import TSKMapApplication
        
        print("✅ 主应用程序导入成功")
        
        # 创建应用程序实例
        app = TSKMapApplication()
        print("✅ 应用程序实例创建成功")
        
        # 检查是否有show_bump_map_tool方法
        if hasattr(app, 'show_bump_map_tool'):
            print("✅ show_bump_map_tool方法存在")
        else:
            print("❌ show_bump_map_tool方法不存在")
            return False
        
        # 检查工具选择器
        if hasattr(app, 'tool_selector'):
            print("✅ tool_selector属性存在")
        else:
            print("❌ tool_selector属性不存在")
            return False
        
        # 测试显示工具选择器
        app.show_tool_selector()
        print("✅ 工具选择器显示成功")
        
        # 检查工具选择器是否有launch_bump_map_tool方法
        if hasattr(app.tool_selector, 'launch_bump_map_tool'):
            print("✅ launch_bump_map_tool方法存在")
        else:
            print("❌ launch_bump_map_tool方法不存在")
            return False
        
        # 测试显示Bump Map Advanced Tool
        try:
            app.show_bump_map_tool()
            print("✅ Bump Map Advanced Tool显示成功")
            
            # 检查是否使用了正确的Advanced Frame
            if app.bump_map_tool:
                class_name = app.bump_map_tool.__class__.__name__
                if class_name == "BumpMapAdvancedFrame":
                    print("✅ 使用了正确的BumpMapAdvancedFrame")
                else:
                    print(f"❌ 使用了错误的类: {class_name}")
                    return False
            else:
                print("❌ bump_map_tool未创建")
                return False
                
        except Exception as e:
            print(f"❌ 显示Bump Map Advanced Tool失败: {e}")
            return False
        
        # 清理
        app.root.quit()
        app.root.destroy()
        
        return True
        
    except Exception as e:
        print(f"❌ 集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_tool_selector_frame():
    """测试工具选择器框架"""
    print(f"\n🧪 测试工具选择器框架")
    print("=" * 60)
    
    try:
        # 导入工具选择器框架
        from tool_selector_frame import ToolSelectorFrame
        
        print("✅ 工具选择器框架导入成功")
        
        # 创建测试根窗口
        root = tk.Tk()
        root.withdraw()  # 隐藏窗口
        
        # Mock应用控制器
        class MockController:
            def __init__(self):
                self.root = root
            def show_bump_map_tool(self):
                print("Mock: show_bump_map_tool called")
        
        controller = MockController()
        
        # 创建工具选择器
        selector = ToolSelectorFrame(root, controller)
        print("✅ 工具选择器创建成功")
        
        # 检查是否有launch_bump_map_tool方法
        if hasattr(selector, 'launch_bump_map_tool'):
            print("✅ launch_bump_map_tool方法存在")
        else:
            print("❌ launch_bump_map_tool方法不存在")
            return False
        
        # 测试方法调用
        try:
            selector.launch_bump_map_tool()
            print("✅ launch_bump_map_tool方法调用成功")
        except Exception as e:
            print(f"❌ launch_bump_map_tool方法调用失败: {e}")
            return False
        
        # 清理
        root.destroy()
        
        return True
        
    except Exception as e:
        print(f"❌ 工具选择器框架测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_advanced_frame_import():
    """测试Advanced Frame导入"""
    print(f"\n🧪 测试Advanced Frame导入")
    print("=" * 60)
    
    try:
        # 导入Advanced Frame
        from bump_map_advanced_frame import BumpMapAdvancedFrame
        
        print("✅ BumpMapAdvancedFrame导入成功")
        
        # 导入Advanced Processor
        from nepes_advanced_processor import NEPESAdvancedProcessor
        
        print("✅ NEPESAdvancedProcessor导入成功")
        
        # 创建测试根窗口
        root = tk.Tk()
        root.withdraw()  # 隐藏窗口
        
        # Mock应用控制器
        class MockController:
            def __init__(self):
                self.root = root
            def return_to_selector_with_confirmation(self, tool_name, has_unsaved):
                print(f"Mock: return_to_selector_with_confirmation({tool_name}, {has_unsaved})")
        
        controller = MockController()
        
        # 创建Advanced Frame
        advanced_frame = BumpMapAdvancedFrame(root, controller)
        print("✅ BumpMapAdvancedFrame创建成功")
        
        # 检查关键方法
        methods_to_check = [
            'show', 'hide', 'process_maps', 'clear_memory',
            'exit_application', 'update_format_preview'
        ]
        
        for method in methods_to_check:
            if hasattr(advanced_frame, method):
                print(f"✅ {method}方法存在")
            else:
                print(f"❌ {method}方法不存在")
                return False
        
        # 检查配置变量
        config_vars = ['pass_value', 'fail_value']
        for var in config_vars:
            if hasattr(advanced_frame, var):
                print(f"✅ {var}变量存在")
            else:
                print(f"❌ {var}变量不存在")
                return False
        
        # 清理
        root.destroy()
        
        return True
        
    except Exception as e:
        print(f"❌ Advanced Frame导入测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🧪 主应用程序集成测试")
    print("=" * 70)
    
    test_results = []
    
    try:
        # 测试1: 主应用程序集成
        result1 = test_main_application_integration()
        test_results.append(("主应用程序集成", result1))
        
        # 测试2: 工具选择器框架
        result2 = test_tool_selector_frame()
        test_results.append(("工具选择器框架", result2))
        
        # 测试3: Advanced Frame导入
        result3 = test_advanced_frame_import()
        test_results.append(("Advanced Frame导入", result3))
        
        # 总结
        print("\n" + "=" * 70)
        print("🎉 集成测试结果总结:")
        
        passed = 0
        for test_name, result in test_results:
            status = "✅ 通过" if result else "❌ 失败"
            print(f"   {test_name}: {status}")
            if result:
                passed += 1
        
        print(f"\n📊 总体结果: {passed}/{len(test_results)} 测试通过")
        
        if passed == len(test_results):
            print("\n🎯 🎉 集成测试完全成功！")
            print("✅ Bump Map Advanced Tool已正确集成到主应用程序")
            print("✅ 工具选择器正确显示Advanced Tool")
            print("✅ 所有组件导入和创建正常")
            print("\n🚀 现在可以运行 python main.py 来使用新功能！")
        else:
            print("⚠️  部分集成测试失败，需要进一步检查")
        
        return passed == len(test_results)
        
    except Exception as e:
        print(f"❌ 集成测试过程出错: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
