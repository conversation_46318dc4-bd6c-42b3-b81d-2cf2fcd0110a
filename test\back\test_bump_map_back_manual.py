#!/usr/bin/env python3
"""
Manual test for Bump Map Tool Back button functionality
This test opens the actual GUI for manual verification

Author: Yuribytes
Company: Chipone TE development Team
"""

import sys
import os
import tkinter as tk
from tkinter import ttk

# Add parent directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


def test_bump_map_back_button_manual():
    """Manual test for Bump Map Tool Back button"""
    print("🚀 Bump Map Tool - Manual Back Button Test")
    print("=" * 50)
    
    try:
        from bump_map_tool_frame import BumpMapToolFrame
        
        # Create test window
        test_root = tk.Tk()
        test_root.title("Bump Map Tool - Manual Back Button Test")
        test_root.geometry("1000x800")
        
        # Mock app controller that shows feedback
        class TestController:
            def __init__(self, root):
                self.root = root
                self.return_calls = 0
                self.selector_calls = 0
            
            def return_to_selector_with_confirmation(self, tool_name, has_unsaved):
                self.return_calls += 1
                print(f"✅ return_to_selector_with_confirmation called for {tool_name}")
                print(f"   Has unsaved work: {has_unsaved}")
                return True
            
            def show_tool_selector(self):
                self.selector_calls += 1
                print(f"✅ show_tool_selector called (call #{self.selector_calls})")
                
                # Show success message
                success_popup = tk.Toplevel(self.root)
                success_popup.title("Test Success")
                success_popup.geometry("400x200")
                success_popup.resizable(False, False)
                
                # Center popup
                x = self.root.winfo_x() + (self.root.winfo_width() // 2) - 200
                y = self.root.winfo_y() + (self.root.winfo_height() // 2) - 100
                success_popup.geometry(f"400x200+{x}+{y}")
                
                frame = ttk.Frame(success_popup, padding="20")
                frame.pack(fill=tk.BOTH, expand=True)
                
                message = ("🎉 Back Button Test Successful!\n\n"
                          "✅ Memory cleanup executed\n"
                          "✅ Cleanup popup displayed\n"
                          "✅ State cleared\n"
                          "✅ Return to main menu triggered\n\n"
                          "Bump Map Tool Back button now works\n"
                          "exactly like Full Map Tool!")
                
                ttk.Label(frame, text=message, justify=tk.CENTER,
                         font=("Arial", 10)).pack(expand=True)
                
                ttk.Button(frame, text="Close Test", 
                          command=self.root.quit).pack(pady=(10, 0))
        
        controller = TestController(test_root)
        bump_gui = BumpMapToolFrame(test_root, controller)
        bump_gui.show()
        
        # Add some test data to simulate unsaved work
        bump_gui.bump_map_file_path.set("test_bump.map")
        bump_gui.dummy_map_file_path.set("test_dummy.map")
        bump_gui.processing_count = 1
        
        # Create a mock processor for memory testing
        class MockProcessor:
            def get_memory_usage_mb(self):
                return 25.7  # Simulate some memory usage
        
        bump_gui.bump_processor = MockProcessor()
        
        # Update status to show test state
        bump_gui.status_var.set("Test Mode: Files loaded, ready to test Back button")
        
        # Add instructions
        instructions = tk.Toplevel(test_root)
        instructions.title("Test Instructions")
        instructions.geometry("500x300")
        instructions.resizable(False, False)
        
        # Center instructions
        x = test_root.winfo_x() + 50
        y = test_root.winfo_y() + 50
        instructions.geometry(f"500x300+{x}+{y}")
        
        inst_frame = ttk.Frame(instructions, padding="20")
        inst_frame.pack(fill=tk.BOTH, expand=True)
        
        inst_text = ("📋 Manual Test Instructions\n\n"
                    "1. The Bump Map Tool window is now open\n"
                    "2. Test files are pre-loaded to simulate unsaved work\n"
                    "3. Click the '← Back' button in the Bump Map Tool\n"
                    "4. You should see:\n"
                    "   • Memory cleanup popup (1 second)\n"
                    "   • Console messages about memory freed\n"
                    "   • Success popup confirming the test\n\n"
                    "5. Compare this behavior with Full Map Tool\n\n"
                    "Expected: Same cleanup and popup behavior\n"
                    "as Full Map Tool")
        
        ttk.Label(inst_frame, text=inst_text, justify=tk.LEFT,
                 font=("Arial", 9)).pack(expand=True)
        
        ttk.Button(inst_frame, text="Start Test", 
                  command=instructions.destroy).pack(pady=(10, 0))
        
        print("📋 Manual Test Setup Complete")
        print("=" * 50)
        print("Instructions:")
        print("1. Click 'Start Test' to close instructions")
        print("2. Click the '← Back' button in Bump Map Tool")
        print("3. Observe the cleanup popup and behavior")
        print("4. Compare with Full Map Tool behavior")
        print("=" * 50)
        
        # Start the GUI
        test_root.mainloop()
        
        print(f"\n📊 Test Results:")
        print(f"   return_to_selector_with_confirmation calls: {controller.return_calls}")
        print(f"   show_tool_selector calls: {controller.selector_calls}")
        
        if controller.selector_calls > 0:
            print("✅ Manual test completed successfully!")
            return True
        else:
            print("❌ Manual test incomplete - Back button not clicked")
            return False
            
    except Exception as e:
        print(f"❌ Manual test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Main test function"""
    print("🧪 Starting Bump Map Tool Back Button Manual Test")
    print("This test will open the GUI for manual verification\n")
    
    success = test_bump_map_back_button_manual()
    
    if success:
        print("\n🎯 Manual test completed!")
        print("✅ Bump Map Tool Back button functionality verified")
    else:
        print("\n⚠️  Manual test incomplete or failed")
    
    return success


if __name__ == "__main__":
    main()
