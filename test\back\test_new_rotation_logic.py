#!/usr/bin/env python3
"""
Test New Rotation Logic - Verify the improved rotation header calculation
"""

import sys
import os

# Add parent directory to path
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.insert(0, parent_dir)

from excel_output import ExcelOutputHandler


def test_rotation_header_calculation():
    """Test the new rotation header calculation logic"""
    print("Testing New Rotation Header Calculation Logic")
    print("=" * 50)

    # Create ExcelOutputHandler instance for testing
    excel_output = ExcelOutputHandler()
    
    # Test with sample dimensions (similar to your data: 9 rows x 271 cols)
    orig_rows = 9
    orig_cols = 271
    
    print(f"Original dimensions: {orig_rows} rows x {orig_cols} cols")
    print()
    
    # Test all rotation angles
    angles = [0, 90, 180, 270]
    
    for angle in angles:
        print(f"Testing {angle}° rotation:")
        
        try:
            row_headers, col_headers = excel_output._calculate_rotated_headers(orig_rows, orig_cols, angle)
            
            print(f"  Row headers (first 10): {row_headers[:10]}")
            print(f"  Col headers (first 10): {col_headers[:10]}")
            print(f"  Row headers count: {len(row_headers)}")
            print(f"  Col headers count: {len(col_headers)}")
            
            # Verify expected dimensions
            if angle == 0 or angle == 180:
                expected_row_count = orig_rows
                expected_col_count = orig_cols
            else:  # 90 or 270
                expected_row_count = orig_cols
                expected_col_count = orig_rows
            
            if len(row_headers) == expected_row_count and len(col_headers) == expected_col_count:
                print(f"  ✅ Header counts are correct")
            else:
                print(f"  ❌ Header counts are wrong. Expected: {expected_row_count} rows, {expected_col_count} cols")
                return False
            
            # Verify header values
            if verify_header_values(angle, row_headers, col_headers, orig_rows, orig_cols):
                print(f"  ✅ Header values are correct")
            else:
                print(f"  ❌ Header values are incorrect")
                return False
                
        except Exception as e:
            print(f"  ❌ Error calculating headers: {e}")
            return False
        
        print()
    
    return True


def verify_header_values(angle, row_headers, col_headers, orig_rows, orig_cols):
    """Verify that header values match expected rotation logic"""
    
    if angle == 0:
        # 0° - No rotation
        expected_rows = list(range(1, orig_rows + 1))  # 1, 2, 3, ..., 9
        expected_cols = list(range(1, orig_cols + 1))  # 1, 2, 3, ..., 271
        
    elif angle == 90:
        # 90° Clockwise
        expected_rows = list(range(1, orig_cols + 1))  # 1, 2, 3, ..., 271
        expected_cols = list(range(orig_rows, 0, -1))  # 9, 8, 7, ..., 1
        
    elif angle == 180:
        # 180°
        expected_rows = list(range(orig_rows, 0, -1))  # 9, 8, 7, ..., 1
        expected_cols = list(range(orig_cols, 0, -1))  # 271, 270, 269, ..., 1
        
    elif angle == 270:
        # 270° Clockwise
        expected_rows = list(range(orig_cols, 0, -1))  # 271, 270, 269, ..., 1
        expected_cols = list(range(1, orig_rows + 1))  # 1, 2, 3, ..., 9
        
    else:
        return False
    
    return row_headers == expected_rows and col_headers == expected_cols


def test_rotation_logic_explanation():
    """Explain the rotation logic clearly"""
    print("Rotation Logic Explanation")
    print("=" * 30)
    
    print("Original Data (0°):")
    print("  Grid: 9 rows x 271 cols")
    print("  Row headers: 1, 2, 3, 4, 5, 6, 7, 8, 9")
    print("  Col headers: 1, 2, 3, ..., 271")
    print()
    
    print("90° Clockwise Rotation:")
    print("  Grid: 271 rows x 9 cols (dimensions swapped)")
    print("  Row headers: 1, 2, 3, ..., 271 (new row positions)")
    print("  Col headers: 9, 8, 7, 6, 5, 4, 3, 2, 1 (original rows, reversed)")
    print("  Logic: Original top-left becomes bottom-left")
    print()
    
    print("180° Rotation:")
    print("  Grid: 9 rows x 271 cols (same dimensions)")
    print("  Row headers: 9, 8, 7, 6, 5, 4, 3, 2, 1 (original rows, reversed)")
    print("  Col headers: 271, 270, 269, ..., 1 (original cols, reversed)")
    print("  Logic: Original top-left becomes bottom-right")
    print()
    
    print("270° Clockwise Rotation:")
    print("  Grid: 271 rows x 9 cols (dimensions swapped)")
    print("  Row headers: 271, 270, 269, ..., 1 (original cols, reversed)")
    print("  Col headers: 1, 2, 3, 4, 5, 6, 7, 8, 9 (new col positions)")
    print("  Logic: Original top-left becomes top-right")
    print()
    
    return True


def main():
    """Main test function"""
    print("New Rotation Logic Test")
    print("Testing improved rotation header calculation")
    print("=" * 60)
    
    success_count = 0
    total_tests = 2
    
    # Test 1: Header calculation
    if test_rotation_header_calculation():
        success_count += 1
        print("✅ Test 1 passed: Header calculation")
    else:
        print("❌ Test 1 failed: Header calculation")
    
    # Test 2: Logic explanation
    if test_rotation_logic_explanation():
        success_count += 1
        print("✅ Test 2 passed: Logic explanation")
    else:
        print("❌ Test 2 failed: Logic explanation")
    
    print("\n" + "=" * 60)
    print(f"New Rotation Logic Test Results: {success_count}/{total_tests} tests passed")
    
    if success_count == total_tests:
        print("🎉 New rotation logic verified!")
        print("\nKey Improvements:")
        print("✅ Pre-calculate header values based on rotation logic")
        print("✅ Separate calculation from Excel writing")
        print("✅ Clear and predictable header numbering")
        print("✅ Proper dimension handling for all rotation angles")
        
        print(f"\nRotation Summary:")
        print(f"• 0°: Original numbering")
        print(f"• 90°: Rows=1..271, Cols=9..1")
        print(f"• 180°: Rows=9..1, Cols=271..1")
        print(f"• 270°: Rows=271..1, Cols=1..9")
        
    else:
        print(f"⚠️  {total_tests - success_count} tests failed")


if __name__ == "__main__":
    main()
