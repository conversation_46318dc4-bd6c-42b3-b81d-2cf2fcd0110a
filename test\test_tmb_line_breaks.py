#!/usr/bin/env python3
"""
Test script for TMB line breaks modification
Tests the modified line breaks in TMB content
"""

import os
import sys

# Add parent directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from tmb_processor import TMBProcessor
from config_reader import Config<PERSON><PERSON><PERSON>


def test_tmb_line_breaks():
    """Test TMB line breaks modification"""
    print("=" * 60)
    print("Testing TMB line breaks modification...")
    
    # Setup
    tmb_processor = TMBProcessor()
    config_reader = ConfigReader()
    
    # Load config
    config_file = "test/CP1_program_bin.xlsx"
    if not config_reader.read_config_file(config_file):
        print("❌ Failed to load config file")
        return False
    
    tmb_processor.set_config_reader(config_reader)
    
    # Test with sample MAP file
    map_file = "test/019.3AD416-19-F4"
    if not os.path.exists(map_file):
        print(f"❌ MAP file not found: {map_file}")
        return False
    
    # Process MAP file to TMB
    tmb_output = tmb_processor.process_map_to_tmb(map_file)
    
    if tmb_output and os.path.exists(tmb_output):
        filename_only = os.path.basename(tmb_output)
        print(f"✅ TMB file generated: {filename_only}")
        
        # Read and analyze line breaks
        with open(tmb_output, 'r', encoding='utf-8') as f:
            content = f.read()
        
        lines = content.split('\n')
        
        # Find the sections and analyze line breaks
        upper_part_end = -1
        middle_part_start = -1
        middle_part_end = -1
        lower_part_start = -1
        
        for i, line in enumerate(lines):
            if "============ Wafer Information (Nepes) ===========" in line:
                middle_part_start = i
                # Look backwards to find where upper part ends
                for j in range(i-1, -1, -1):
                    if lines[j].strip() != "":
                        upper_part_end = j
                        break
                break
        
        for i, line in enumerate(lines):
            if line.startswith("Cat 00:"):
                lower_part_start = i
                # Look backwards to find where middle part ends
                for j in range(i-1, -1, -1):
                    if lines[j].strip() != "":
                        middle_part_end = j
                        break
                break
        
        print(f"\nLine break analysis:")
        
        # Analyze line breaks between upper and middle parts
        if upper_part_end != -1 and middle_part_start != -1:
            empty_lines_1 = middle_part_start - upper_part_end - 1
            print(f"Between upper and middle parts: {empty_lines_1} empty lines")
            
            # Show the transition
            print(f"Upper part ends at line {upper_part_end + 1}: '{lines[upper_part_end][:50]}...'")
            for i in range(upper_part_end + 1, middle_part_start):
                print(f"Line {i + 1}: '{lines[i]}' (empty: {lines[i] == ''})")
            print(f"Middle part starts at line {middle_part_start + 1}: '{lines[middle_part_start][:50]}...'")
        
        # Analyze line breaks between middle and lower parts
        if middle_part_end != -1 and lower_part_start != -1:
            empty_lines_2 = lower_part_start - middle_part_end - 1
            print(f"\nBetween middle and lower parts: {empty_lines_2} empty lines")
            
            # Show the transition
            print(f"Middle part ends at line {middle_part_end + 1}: '{lines[middle_part_end][:50]}...'")
            for i in range(middle_part_end + 1, lower_part_start):
                print(f"Line {i + 1}: '{lines[i]}' (empty: {lines[i] == ''})")
            print(f"Lower part starts at line {lower_part_start + 1}: '{lines[lower_part_start][:50]}...'")
        
        # Verify the line breaks are correct (should be 2 empty lines each)
        success = True
        if empty_lines_1 != 2:
            print(f"❌ Expected 2 empty lines between upper and middle parts, got {empty_lines_1}")
            success = False
        else:
            print("✅ Correct line breaks between upper and middle parts")
        
        if empty_lines_2 != 2:
            print(f"❌ Expected 2 empty lines between middle and lower parts, got {empty_lines_2}")
            success = False
        else:
            print("✅ Correct line breaks between middle and lower parts")
        
        print(f"\nTMB file saved to: {tmb_output}")
        return success
    else:
        print("❌ Failed to generate TMB file")
        return False


def main():
    """Main test function"""
    print("Testing TMB line breaks modification")
    print("=" * 60)
    
    # Change to script directory
    script_dir = os.path.dirname(os.path.abspath(__file__))
    parent_dir = os.path.dirname(script_dir)
    os.chdir(parent_dir)
    
    print(f"Working directory: {os.getcwd()}")
    
    # Run test
    try:
        result = test_tmb_line_breaks()
        if result:
            print("\n✅ TMB line breaks test completed successfully")
            print("The TMB file now has two line breaks (empty lines) between sections")
        else:
            print("\n❌ TMB line breaks test failed")
    except Exception as e:
        print(f"\n❌ Test error: {e}")


if __name__ == "__main__":
    main()
