#!/usr/bin/env python3
"""
Launcher GUI - Main entry point for TSK/MAP processing tools
Provides choice between AB Map Tool and Full Map Generation Tool
"""

import tkinter as tk
from tkinter import ttk
import os


class LauncherGUI:
    """Main launcher GUI for selecting processing tools"""
    
    def __init__(self, root):
        self.root = root
        self.setup_window()
        self.create_widgets()
        self.center_window()
    
    def setup_window(self):
        """Setup main window properties"""
        self.root.title("TSK/MAP Processing Tools - Launcher")
        self.root.geometry("500x350")
        self.root.resizable(True, True)
        self.root.minsize(450, 300)
    
    def create_widgets(self):
        """Create and layout GUI widgets"""
        # Main frame with padding
        main_frame = ttk.Frame(self.root, padding="20")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Configure grid weights
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(0, weight=1)
        
        # Title
        title_label = ttk.Label(main_frame, text="TSK/MAP Processing Tools", 
                               font=('Arial', 16, 'bold'))
        title_label.grid(row=0, column=0, pady=(0, 20))
        
        # Subtitle
        subtitle_label = ttk.Label(main_frame, text="Choose your processing tool:", 
                                  font=('Arial', 12))
        subtitle_label.grid(row=1, column=0, pady=(0, 30))
        
        # Tool selection frame
        tools_frame = ttk.Frame(main_frame)
        tools_frame.grid(row=2, column=0, sticky=(tk.W, tk.E), pady=(0, 30))
        tools_frame.columnconfigure(0, weight=1)
        tools_frame.columnconfigure(1, weight=1)
        
        # AB Map Tool button
        ab_map_frame = ttk.LabelFrame(tools_frame, text="AB Map Tool", padding="15")
        ab_map_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 10))
        
        ab_map_desc = ttk.Label(ab_map_frame, text="Process single TSK/MAP file\n• Device information extraction\n• Bin statistics analysis\n• Excel output with formatting\n• Configuration file support", 
                               justify=tk.LEFT, wraplength=200)
        ab_map_desc.grid(row=0, column=0, pady=(0, 15))
        
        ab_map_button = ttk.Button(ab_map_frame, text="Launch AB Map Tool", 
                                  command=self.launch_ab_map_tool, width=20)
        ab_map_button.grid(row=1, column=0)
        
        # Full Map Tool button
        full_map_frame = ttk.LabelFrame(tools_frame, text="Full Map Generation Tool", padding="15")
        full_map_frame.grid(row=0, column=1, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(10, 0))
        
        full_map_desc = ttk.Label(full_map_frame, text="Process multiple TSK/MAP files\n• Batch processing support\n• Multiple Excel sheets\n• Unified configuration\n• Summary report generation", 
                                 justify=tk.LEFT, wraplength=200)
        full_map_desc.grid(row=0, column=0, pady=(0, 15))
        
        full_map_button = ttk.Button(full_map_frame, text="Launch Full Map Tool", 
                                    command=self.launch_full_map_tool, width=20)
        full_map_button.grid(row=1, column=0)
        
        # Info section
        info_frame = ttk.LabelFrame(main_frame, text="Information", padding="10")
        info_frame.grid(row=3, column=0, sticky=(tk.W, tk.E), pady=(0, 20))
        
        info_text = ttk.Label(info_frame, text="• AB Map Tool: Traditional single-file processing\n• Full Map Tool: Batch processing for multiple files\n• Both tools support configuration files and bin name mapping", 
                             justify=tk.LEFT)
        info_text.grid(row=0, column=0, sticky=tk.W)
        
        # Exit button
        exit_button = ttk.Button(main_frame, text="Exit", command=self.root.quit, width=15)
        exit_button.grid(row=4, column=0, pady=(10, 0))
    
    def center_window(self):
        """Center the window on screen"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f"{width}x{height}+{x}+{y}")
    
    def launch_ab_map_tool(self):
        """Launch the AB Map Tool (existing functionality)"""
        try:
            # Hide launcher window
            self.root.withdraw()
            
            # Import and launch AB Map Tool
            from tsk_map_gui import TSKMapGUI
            
            # Create new window for AB Map Tool
            ab_map_window = tk.Toplevel()
            ab_map_gui = TSKMapGUI(ab_map_window)
            
            # When AB Map Tool closes, show launcher again
            def on_ab_map_close():
                ab_map_window.destroy()
                self.root.deiconify()
            
            ab_map_window.protocol("WM_DELETE_WINDOW", on_ab_map_close)
            
        except Exception as e:
            import tkinter.messagebox as messagebox
            messagebox.showerror("Error", f"Failed to launch AB Map Tool:\n{e}")
            self.root.deiconify()
    
    def launch_full_map_tool(self):
        """Launch the Full Map Generation Tool (new functionality)"""
        try:
            # Hide launcher window
            self.root.withdraw()
            
            # Import and launch Full Map Tool
            from full_map_gui import FullMapGUI
            
            # Create new window for Full Map Tool
            full_map_window = tk.Toplevel()
            full_map_gui = FullMapGUI(full_map_window)
            
            # When Full Map Tool closes, show launcher again
            def on_full_map_close():
                full_map_window.destroy()
                self.root.deiconify()
            
            full_map_window.protocol("WM_DELETE_WINDOW", on_full_map_close)
            
        except Exception as e:
            import tkinter.messagebox as messagebox
            messagebox.showerror("Error", f"Failed to launch Full Map Tool:\n{e}")
            self.root.deiconify()


def main():
    """Main function"""
    root = tk.Tk()
    LauncherGUI(root)
    root.mainloop()


if __name__ == "__main__":
    main()
