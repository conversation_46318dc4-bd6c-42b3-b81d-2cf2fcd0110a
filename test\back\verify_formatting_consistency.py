#!/usr/bin/env python3
"""
Verify formatting consistency for Worker File 5 enhancement
Checks that the new 4 columns have consistent formatting with existing bin columns
"""

import os
import sys
from openpyxl import load_workbook

def verify_header_formatting():
    """Verify that new headers have consistent formatting with bin headers"""
    print("🔍 Verifying Header Formatting Consistency...")
    print("=" * 60)
    
    try:
        # Load the demo output file
        output_file = "demo_worker_file5_output.xlsx"
        if not os.path.exists(output_file):
            print(f"❌ Output file not found: {output_file}")
            return False
            
        wb = load_workbook(output_file)
        if "Bin_Summary" not in wb.sheetnames:
            print("❌ Bin_Summary sheet not found")
            return False
            
        ws = wb["Bin_Summary"]
        
        # Check bin header formatting (reference columns)
        print("📋 Reference Bin Header Formatting (C128 - Column 131):")
        ref_cell = ws.cell(row=5, column=131)  # C128 column
        print(f"  Font: {ref_cell.font.name}, Size: {ref_cell.font.size}, Bold: {ref_cell.font.bold}")
        print(f"  Font Color: {ref_cell.font.color}")
        print(f"  Fill Color: {ref_cell.fill.start_color}")
        print(f"  Border: {ref_cell.border.top.style if ref_cell.border.top else 'None'}")
        
        # Check new header formatting
        print(f"\n📋 New Header Formatting:")
        new_headers = [
            (132, "Tester"),
            (133, "Probe card no."),
            (134, "Test Program"),
            (135, "Test Flow")
        ]
        
        all_consistent = True
        for col_num, header_name in new_headers:
            cell = ws.cell(row=5, column=col_num)
            print(f"\n  {header_name} (Column {col_num}):")
            print(f"    Font: {cell.font.name}, Size: {cell.font.size}, Bold: {cell.font.bold}")
            print(f"    Font Color: {cell.font.color}")
            print(f"    Fill Color: {cell.fill.start_color}")
            print(f"    Border: {cell.border.top.style if cell.border.top else 'None'}")
            
            # Compare with reference
            font_match = (cell.font.name == ref_cell.font.name and 
                         cell.font.size == ref_cell.font.size and 
                         cell.font.bold == ref_cell.font.bold)
            color_match = str(cell.font.color) == str(ref_cell.font.color)
            fill_match = str(cell.fill.start_color) == str(ref_cell.fill.start_color)
            
            if font_match and color_match and fill_match:
                print(f"    ✅ Formatting matches reference")
            else:
                print(f"    ❌ Formatting differs from reference")
                all_consistent = False
        
        wb.close()
        
        if all_consistent:
            print(f"\n✅ All new headers have consistent formatting!")
            return True
        else:
            print(f"\n⚠️  Some headers have inconsistent formatting")
            return False
            
    except Exception as e:
        print(f"❌ Error verifying header formatting: {e}")
        return False

def verify_data_formatting():
    """Verify that new data columns have consistent formatting with existing columns"""
    print("\n🔍 Verifying Data Row Formatting Consistency...")
    print("=" * 60)
    
    try:
        # Load the demo output file
        output_file = "demo_worker_file5_output.xlsx"
        wb = load_workbook(output_file)
        ws = wb["Bin_Summary"]
        
        # Check reference data cell formatting (bin column)
        print("📋 Reference Data Cell Formatting (C128 - Column 131, Row 6):")
        ref_cell = ws.cell(row=6, column=131)  # C128 column, first data row
        print(f"  Font: {ref_cell.font.name}, Size: {ref_cell.font.size}")
        print(f"  Font Color: {ref_cell.font.color}")
        print(f"  Fill Color: {ref_cell.fill.start_color}")
        print(f"  Alignment: {ref_cell.alignment.horizontal}")
        
        # Check new data cell formatting
        print(f"\n📋 New Data Cell Formatting (Row 6):")
        new_columns = [
            (132, "Tester"),
            (133, "Probe card no."),
            (134, "Test Program"),
            (135, "Test Flow")
        ]
        
        all_consistent = True
        for col_num, col_name in new_columns:
            cell = ws.cell(row=6, column=col_num)
            print(f"\n  {col_name} (Column {col_num}):")
            print(f"    Font: {cell.font.name}, Size: {cell.font.size}")
            print(f"    Font Color: {cell.font.color}")
            print(f"    Fill Color: {cell.fill.start_color}")
            print(f"    Alignment: {cell.alignment.horizontal}")
            print(f"    Value: '{cell.value}'")
            
            # Compare with reference (excluding font color which may vary)
            font_match = (cell.font.name == ref_cell.font.name and 
                         cell.font.size == ref_cell.font.size)
            fill_match = str(cell.fill.start_color) == str(ref_cell.fill.start_color)
            align_match = cell.alignment.horizontal == ref_cell.alignment.horizontal
            
            if font_match and fill_match and align_match:
                print(f"    ✅ Formatting matches reference")
            else:
                print(f"    ❌ Formatting differs from reference")
                all_consistent = False
        
        wb.close()
        
        if all_consistent:
            print(f"\n✅ All new data cells have consistent formatting!")
            return True
        else:
            print(f"\n⚠️  Some data cells have inconsistent formatting")
            return False
            
    except Exception as e:
        print(f"❌ Error verifying data formatting: {e}")
        return False

def main():
    """Run formatting consistency verification"""
    print("🚀 Worker File 5 - Formatting Consistency Verification")
    print("=" * 80)
    
    # Verify header formatting
    header_ok = verify_header_formatting()
    
    # Verify data formatting  
    data_ok = verify_data_formatting()
    
    # Summary
    print("\n" + "=" * 80)
    print("📊 Formatting Verification Summary:")
    print("=" * 80)
    
    print(f"Header Formatting: {'✅ CONSISTENT' if header_ok else '❌ INCONSISTENT'}")
    print(f"Data Formatting:   {'✅ CONSISTENT' if data_ok else '❌ INCONSISTENT'}")
    
    if header_ok and data_ok:
        print("\n🎉 All formatting is consistent! The new 4 columns match the existing style.")
    else:
        print("\n⚠️  Some formatting inconsistencies detected. Please check the output above.")
    
    print("=" * 80)
    return header_ok and data_ok

if __name__ == "__main__":
    main()
