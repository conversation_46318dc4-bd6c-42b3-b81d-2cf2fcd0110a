#!/usr/bin/env python3
"""
简化的Bump Map Tool功能测试
验证核心功能是否正常工作

Author: Yuribytes
Company: Chipone TE development Team
"""

import os
import sys
import tkinter as tk

# 添加父目录到路径
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, parent_dir)

def test_main_application_creation():
    """测试main_application创建"""
    print("🧪 测试main_application创建")
    print("=" * 50)
    
    try:
        from main_application import TSKMapApplication
        
        app = TSKMapApplication()
        app.root.withdraw()
        
        print("✅ TSKMapApplication创建成功")
        
        # 检查关键方法
        if hasattr(app, 'show_bump_map_tool'):
            print("✅ show_bump_map_tool方法存在")
        else:
            print("❌ show_bump_map_tool方法不存在")
            return False
        
        app.root.destroy()
        return True
        
    except Exception as e:
        print(f"❌ main_application创建失败: {e}")
        return False

def test_bump_map_tool_loading():
    """测试Bump Map Tool加载"""
    print(f"\n🧪 测试Bump Map Tool加载")
    print("=" * 50)
    
    try:
        from main_application import TSKMapApplication
        
        app = TSKMapApplication()
        app.root.withdraw()
        
        # 调用show_bump_map_tool
        app.show_bump_map_tool()
        
        # 检查bump_map_tool是否创建
        if app.bump_map_tool:
            print("✅ bump_map_tool实例创建成功")
        else:
            print("❌ bump_map_tool实例创建失败")
            return False
        
        # 检查current_tool设置
        if app.current_tool == "bump_map":
            print("✅ current_tool正确设置")
        else:
            print(f"❌ current_tool设置错误: {app.current_tool}")
            return False
        
        # 测试show/hide方法
        app.bump_map_tool.show()
        print("✅ show方法调用成功")
        
        app.bump_map_tool.hide()
        print("✅ hide方法调用成功")
        
        app.root.destroy()
        return True
        
    except Exception as e:
        print(f"❌ Bump Map Tool加载失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_bump_map_enhanced_frame_direct():
    """直接测试BumpMapEnhancedFrame"""
    print(f"\n🧪 直接测试BumpMapEnhancedFrame")
    print("=" * 50)
    
    try:
        from bump_map_enhanced_frame import BumpMapEnhancedFrame
        
        root = tk.Tk()
        root.withdraw()
        
        class MockController:
            def __init__(self):
                self.root = root
        
        controller = MockController()
        enhanced_frame = BumpMapEnhancedFrame(root, controller)
        
        print("✅ BumpMapEnhancedFrame创建成功")
        
        # 检查关键属性（在初始化后）
        if hasattr(enhanced_frame, 'bump_map_files'):
            print("✅ bump_map_files属性存在")
        else:
            print("❌ bump_map_files属性不存在")
            return False
        
        if hasattr(enhanced_frame, 'dummy_map_file_path'):
            print("✅ dummy_map_file_path属性存在")
        else:
            print("❌ dummy_map_file_path属性不存在")
            return False
        
        # 检查滚动组件
        if hasattr(enhanced_frame, 'canvas') and hasattr(enhanced_frame, 'scrollbar'):
            print("✅ 滚动组件存在")
        else:
            print("❌ 滚动组件不存在")
            return False
        
        root.destroy()
        return True
        
    except Exception as e:
        print(f"❌ BumpMapEnhancedFrame直接测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_tool_selector_integration():
    """测试tool_selector集成"""
    print(f"\n🧪 测试tool_selector集成")
    print("=" * 50)
    
    try:
        from tool_selector import ToolSelector
        
        selector = ToolSelector()
        
        # 检查select_bump_map_tool方法
        if hasattr(selector, 'select_bump_map_tool'):
            print("✅ select_bump_map_tool方法存在")
        else:
            print("❌ select_bump_map_tool方法不存在")
            return False
        
        # 测试方法调用
        selector.select_bump_map_tool()
        
        if selector.selected_tool == "bump_map":
            print("✅ 工具选择正确")
        else:
            print(f"❌ 工具选择错误: {selector.selected_tool}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ tool_selector集成测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🧪 Bump Map Tool简化功能测试")
    print("=" * 70)
    
    test_results = []
    
    try:
        # 测试1: main_application创建
        result1 = test_main_application_creation()
        test_results.append(("main_application创建", result1))
        
        # 测试2: BumpMapEnhancedFrame直接测试
        result2 = test_bump_map_enhanced_frame_direct()
        test_results.append(("BumpMapEnhancedFrame直接测试", result2))
        
        # 测试3: tool_selector集成
        result3 = test_tool_selector_integration()
        test_results.append(("tool_selector集成", result3))
        
        # 测试4: Bump Map Tool加载
        result4 = test_bump_map_tool_loading()
        test_results.append(("Bump Map Tool加载", result4))
        
        # 总结
        print("\n" + "=" * 70)
        print("🎉 简化功能测试结果总结:")
        
        passed = 0
        for test_name, result in test_results:
            status = "✅ 通过" if result else "❌ 失败"
            print(f"   {test_name}: {status}")
            if result:
                passed += 1
        
        print(f"\n📊 总体结果: {passed}/{len(test_results)} 测试通过")
        
        if passed == len(test_results):
            print("\n🎯 🎉 所有简化功能测试成功！")
            print("✅ 核心功能正常工作")
            print("✅ 集成流程完整")
            print("✅ 可以从主界面正常加载Bump Map Tool")
            print("\n🚀 建议:")
            print("   1. 运行 python main.py")
            print("   2. 选择 Bump Map Tool")
            print("   3. 开始使用功能")
        else:
            print("⚠️  部分功能测试失败")
            failed_tests = [name for name, result in test_results if not result]
            print(f"失败的测试: {', '.join(failed_tests)}")
        
        return passed == len(test_results)
        
    except Exception as e:
        print(f"❌ 测试过程出错: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
