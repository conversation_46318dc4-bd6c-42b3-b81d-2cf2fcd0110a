#!/usr/bin/env python3
"""
NEPES TSK Format Processor
Correctly processes NEPES bump map data according to TSK map format specifications
Uses proper TestResultCategory position calculation

Author: Yuribytes
Company: Chipone TE development Team
"""

import os
import struct
import re
from typing import Dict, List, Tuple, Optional, Any
from tsk_map_processor import TSKMapProcessor


class NEPESTSKProcessor:
    """NEPES Corporation TSK format processor with correct binary positioning"""
    
    def __init__(self):
        self.bump_data = []  # 2D array of bump map values
        self.tsk_processor = TSKMapProcessor()  # Use existing TSK processor
        self.header_info = {}
        self.rows = 0
        self.cols = 0
        self.processing_stats = {
            'total_positions': 0,
            'converted_to_63': 0,  # "00" positions -> bin 63
            'converted_to_59': 0,  # non-"00" positions -> bin 59
            'category_modifications': 0,
            'errors': 0
        }
    
    def parse_bump_map(self, file_path: str) -> bool:
        """Parse NEPES bump map file (text format with RowData lines)"""
        try:
            print(f"📖 Parsing NEPES bump map: {file_path}")
            
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
            
            # Extract header information
            self._extract_header_info(content)
            
            # Find and parse RowData lines
            row_data_lines = re.findall(r'RowData:(.*)', content)
            
            if not row_data_lines:
                print("❌ Error: No RowData found in bump map file")
                return False
            
            # Parse each row of data
            self.bump_data = []
            for i, line in enumerate(row_data_lines):
                # Split by spaces and clean up
                values = [v.strip() for v in line.strip().split() if v.strip()]
                self.bump_data.append(values)
                
                if i == 0:  # Log first row as sample
                    print(f"   Sample row data: {values[:10]}...")
            
            self.rows = len(self.bump_data)
            self.cols = len(self.bump_data[0]) if self.bump_data else 0
            
            print(f"✅ Parsed NEPES bump map: {self.rows} rows × {self.cols} columns")
            print(f"   Header info: {self.header_info}")
            
            return True
            
        except Exception as e:
            print(f"❌ Error parsing NEPES bump map: {e}")
            return False
    
    def _extract_header_info(self, content: str) -> None:
        """Extract header information from NEPES bump map"""
        header_patterns = {
            'DEVICE': r'DEVICE:(.+)',
            'LOT': r'LOT:(.+)',
            'WAFER': r'WAFER:(.+)',
            'ROWCT': r'ROWCT:(\d+)',
            'COLCT': r'COLCT:(\d+)',
            'REFPX': r'REFPX:(.+)',
            'REFPY': r'REFPY:(.+)',
            'FNLOC': r'FNLOC:(\d+)',
            'BCEQU': r'BCEQU:(.+)'
        }
        
        for key, pattern in header_patterns.items():
            match = re.search(pattern, content)
            if match:
                self.header_info[key] = match.group(1).strip()
    
    def load_tsk_template(self, file_path: str) -> bool:
        """Load TSK template file using TSK processor"""
        try:
            print(f"📖 Loading TSK template: {file_path}")

            # Read the file
            success = self.tsk_processor.read_file(file_path)
            if success:
                # Parse the header to get dimensions and positions
                header_success = self.tsk_processor.parse_file_header()
                if header_success:
                    print(f"✅ Loaded TSK template: {len(self.tsk_processor.filearray)} bytes")
                    print(f"   TSK dimensions: {self.tsk_processor.rowsize} × {self.tsk_processor.columnsize}")
                    print(f"   TestResultStartPos: {self.tsk_processor.TestResultStartPos}")
                    print(f"   TestResultCategory: {self.tsk_processor.TestResultCategory}")
                    return True
                else:
                    print("❌ Failed to parse TSK header")
                    return False
            else:
                print("❌ Failed to read TSK template file")
                return False

        except Exception as e:
            print(f"❌ Error loading TSK template: {e}")
            return False
    
    def apply_nepes_conversion_rules(self) -> bool:
        """Apply NEPES conversion rules to TSK format using correct positioning"""
        if not self.bump_data or not self.tsk_processor.filearray:
            print("❌ Error: Bump data or TSK template not loaded")
            return False
        
        try:
            print("🔄 Applying NEPES conversion rules to TSK format...")
            
            # Reset statistics
            self.processing_stats = {
                'total_positions': 0,
                'converted_to_63': 0,
                'converted_to_59': 0,
                'category_modifications': 0,
                'errors': 0
            }
            
            # Apply conversion rules using TSK format positioning
            for row in range(min(self.rows, len(self.bump_data))):
                for col in range(min(self.cols, len(self.bump_data[row]))):
                    bump_value = self.bump_data[row][col]
                    
                    # Calculate die index (0-based)
                    die_index = row * self.cols + col
                    
                    # Calculate category position using TSK format
                    # TestResultCategory = TestResultStartPos + 1 + rowsize * columnsize * 6 + 172
                    category_start_pos = self.tsk_processor.TestResultCategory + 4 * die_index
                    
                    # Ensure we don't exceed file boundaries
                    if category_start_pos + 3 < len(self.tsk_processor.filearray):
                        self.processing_stats['total_positions'] += 1
                        
                        # Apply NEPES conversion rules:
                        # "00" -> 63 (0x3F) - valid bin positions
                        # everything else ("__", "0C", etc.) -> 59 (0x3B) - invalid/empty positions
                        if bump_value == "00":
                            target_value = 63  # 0x3F
                            self.processing_stats['converted_to_63'] += 1
                        else:
                            target_value = 59  # 0x3B
                            self.processing_stats['converted_to_59'] += 1
                        
                        # Convert target value to 4-byte binary representation
                        # TSK format uses 4 bytes for category data
                        category_bytes = self._value_to_category_bytes(target_value)
                        
                        # Write the category bytes to the correct positions
                        for i, byte_val in enumerate(category_bytes):
                            if category_start_pos + i < len(self.tsk_processor.filearray):
                                self.tsk_processor.filearray[category_start_pos + i] = byte_val
                                self.processing_stats['category_modifications'] += 1
                    else:
                        self.processing_stats['errors'] += 1
            
            print(f"✅ NEPES conversion completed:")
            print(f"   Total positions processed: {self.processing_stats['total_positions']}")
            print(f"   Converted to 63 ('00' positions): {self.processing_stats['converted_to_63']}")
            print(f"   Converted to 59 (other positions): {self.processing_stats['converted_to_59']}")
            print(f"   Category bytes modified: {self.processing_stats['category_modifications']}")
            print(f"   Errors: {self.processing_stats['errors']}")
            
            return True
            
        except Exception as e:
            print(f"❌ Error applying NEPES conversion rules: {e}")
            return False
    
    def _value_to_category_bytes(self, value: int) -> List[int]:
        """Convert integer value to 4-byte category representation for TSK format"""
        # TSK format stores category as 4 bytes
        # We need to determine the correct byte representation
        # Based on TSK processor analysis, category is stored as binary data
        
        # For now, store the value in the first byte and pad with zeros
        # This may need adjustment based on actual TSK format requirements
        return [value, 0, 0, 0]
    
    def save_output_tsk(self, output_path: str) -> bool:
        """Save the converted TSK map"""
        try:
            print(f"💾 Saving converted TSK map: {output_path}")
            
            with open(output_path, 'wb') as f:
                f.write(self.tsk_processor.filearray)
            
            file_size = os.path.getsize(output_path)
            print(f"✅ Saved converted TSK map: {file_size} bytes")
            return True
            
        except Exception as e:
            print(f"❌ Error saving TSK map: {e}")
            return False
    
    def process_nepes_to_tsk(self, bump_map_path: str, tsk_template_path: str, output_path: str) -> bool:
        """Complete NEPES to TSK processing pipeline"""
        print("🚀 Starting NEPES to TSK conversion...")
        print("=" * 60)
        
        try:
            # Step 1: Parse NEPES bump map
            print("📋 Step 1: Parsing NEPES bump map...")
            if not self.parse_bump_map(bump_map_path):
                return False
            
            # Step 2: Load TSK template
            print("\n📋 Step 2: Loading TSK template...")
            if not self.load_tsk_template(tsk_template_path):
                return False
            
            # Step 3: Apply NEPES conversion rules
            print("\n📋 Step 3: Applying NEPES conversion rules...")
            if not self.apply_nepes_conversion_rules():
                return False
            
            # Step 4: Save output TSK
            print("\n📋 Step 4: Saving output TSK...")
            if not self.save_output_tsk(output_path):
                return False
            
            print("\n" + "=" * 60)
            print("🎉 NEPES to TSK conversion completed successfully!")
            print(f"📊 Processing Summary:")
            print(f"   Input bump map: {bump_map_path}")
            print(f"   TSK template: {tsk_template_path}")
            print(f"   Output TSK: {output_path}")
            print(f"   Bump dimensions: {self.rows} × {self.cols}")
            print(f"   TSK dimensions: {self.tsk_processor.rowsize} × {self.tsk_processor.columnsize}")
            print(f"   Valid bins (63): {self.processing_stats['converted_to_63']}")
            print(f"   Invalid/Empty (59): {self.processing_stats['converted_to_59']}")
            print(f"   Category modifications: {self.processing_stats['category_modifications']}")
            
            return True
            
        except Exception as e:
            print(f"❌ NEPES to TSK conversion failed: {e}")
            return False
    
    def get_processing_stats(self) -> Dict[str, Any]:
        """Get detailed processing statistics"""
        return {
            'test_house': 'NEPES Corporation',
            'bump_dimensions': f"{self.rows} × {self.cols}",
            'tsk_dimensions': f"{self.tsk_processor.rowsize} × {self.tsk_processor.columnsize}",
            'header_info': self.header_info,
            'processing_stats': self.processing_stats,
            'tsk_info': {
                'TestResultStartPos': self.tsk_processor.TestResultStartPos,
                'TestResultCategory': self.tsk_processor.TestResultCategory,
                'file_size': len(self.tsk_processor.filearray) if self.tsk_processor.filearray else 0
            }
        }


def main():
    """Test function for NEPES TSK processor"""
    print("🧪 NEPES TSK Processor - Test")
    print("=" * 50)
    
    processor = NEPESTSKProcessor()
    
    # Test with provided files
    bump_file = "test/D97127.09"
    tsk_template = "test/009.NNS157-09-E4"
    output_file = "test/nepes_tsk_output.tsk"
    
    if os.path.exists(bump_file) and os.path.exists(tsk_template):
        success = processor.process_nepes_to_tsk(bump_file, tsk_template, output_file)
        
        if success:
            stats = processor.get_processing_stats()
            print(f"\n📊 Final Statistics:")
            for key, value in stats.items():
                print(f"   {key}: {value}")
        
        return success
    else:
        print(f"❌ Test files not found!")
        print(f"   Bump file: {bump_file} - {'✅' if os.path.exists(bump_file) else '❌'}")
        print(f"   TSK template: {tsk_template} - {'✅' if os.path.exists(tsk_template) else '❌'}")
        return False


if __name__ == "__main__":
    main()
