#!/usr/bin/env python3
"""
测试Map Version 4的32bit修改逻辑
验证所有Map Version都使用相同的32bit修改方式

具体验证:
- x=79,y=0 ("00"): 位置15365-15368 → 00000000000000000000000000111111 (63)
- x=149,y=2 ("XX"): 位置18085-18088 → 00000000000000000000000000111011 (59)

Author: Yuribytes
Company: Chipone TE development Team
"""

import os
import sys
import struct

# 添加父目录到路径
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, parent_dir)

from nepes_enhanced_processor import NEPESEnhancedProcessor
from tsk_map_processor import TSKMapProcessor

def verify_32bit_patterns():
    """验证32bit修改模式"""
    print("🔍 验证32bit修改模式")
    print("=" * 40)
    
    # 验证Pass模式 (63)
    pass_binary = 0b00000000000000000000000000111111
    pass_bytes = struct.pack('<I', pass_binary)
    print(f"Pass模式 (63):")
    print(f"   二进制: {bin(pass_binary)}")
    print(f"   十进制: {pass_binary}")
    print(f"   字节序列: {pass_bytes.hex()} ({[hex(b) for b in pass_bytes]})")
    
    # 验证Fail模式 (59)
    fail_binary = 0b00000000000000000000000000111011
    fail_bytes = struct.pack('<I', fail_binary)
    print(f"\nFail模式 (59):")
    print(f"   二进制: {bin(fail_binary)}")
    print(f"   十进制: {fail_binary}")
    print(f"   字节序列: {fail_bytes.hex()} ({[hex(b) for b in fail_bytes]})")
    
    return pass_bytes, fail_bytes

def analyze_specific_positions():
    """分析具体位置的修改"""
    print(f"\n🎯 分析具体位置修改")
    print("=" * 40)
    
    bump_file = "D97127.09"
    dummy_file = "009.NNS157-09-E4"
    output_file = "map_version4_32bit_test.tsk"
    
    # 读取原始数据
    with open(dummy_file, 'rb') as f:
        original_data = f.read()
    
    # 定义测试位置
    test_positions = [
        {"x": 0, "y": 0, "expected_bump": "__", "expected_pos": 15049, "description": "x=0,y=0 (__) - 应保持不变"},
        {"x": 79, "y": 0, "expected_bump": "00", "expected_pos": 15365, "description": "x=79,y=0 (00) - 应修改为63"},
        {"x": 149, "y": 2, "expected_bump": "XX", "expected_pos": 18085, "description": "x=149,y=2 (XX) - 应修改为59"}
    ]
    
    # 验证位置计算
    columnsize = 305
    TestResultCategory = 15049
    
    print("📊 位置计算验证:")
    for pos_info in test_positions:
        x, y = pos_info["x"], pos_info["y"]
        die_index = y * columnsize + x
        calculated_pos = TestResultCategory + die_index * 4
        expected_pos = pos_info["expected_pos"]
        
        print(f"\n   {pos_info['description']}:")
        print(f"     计算位置: {calculated_pos}-{calculated_pos+3}")
        print(f"     预期位置: {expected_pos}-{expected_pos+3}")
        print(f"     位置匹配: {'✅' if calculated_pos == expected_pos else '❌'}")
        
        # 显示原始数据
        original_bytes = original_data[calculated_pos:calculated_pos+4]
        print(f"     原始数据: {original_bytes.hex()}")
    
    # 执行处理
    print(f"\n🚀 执行NEPES Enhanced处理...")
    processor = NEPESEnhancedProcessor()
    success = processor.process_nepes_enhanced(bump_file, dummy_file, output_file)
    
    if not success:
        print("❌ 处理失败")
        return False
    
    # 读取处理后数据
    with open(output_file, 'rb') as f:
        processed_data = f.read()
    
    # 获取预期的字节模式
    pass_bytes, fail_bytes = verify_32bit_patterns()
    
    print(f"\n📊 处理后数据验证:")
    all_correct = True
    
    for pos_info in test_positions:
        x, y = pos_info["x"], pos_info["y"]
        die_index = y * columnsize + x
        calculated_pos = TestResultCategory + die_index * 4
        
        original_bytes = original_data[calculated_pos:calculated_pos+4]
        processed_bytes = processed_data[calculated_pos:calculated_pos+4]
        
        print(f"\n   {pos_info['description']}:")
        print(f"     原始: {original_bytes.hex()}")
        print(f"     处理后: {processed_bytes.hex()}")
        
        # 验证修改结果
        if pos_info["expected_bump"] == "__":
            # "__" 应该保持不变
            if original_bytes == processed_bytes:
                print(f"     ✅ 正确: 保持不变")
            else:
                print(f"     ❌ 错误: 不应该修改")
                all_correct = False
                
        elif pos_info["expected_bump"] == "00":
            # "00" 应该修改为Pass (63)
            if processed_bytes == pass_bytes:
                print(f"     ✅ 正确: 修改为Pass (63) - {pass_bytes.hex()}")
            else:
                print(f"     ❌ 错误: 应该是 {pass_bytes.hex()}")
                all_correct = False
                
        else:  # "XX"
            # "XX" 应该修改为Fail (59)
            if processed_bytes == fail_bytes:
                print(f"     ✅ 正确: 修改为Fail (59) - {fail_bytes.hex()}")
            else:
                print(f"     ❌ 错误: 应该是 {fail_bytes.hex()}")
                all_correct = False
    
    return all_correct

def verify_processing_stats():
    """验证处理统计"""
    print(f"\n📊 验证处理统计")
    print("=" * 40)
    
    bump_file = "D97127.09"
    dummy_file = "009.NNS157-09-E4"
    output_file = "map_version4_32bit_stats.tsk"
    
    processor = NEPESEnhancedProcessor()
    success = processor.process_nepes_enhanced(bump_file, dummy_file, output_file)
    
    if success:
        stats = processor.get_processing_stats()
        
        print(f"✅ 处理统计:")
        print(f"   Map Version: {stats.get('map_version', 'Unknown')}")
        print(f"   '__'位置: {stats['processing_stats']['unchanged_positions']}")
        print(f"   '00'位置: {stats['processing_stats']['pass_positions']}")
        print(f"   'XX'位置: {stats['processing_stats']['fail_positions']}")
        print(f"   二进制修改: {stats['processing_stats']['binary_modifications']}")
        print(f"   错误数: {stats['processing_stats']['errors']}")
        
        # 验证32bit修改的字节数
        expected_modifications = (stats['processing_stats']['pass_positions'] + 
                                stats['processing_stats']['fail_positions']) * 4
        actual_modifications = stats['processing_stats']['binary_modifications']
        
        print(f"\n🎯 32bit修改验证:")
        print(f"   预期修改字节: {expected_modifications} (每位置4字节)")
        print(f"   实际修改字节: {actual_modifications}")
        
        if expected_modifications == actual_modifications:
            print(f"   ✅ 32bit修改正确")
            return True
        else:
            print(f"   ❌ 修改字节数不匹配")
            return False
    else:
        print("❌ 处理失败")
        return False

def main():
    """主测试函数"""
    print("🧪 Map Version 4 - 32bit修改逻辑测试")
    print("=" * 60)
    
    # 切换到测试目录
    os.chdir(os.path.dirname(os.path.abspath(__file__)))
    
    try:
        # 测试1: 验证32bit模式
        print("📋 测试1: 验证32bit修改模式")
        verify_32bit_patterns()
        
        # 测试2: 分析具体位置
        print("📋 测试2: 分析具体位置修改")
        result2 = analyze_specific_positions()
        
        # 测试3: 验证处理统计
        print("📋 测试3: 验证处理统计")
        result3 = verify_processing_stats()
        
        print("\n" + "=" * 60)
        print("🎉 测试结果总结:")
        print(f"   具体位置修改: {'✅ 通过' if result2 else '❌ 失败'}")
        print(f"   处理统计验证: {'✅ 通过' if result3 else '❌ 失败'}")
        
        if result2 and result3:
            print(f"\n🎯 Map Version 4 - 32bit修改验证成功!")
            print(f"✅ x=79,y=0 (00): 位置15365-15368 → 3f000000 (63)")
            print(f"✅ x=149,y=2 (XX): 位置18085-18088 → 3b000000 (59)")
            print(f"✅ '__'位置保持不变")
            print(f"✅ 所有Map Version统一使用32bit修改")
            return True
        else:
            print(f"\n❌ Map Version 4修改逻辑需要进一步检查")
            return False
            
    except Exception as e:
        print(f"❌ 测试过程出错: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
