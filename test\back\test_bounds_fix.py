#!/usr/bin/env python3
"""
Test bounds fix for NEPES Enhanced Processor
Verify that all 500 "__" positions are correctly processed
"""

import os
import sys
sys.path.append('.')

from nepes_enhanced_processor import NEPESEnhancedProcessor

def test_bounds_fix():
    """Test the bounds fix"""
    print("🧪 Testing Bounds Fix for NEPES Enhanced Processor")
    print("=" * 60)
    
    # Change to test directory
    os.chdir('test')
    
    # Test files
    bump_file = "D97127.09"
    dummy_file = "009.NNS157-09-E4"
    output_file = "bounds_fix_test_output.tsk"
    
    # Initialize processor
    processor = NEPESEnhancedProcessor()
    
    print("🚀 Processing with fixed bounds checking...")
    success = processor.process_nepes_enhanced(bump_file, dummy_file, output_file)
    
    if success:
        stats = processor.get_processing_stats()
        
        print(f"\n📊 Processing Results:")
        print(f"   Total positions processed: {stats['processing_stats']['total_positions']}")
        print(f"   Unchanged positions (__): {stats['processing_stats']['unchanged_positions']}")
        print(f"   Pass positions (00): {stats['processing_stats']['pass_positions']}")
        print(f"   Fail positions (XX): {stats['processing_stats']['fail_positions']}")
        print(f"   Binary modifications: {stats['processing_stats']['binary_modifications']}")
        print(f"   Errors: {stats['processing_stats']['errors']}")
        
        # Check if we got all 500 "__" positions
        expected_underscore = 500
        actual_underscore = stats['processing_stats']['unchanged_positions']
        
        print(f"\n🔍 '__' Position Verification:")
        print(f"   Expected '__' positions: {expected_underscore}")
        print(f"   Actual '__' positions: {actual_underscore}")
        
        if actual_underscore == expected_underscore:
            print(f"   ✅ SUCCESS: All '__' positions processed correctly!")
        else:
            print(f"   ❌ ERROR: Missing {expected_underscore - actual_underscore} '__' positions")
        
        # Check for errors
        if stats['processing_stats']['errors'] == 0:
            print(f"   ✅ SUCCESS: No bounds errors!")
        else:
            print(f"   ❌ ERROR: {stats['processing_stats']['errors']} bounds errors")
        
        # Verify total positions
        expected_total = 2440  # 8 × 305
        actual_total = stats['processing_stats']['total_positions']
        
        print(f"\n📏 Total Position Verification:")
        print(f"   Expected total positions: {expected_total}")
        print(f"   Actual total positions: {actual_total}")
        
        if actual_total == expected_total:
            print(f"   ✅ SUCCESS: All positions processed!")
        else:
            print(f"   ❌ ERROR: Missing {expected_total - actual_total} positions")
        
        return True
    else:
        print("❌ Processing failed")
        return False

def main():
    """Main test function"""
    try:
        success = test_bounds_fix()
        
        if success:
            print(f"\n🎉 Bounds Fix Test COMPLETED!")
            print(f"✅ All 500 '__' positions should now be correctly processed")
            print(f"✅ No bounds errors should occur")
        else:
            print(f"\n❌ Bounds Fix Test FAILED!")
        
        return success
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
