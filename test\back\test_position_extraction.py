#!/usr/bin/env python3
"""
Test Position Extraction - Verify x,y coordinate extraction matches VB code exactly
"""

import sys
import os
sys.path.append('..')
from tsk_map_processor import TSKMapProcessor


def test_vb_mid_function():
    """
    Test VB Mid function conversion to Python slicing
    """
    print("Testing VB Mid Function Conversion")
    print("=" * 50)
    
    # Create test binary string (48 bits = 6 bytes worth)
    test_binary = "000000010000001000000011000001000000010100000110"
    print(f"Test binary string (48 bits): {test_binary}")
    print(f"Length: {len(test_binary)} bits")
    
    # VB Mid function tests
    vb_tests = [
        # (VB_start, VB_length, description, expected_python_slice)
        (1, 2, "IsPassDie (bits 1-2)", slice(0, 2)),
        (8, 9, "X coordinate (bits 8-16)", slice(7, 16)),
        (17, 2, "IsTestDie (bits 17-18)", slice(16, 18)),
        (24, 9, "Y coordinate (bits 24-32)", slice(23, 32)),
        (43, 6, "Category v0 (bits 43-48)", slice(42, 48)),
    ]
    
    print("\nVB Mid() to Python slice conversion:")
    for vb_start, vb_length, desc, py_slice in vb_tests:
        vb_end = vb_start + vb_length - 1
        py_start = py_slice.start
        py_stop = py_slice.stop
        
        if py_stop <= len(test_binary):
            extracted = test_binary[py_slice]
            print(f"  {desc}:")
            print(f"    VB: Mid(string, {vb_start}, {vb_length}) -> bits {vb_start}-{vb_end}")
            print(f"    Python: [{py_start}:{py_stop}] -> '{extracted}'")
        else:
            print(f"  {desc}: OUT OF RANGE (need {py_stop} bits, have {len(test_binary)})")


def test_coordinate_extraction_with_real_data(filepath):
    """
    Test coordinate extraction with real TSK file data
    """
    print(f"\nTesting Coordinate Extraction with Real Data: {filepath}")
    print("=" * 60)
    
    if not os.path.exists(filepath):
        print(f"❌ File not found: {filepath}")
        return False
    
    processor = TSKMapProcessor()
    
    # Read and parse file
    if not processor.read_file(filepath):
        print("❌ Failed to read file")
        return False
    
    if not processor.parse_file_header():
        print("❌ Failed to parse header")
        return False
    
    print(f"✅ File parsed: {processor.columnsize}x{processor.rowsize}")
    print(f"Version: {processor.May_version}")
    print(f"TestResultStartPos: {processor.TestResultStartPos}")
    
    # Test first few dies with detailed output
    print(f"\nTesting first 5 dies with detailed binary analysis:")
    
    for die_idx in range(min(5, processor.rowsize * processor.columnsize)):
        i = (die_idx // processor.columnsize) + 1  # VB 1-based row
        j = (die_idx % processor.columnsize) + 1   # VB 1-based col
        
        # Calculate die result position (matching VB code exactly)
        # VB: TestResultStartPos + 6 * (((i - 1) * columnsize) + j - 1) + 1
        result_start = processor.TestResultStartPos + 6 * die_idx + 1
        result_end = result_start + 5  # 6 bytes total
        
        print(f"\nDie ({i},{j}) - Index {die_idx}:")
        print(f"  Binary data position: {result_start} to {result_end + 1}")
        
        if result_end < len(processor.filearray):
            # Get binary data
            onedieresult = processor.get_binary(processor.filearray, result_start, result_end + 1)
            print(f"  Binary length: {len(onedieresult)} bits")
            
            if len(onedieresult) >= 48:  # Need at least 48 bits for full analysis
                print(f"  Full binary: {onedieresult}")
                
                # Extract each field with detailed output
                print(f"  Field extraction:")
                
                # IsPassDie (bits 1-2)
                pass_bits = onedieresult[0:2]
                pass_val = processor.binary2val(pass_bits)
                print(f"    IsPassDie [0:2]: '{pass_bits}' = {pass_val}")
                
                # X coordinate (bits 8-16) 
                x_bits = onedieresult[7:16]
                x_val = processor.binary2val(x_bits)
                print(f"    X coord [7:16]: '{x_bits}' = {x_val}")
                
                # IsTestDie (bits 17-18)
                test_bits = onedieresult[16:18]
                test_val = processor.binary2val(test_bits)
                print(f"    IsTestDie [16:18]: '{test_bits}' = {test_val}")
                
                # Y coordinate (bits 24-32)
                if len(onedieresult) >= 32:
                    y_bits = onedieresult[23:32]
                    y_val = processor.binary2val(y_bits)
                    print(f"    Y coord [23:32]: '{y_bits}' = {y_val}")
                else:
                    print(f"    Y coord: INSUFFICIENT DATA (need 32 bits, have {len(onedieresult)})")
                
                # Category (version dependent)
                if processor.May_version == 0 and len(onedieresult) >= 48:
                    cat_bits = onedieresult[42:48]
                    cat_val = processor.binary2val(cat_bits)
                    print(f"    Category [42:48]: '{cat_bits}' = {cat_val}")
                
                # Use the actual extraction function
                x, y, category, is_test_die, is_pass_die = processor.get_data_from_die_result_update(
                    processor.May_version, onedieresult, "")
                
                print(f"  Final extracted values:")
                print(f"    x={x}, y={y}, category={category}")
                print(f"    is_test_die={is_test_die}, is_pass_die={is_pass_die}")
                
                # Validate coordinates are reasonable
                if 0 <= x <= 10000 and 0 <= y <= 10000:
                    print(f"    ✅ Coordinates seem reasonable")
                else:
                    print(f"    ⚠️  Coordinates seem unusual (x={x}, y={y})")
            else:
                print(f"  ❌ Insufficient binary data ({len(onedieresult)} bits, need 48)")
        else:
            print(f"  ❌ Data position exceeds file size")


def test_binary_string_analysis():
    """
    Analyze binary string structure for debugging
    """
    print(f"\nBinary String Structure Analysis")
    print("=" * 50)
    
    # Create a test processor
    processor = TSKMapProcessor()
    
    # Test with known values
    test_cases = [
        # (description, binary_string, expected_x, expected_y)
        ("Simple test", "0" * 48, 0, 0),
        ("X=1, Y=1", "00000000000000010000000000000000000000010000000000000000", 1, 1),
        ("X=6, Y=1", "00000000000001100000000000000000000000010000000000000000", 6, 1),
    ]
    
    for desc, binary_str, exp_x, exp_y in test_cases:
        print(f"\nTest case: {desc}")
        print(f"Binary: {binary_str}")
        print(f"Length: {len(binary_str)} bits")
        
        if len(binary_str) >= 32:
            # Extract X (bits 8-16)
            x_bits = binary_str[7:16]
            x_val = processor.binary2val(x_bits)
            
            # Extract Y (bits 24-32)
            y_bits = binary_str[23:32]
            y_val = processor.binary2val(y_bits)
            
            print(f"X extraction [7:16]: '{x_bits}' = {x_val} (expected: {exp_x})")
            print(f"Y extraction [23:32]: '{y_bits}' = {y_val} (expected: {exp_y})")
            
            if x_val == exp_x and y_val == exp_y:
                print("✅ Extraction correct")
            else:
                print("❌ Extraction incorrect")


def main():
    """
    Main test function
    """
    if len(sys.argv) < 2:
        print("Position Extraction Test")
        print("Usage: python test_position_extraction.py <tsk_file_path>")
        print("Example: python test_position_extraction.py ../3AA111-01-B4")
        return
    
    filepath = sys.argv[1]
    
    print("TSK/MAP Position Extraction Test")
    print("Verifying x,y coordinate extraction matches VB code")
    print("=" * 70)
    
    # Test 1: VB Mid function conversion
    test_vb_mid_function()
    
    # Test 2: Binary string analysis
    test_binary_string_analysis()
    
    # Test 3: Real data extraction
    test_coordinate_extraction_with_real_data(filepath)
    
    print("\n" + "=" * 70)
    print("Position Extraction Test Completed!")


if __name__ == "__main__":
    main()
