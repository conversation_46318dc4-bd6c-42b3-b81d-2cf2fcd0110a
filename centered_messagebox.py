"""
Centered MessageBox Module for Full Map Tool
Provides centered messagebox dialogs specifically for Full Map Tool
Maintains original detailed content while adding centering functionality
Author: Yuribytes
Company: Chipone TE development Team
"""

import tkinter as tk
from tkinter import ttk
from typing import Optional, Callable


class CenteredMessageBox:
    """Custom messagebox that centers on parent window"""
    
    @staticmethod
    def _center_window(window: tk.Toplevel, parent: tk.Tk, width: int = 450, height: int = 200):
        """Center window on parent"""
        try:
            # Update parent to get accurate geometry
            parent.update_idletasks()
            
            # Get parent window position and size
            parent_x = parent.winfo_x()
            parent_y = parent.winfo_y()
            parent_width = parent.winfo_width()
            parent_height = parent.winfo_height()
            
            # Calculate center position
            x = parent_x + (parent_width // 2) - (width // 2)
            y = parent_y + (parent_height // 2) - (height // 2)
            
            # Ensure window stays on screen
            x = max(0, x)
            y = max(0, y)
            
            window.geometry(f"{width}x{height}+{x}+{y}")
            
        except Exception as e:
            print(f"Warning: Could not center window: {e}")
            window.geometry(f"{width}x{height}")
    
    @staticmethod
    def _calculate_dialog_size(message: str, min_width: int = 450, min_height: int = 200) -> tuple:
        """Calculate optimal dialog size based on message content"""
        lines = message.split('\n')
        line_count = len(lines)
        max_line_length = max(len(line) for line in lines) if lines else 0

        # Calculate width based on longest line (approximately 8 pixels per character)
        calculated_width = max(min_width, min(800, max_line_length * 8 + 100))

        # Calculate height based on line count (approximately 20 pixels per line + padding)
        calculated_height = max(min_height, min(600, line_count * 20 + 150))

        return int(calculated_width), int(calculated_height)

    @staticmethod
    def _create_dialog(parent: tk.Tk, title: str, message: str, dialog_type: str = "info",
                      width: int = None, height: int = None, buttons: list = None) -> Optional[str]:
        """Create a centered dialog window with dynamic sizing"""
        try:
            # Calculate optimal size if not provided
            if width is None or height is None:
                calc_width, calc_height = CenteredMessageBox._calculate_dialog_size(message)
                width = width or calc_width
                height = height or calc_height

            # Create dialog window
            dialog = tk.Toplevel(parent)
            dialog.title(title)
            dialog.resizable(True, True)  # Allow resizing for better user experience
            dialog.transient(parent)
            dialog.grab_set()

            # Center the dialog
            CenteredMessageBox._center_window(dialog, parent, width, height)
            
            # Configure dialog
            dialog.configure(bg='white')
            
            # Result variable
            result = tk.StringVar()
            result.set("")
            
            # Main frame
            main_frame = ttk.Frame(dialog, padding="20")
            main_frame.pack(fill=tk.BOTH, expand=True)
            
            # Icon and message frame
            content_frame = ttk.Frame(main_frame)
            content_frame.pack(fill=tk.BOTH, expand=True)
            
            # Icon based on dialog type
            icon_text = {
                'info': 'ℹ️',
                'warning': '⚠️',
                'error': '❌',
                'question': '❓',
                'success': '✅'
            }.get(dialog_type, 'ℹ️')
            
            # Icon label
            icon_label = ttk.Label(content_frame, text=icon_text, font=("Arial", 16))
            icon_label.pack(pady=(0, 10))
            
            # Message label with dynamic wraplength and better font
            # Use a scrollable text widget for very long messages
            if len(message) > 500 or message.count('\n') > 15:
                # Create scrollable text widget for long messages
                text_frame = ttk.Frame(content_frame)
                text_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 20))

                text_widget = tk.Text(text_frame, wrap=tk.WORD, font=("Arial", 9),
                                    height=min(20, message.count('\n') + 5),
                                    width=max(60, min(100, max(len(line) for line in message.split('\n')))),
                                    bg='white', relief=tk.FLAT, state=tk.DISABLED)

                scrollbar = ttk.Scrollbar(text_frame, orient=tk.VERTICAL, command=text_widget.yview)
                text_widget.configure(yscrollcommand=scrollbar.set)

                text_widget.config(state=tk.NORMAL)
                text_widget.insert(tk.END, message)
                text_widget.config(state=tk.DISABLED)

                text_widget.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
                scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
            else:
                # Use label for shorter messages with dynamic wraplength
                wrap_length = max(400, min(width - 100, 600))
                message_label = ttk.Label(content_frame, text=message, justify=tk.LEFT,
                                        font=("Arial", 9), wraplength=wrap_length)
                message_label.pack(pady=(0, 20), padx=10)
            
            # Button frame
            button_frame = ttk.Frame(main_frame)
            button_frame.pack(fill=tk.X)
            
            # Default buttons if none provided
            if buttons is None:
                if dialog_type == 'question':
                    buttons = [('Yes', 'yes'), ('No', 'no')]
                else:
                    buttons = [('OK', 'ok')]
            
            # Create buttons
            def on_button_click(value):
                result.set(value)
                dialog.destroy()
            
            for i, (text, value) in enumerate(buttons):
                btn = ttk.Button(button_frame, text=text, 
                               command=lambda v=value: on_button_click(v))
                btn.pack(side=tk.LEFT, padx=(5, 0) if i > 0 else 0)
                
                # Make first button default
                if i == 0:
                    btn.focus_set()
                    dialog.bind('<Return>', lambda e, v=value: on_button_click(v))
            
            # Center buttons
            for child in button_frame.winfo_children():
                child.pack_configure(expand=True)
            
            # Handle window close
            dialog.protocol("WM_DELETE_WINDOW", lambda: on_button_click(''))
            
            # Handle Escape key
            dialog.bind('<Escape>', lambda e: on_button_click(''))
            
            # Wait for dialog to close
            dialog.wait_window()
            
            return result.get()
            
        except Exception as e:
            print(f"Error creating centered dialog: {e}")
            return None
    
    @staticmethod
    def showinfo(title: str, message: str, parent: tk.Tk = None) -> None:
        """Show centered info dialog with dynamic sizing for detailed content"""
        if parent is None:
            parent = tk._default_root
        # Use dynamic sizing based on message content
        CenteredMessageBox._create_dialog(parent, title, message, "info")

    @staticmethod
    def showwarning(title: str, message: str, parent: tk.Tk = None) -> None:
        """Show centered warning dialog with dynamic sizing for detailed content"""
        if parent is None:
            parent = tk._default_root
        # Use dynamic sizing based on message content
        CenteredMessageBox._create_dialog(parent, title, message, "warning")

    @staticmethod
    def showerror(title: str, message: str, parent: tk.Tk = None) -> None:
        """Show centered error dialog with dynamic sizing for detailed content"""
        if parent is None:
            parent = tk._default_root
        # Use dynamic sizing based on message content
        CenteredMessageBox._create_dialog(parent, title, message, "error")
    
    @staticmethod
    def showsuccess(title: str, message: str, parent: tk.Tk = None) -> None:
        """Show centered success dialog"""
        if parent is None:
            parent = tk._default_root
        CenteredMessageBox._create_dialog(parent, title, message, "success")
    
    @staticmethod
    def askyesno(title: str, message: str, parent: tk.Tk = None) -> bool:
        """Show centered yes/no dialog with dynamic sizing for detailed content"""
        if parent is None:
            parent = tk._default_root
        result = CenteredMessageBox._create_dialog(
            parent, title, message, "question",
            buttons=[('Yes', 'yes'), ('No', 'no')]
        )
        return result == 'yes'
    
    @staticmethod
    def askokcancel(title: str, message: str, parent: tk.Tk = None) -> bool:
        """Show centered OK/Cancel dialog"""
        if parent is None:
            parent = tk._default_root
        result = CenteredMessageBox._create_dialog(
            parent, title, message, "question",
            buttons=[('OK', 'ok'), ('Cancel', 'cancel')]
        )
        return result == 'ok'
    
    @staticmethod
    def askretrycancel(title: str, message: str, parent: tk.Tk = None) -> bool:
        """Show centered Retry/Cancel dialog"""
        if parent is None:
            parent = tk._default_root
        result = CenteredMessageBox._create_dialog(
            parent, title, message, "question",
            buttons=[('Retry', 'retry'), ('Cancel', 'cancel')]
        )
        return result == 'retry'


# Convenience functions that match tkinter.messagebox API
def showinfo(title: str, message: str, parent: tk.Tk = None) -> None:
    """Show centered info dialog"""
    CenteredMessageBox.showinfo(title, message, parent)

def showwarning(title: str, message: str, parent: tk.Tk = None) -> None:
    """Show centered warning dialog"""
    CenteredMessageBox.showwarning(title, message, parent)

def showerror(title: str, message: str, parent: tk.Tk = None) -> None:
    """Show centered error dialog"""
    CenteredMessageBox.showerror(title, message, parent)

def showsuccess(title: str, message: str, parent: tk.Tk = None) -> None:
    """Show centered success dialog"""
    CenteredMessageBox.showsuccess(title, message, parent)

def askyesno(title: str, message: str, parent: tk.Tk = None) -> bool:
    """Show centered yes/no dialog"""
    return CenteredMessageBox.askyesno(title, message, parent)

def askokcancel(title: str, message: str, parent: tk.Tk = None) -> bool:
    """Show centered OK/Cancel dialog"""
    return CenteredMessageBox.askokcancel(title, message, parent)

def askretrycancel(title: str, message: str, parent: tk.Tk = None) -> bool:
    """Show centered Retry/Cancel dialog"""
    return CenteredMessageBox.askretrycancel(title, message, parent)


# Test function
def test_centered_messagebox():
    """Test the centered messagebox functionality"""
    root = tk.Tk()
    root.title("Centered MessageBox Test")
    root.geometry("800x600")
    root.configure(bg='lightgray')
    
    def test_info():
        showinfo("Information", "This is a centered info message!", root)
    
    def test_warning():
        showwarning("Warning", "This is a centered warning message!", root)
    
    def test_error():
        showerror("Error", "This is a centered error message!", root)
    
    def test_success():
        showsuccess("Success", "This is a centered success message!", root)
    
    def test_question():
        result = askyesno("Question", "Do you like centered dialogs?", root)
        showinfo("Result", f"You answered: {'Yes' if result else 'No'}", root)
    
    # Create test buttons
    ttk.Button(root, text="Test Info", command=test_info).pack(pady=10)
    ttk.Button(root, text="Test Warning", command=test_warning).pack(pady=10)
    ttk.Button(root, text="Test Error", command=test_error).pack(pady=10)
    ttk.Button(root, text="Test Success", command=test_success).pack(pady=10)
    ttk.Button(root, text="Test Question", command=test_question).pack(pady=10)
    
    root.mainloop()


if __name__ == "__main__":
    test_centered_messagebox()
