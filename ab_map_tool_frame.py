#!/usr/bin/env python3
"""
AB Map Tool Frame - Complete AB Map functionality with return button
Frame-based version for main application controller
Based on tsk_map_gui.py with full functionality preserved
Author: Yuribytes
Company: Chipone TE development Team
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import os
from datetime import datetime
from tsk_map_gui import ABMapGUI


class ABMapToolFrame:
    """AB Map Tool as a frame for the main application - Full functionality"""

    def __init__(self, parent, app_controller):
        self.parent = parent
        self.app_controller = app_controller
        self.main_frame = None

        # Create the main frame
        self.main_frame = ttk.Frame(self.parent, padding="15")

        # Create the AB Map GUI directly in our frame
        self.ab_map_gui = ABMapGUI(self.main_frame)

        # Override the return_to_selector method
        original_return = self.ab_map_gui.return_to_selector
        self.ab_map_gui.return_to_selector = self.return_to_selector
        print("AB Map Tool Frame - Method override and button rebinding completed")

        # Also find and rebind the Back button directly (more robust)
        self._rebind_back_button()

        # Store references to important attributes for easy access
        self.mode_selection = self.ab_map_gui.mode_selection
        self.config_file_path = self.ab_map_gui.config_file_path
        self.amap_file_path = self.ab_map_gui.amap_file_path
        self.bmap_file_path = self.ab_map_gui.bmap_file_path
        self.compare_amap_path = self.ab_map_gui.compare_amap_path
        self.compare_bmap_path = self.ab_map_gui.compare_bmap_path
        self.rotation_angle = self.ab_map_gui.rotation_angle
        self.filter_empty = self.ab_map_gui.filter_empty
        self.status_var = self.ab_map_gui.status_var

    def _rebind_back_button(self):
        """Find and rebind the Back button to ensure it uses our method"""
        try:
            # Find the Back button in the AB Map GUI
            def find_back_button(widget):
                """Recursively find the Back button"""
                if hasattr(widget, 'winfo_children'):
                    for child in widget.winfo_children():
                        if hasattr(child, 'cget'):
                            try:
                                if child.cget('text') == '← Back':
                                    child.configure(command=self.return_to_selector)
                                    return True
                            except:
                                pass
                        if find_back_button(child):
                            return True
                return False

            # Search for the Back button starting from the main frame
            find_back_button(self.ab_map_gui.root)

        except Exception as e:
            print(f"Warning: Error rebinding Back button: {e}")
    
    def return_to_selector(self):
        """Return to tool selector with automatic memory cleanup"""
        print("🔄 AB Map Tool - Returning to main menu with memory cleanup")
        try:
            # Get memory info before clearing (for accurate reporting)
            memory_before_clear = 0.0
            processor_count = 0

            if self.ab_map_gui.amap_processor:
                if hasattr(self.ab_map_gui.amap_processor, 'get_memory_usage_mb'):
                    memory_before_clear += self.ab_map_gui.amap_processor.get_memory_usage_mb()
                processor_count += 1
            if self.ab_map_gui.bmap_processor:
                if hasattr(self.ab_map_gui.bmap_processor, 'get_memory_usage_mb'):
                    memory_before_clear += self.ab_map_gui.bmap_processor.get_memory_usage_mb()
                processor_count += 1

            # Clear memory automatically
            self.ab_map_gui.clear_memory()
            print(f"AB Map Tool - Memory cleared when returning to main menu: {memory_before_clear:.1f} MB freed")

            # Verify memory was actually cleared
            memory_after_clear = 0.0
            if self.ab_map_gui.amap_processor:
                if hasattr(self.ab_map_gui.amap_processor, 'get_memory_usage_mb'):
                    memory_after_clear += self.ab_map_gui.amap_processor.get_memory_usage_mb()
            if self.ab_map_gui.bmap_processor:
                if hasattr(self.ab_map_gui.bmap_processor, 'get_memory_usage_mb'):
                    memory_after_clear += self.ab_map_gui.bmap_processor.get_memory_usage_mb()

            # Calculate actual memory freed
            actual_memory_freed = memory_before_clear - memory_after_clear
            print(f"AB Map Tool - Actual memory freed: {actual_memory_freed:.1f} MB (before: {memory_before_clear:.1f} MB, after: {memory_after_clear:.1f} MB)")

            # Show 1-second cleanup popup with actual freed memory
            self.show_return_cleanup_popup(actual_memory_freed, processor_count)

            # Return to selector after popup
            self.app_controller.root.after(1100, self.app_controller.show_tool_selector)

        except Exception as e:
            print(f"Warning: Error during AB Map return cleanup: {e}")
            # Still return to selector even if cleanup fails
            self.app_controller.show_tool_selector()

    def show_return_cleanup_popup(self, memory_freed: float, processor_count: int):
        """Show 1-second cleanup popup when returning to main menu"""
        try:
            # Create popup window
            popup = tk.Toplevel(self.app_controller.root)
            popup.title("Memory Cleanup")
            popup.geometry("350x120")
            popup.resizable(False, False)

            # Center popup on parent window
            popup.transient(self.app_controller.root)
            popup.grab_set()

            # Center popup
            x = self.app_controller.root.winfo_x() + (self.app_controller.root.winfo_width() // 2) - 175
            y = self.app_controller.root.winfo_y() + (self.app_controller.root.winfo_height() // 2) - 60
            popup.geometry(f"350x120+{x}+{y}")

            # Create content
            main_frame = ttk.Frame(popup, padding="20")
            main_frame.pack(fill=tk.BOTH, expand=True)

            # Icon and message
            if memory_freed > 0:
                message = f"🔄 Returning to Main Menu\n\n{memory_freed:.1f} MB freed from {processor_count} processors"
            elif processor_count > 0:
                message = f"🔄 Returning to Main Menu\n\nProcessors cleared (no memory to free)"
            else:
                message = "🔄 Returning to Main Menu\n\nAB Map Tool cleared"

            ttk.Label(main_frame, text=message, justify=tk.CENTER,
                     font=("Arial", 10)).pack(expand=True)

            # Auto-close after 1 second
            popup.after(1000, popup.destroy)

            # Allow manual close with Escape key
            popup.bind('<Escape>', lambda e: popup.destroy())
            popup.focus_set()

        except Exception as e:
            print(f"Warning: Error showing AB Map return cleanup popup: {e}")
    
    def has_unsaved_work(self):
        """Check if there's unsaved work"""
        return (self.ab_map_gui.amap_processor is not None or
                self.ab_map_gui.bmap_processor is not None or
                self.ab_map_gui.processing_count > 0)

    def clear_memory(self):
        """Clear memory - delegate to AB Map GUI"""
        return self.ab_map_gui.clear_memory()

    def exit_application(self):
        """Exit application with cleanup - delegate to AB Map GUI"""
        return self.ab_map_gui.exit_application()
    
    def show(self):
        """Show the AB Map Tool frame"""
        if self.main_frame:
            self.main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
    
    def hide(self):
        """Hide the AB Map Tool frame"""
        if self.main_frame:
            self.main_frame.grid_remove()


def main():
    """Test function for standalone running"""
    root = tk.Tk()
    root.title("AB Map Tool Test")
    root.geometry("900x800")
    
    # Mock app controller for testing
    class MockController:
        def __init__(self, root):
            self.root = root
        def return_to_selector_with_confirmation(self, tool_name, has_unsaved):
            print(f"Would return to selector from {tool_name}, unsaved: {has_unsaved}")
            return True
    
    controller = MockController(root)
    ab_tool = ABMapToolFrame(root, controller)
    ab_tool.show()
    
    root.mainloop()


if __name__ == "__main__":
    main()
