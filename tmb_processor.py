#!/usr/bin/env python3
"""
TMB File Processor - Generate TMB format files from MAP data
Based on worker_file17.txt requirements for Full Map Tool enhancement

TMB Format Structure:
- Upper part: Map data visualization (category positions)
- Middle part: Wafer Information (Nepes format)
- Lower part: Category statistics (Cat 00~Cat 255)

Author: Yuribytes
Company: Chipone TE development Team
"""

import os
import struct
from datetime import datetime
from typing import List, Dict, Optional, Any
from tsk_map_processor import TSKMapProcessor
from config_reader import ConfigReader


class TMBProcessor:
    """TMB file processor for Full Map Tool enhancement"""
    
    def __init__(self):
        self.tsk_processor = None
        self.config_reader = None
        self.output_folder = ""
        
    def set_config_reader(self, config_reader: ConfigReader):
        """Set configuration reader for Excel data"""
        self.config_reader = config_reader
        
    def set_output_folder(self, folder_path: str):
        """Set output folder for TMB files"""
        self.output_folder = folder_path
        
    def generate_tmb_filename(self, map_file_path: str) -> str:
        """Generate TMB filename with lotid + "_" + H5判断 + slotid format"""
        # Extract lotid from MAP file (起始地址82，18个bytes，转换为ASCII)
        lotid = self.extract_ascii_data(map_file_path, 82, 18).strip()  # 83-1=82

        # Clean lotid for filename (remove invalid characters)
        clean_lotid = "".join(c for c in lotid if c.isalnum() or c in '-_.')
        if not clean_lotid:
            clean_lotid = "UNKNOWN_LOT"

        # Extract slotid from MAP file (起始地址102，2个bytes，转换为十进制，格式化为两位数)
        slotid_decimal = self.extract_binary_to_decimal(map_file_path, 102, 2)  # 103-1=102
        slotid = f"{int(slotid_decimal):02d}"  # 格式化为两位数，如：0→01, 1→01, 19→19

        # Get H5 content from config and convert to number
        h5_number = ""
        if self.config_reader and self.config_reader.has_config_loaded():
            h5_content = self.config_reader.get_test_flow()
            # H5判断逻辑：CP1->1, CP2->2
            if h5_content == "CP1":
                h5_number = "1"
            elif h5_content == "CP2":
                h5_number = "2"
            else:
                # 如果不是CP1或CP2，使用原内容
                h5_number = h5_content

        if not h5_number:
            h5_number = "UNKNOWN"

        # Create TMB filename: lotid + "_" + H5判断 + slotid + .tmb
        tmb_filename = f"{clean_lotid}-{h5_number}-{slotid}.tmb"

        if self.output_folder:
            return os.path.join(self.output_folder, tmb_filename)
        else:
            # Use same directory as source file
            source_dir = os.path.dirname(map_file_path)
            return os.path.join(source_dir, tmb_filename)
    
    def process_map_to_tmb(self, map_file_path: str) -> Optional[str]:
        """Process single MAP file to TMB format"""
        try:
            print(f"Processing MAP to TMB: {os.path.basename(map_file_path)}")
            
            # Initialize TSK processor
            self.tsk_processor = TSKMapProcessor()
            
            # Read and parse MAP file
            if not self.tsk_processor.read_file(map_file_path):
                print(f"❌ Failed to read MAP file: {map_file_path}")
                return None
                
            if not self.tsk_processor.parse_file_header():
                print(f"❌ Failed to parse MAP header: {map_file_path}")
                return None
                
            if not self.tsk_processor.process_die_data():
                print(f"❌ Failed to process die data: {map_file_path}")
                return None
            
            # Generate TMB filename
            tmb_output_path = self.generate_tmb_filename(map_file_path)
            
            # Generate TMB content
            tmb_content = self.generate_tmb_content(map_file_path)
            
            # Write TMB file
            with open(tmb_output_path, 'w', encoding='utf-8') as f:
                f.write(tmb_content)
            
            print(f"✅ TMB file generated: {os.path.basename(tmb_output_path)}")
            return tmb_output_path
            
        except Exception as e:
            print(f"❌ Error processing MAP to TMB: {e}")
            return None
    
    def generate_tmb_content(self, map_file_path: str) -> str:
        """Generate complete TMB file content"""
        content_parts = []

        # Upper part: Map data visualization
        content_parts.append(self.generate_upper_part())
        content_parts.append("\n\n\n")  # Three empty lines (two line breaks)

        # Middle part: Wafer Information
        content_parts.append(self.generate_middle_part(map_file_path))
        content_parts.append("\n\n\n")  # Three empty lines (two line breaks)

        # Lower part: Category statistics
        content_parts.append(self.generate_lower_part())

        return "".join(content_parts)
    
    def generate_upper_part(self) -> str:
        """Generate upper part: Map data visualization matching reference format"""
        if not self.tsk_processor or not self.tsk_processor.map_data:
            return "No map data available"

        lines = []

        # Column headers (00, 01, 02, ...) - 每个列标题占3个字符位置
        col_headers = ["   "]  # 3个空格用于行标题对齐
        for i in range(self.tsk_processor.columnsize):
            if i < 10:
                col_headers.append(f" 0{i}")   # 0-9: 空格+0+数字 ( 00,  01,  02, ...)
            elif i < 100:
                col_headers.append(f" {i:02d}")  # 10-99: 空格+两位数字 ( 10,  11, ...)
            else:
                col_headers.append(f" {i:3d}")   # 100+: 空格+三位数字格式 ( 100,  101,  102, ...)
        lines.append("".join(col_headers))  # 直接连接，每个位置占4个字符（三位数+空格）

        # Separator line with --+--+--+ pattern - 根据列数调整字符数
        separator_parts = ["--+"]  # 对应行标题的分隔符(3个字符)
        for i in range(self.tsk_processor.columnsize):
            if i < 100:
                separator_parts.append("--+")  # 0-99: 3个字符的分隔符
            else:
                separator_parts.append("---+")  # 100+: 4个字符的分隔符（三位数+空格）
        lines.append("".join(separator_parts))

        # Row data with row headers and | separators
        for row in range(self.tsk_processor.rowsize):
            row_parts = [f"{row:02d}|"]  # Row header with |

            # Build row data - 根据列数调整字符宽度
            row_data = []
            for col in range(self.tsk_processor.columnsize):
                category = self.tsk_processor.map_data[row][col][0]
                color = self.tsk_processor.map_data[row][col][1]

                # 修正逻辑：只有color==0的位置才是真正的空白区域
                # bin0 (category==0) 如果有测试数据 (color!=0) 应该显示为0
                if color == 0:  # 未测试的空白区域
                    if col < 100:
                        row_data.append("   ")  # 0-99列：3个空格
                    else:
                        row_data.append("    ")  # 100+列：4个空格
                else:  # 有测试数据的区域，包括bin0
                    if col < 100:
                        # 0-99列：每个位置占3个字符
                        if category < 10:
                            row_data.append(f"  {category}")  # 1位数：两个空格+数字
                        elif category < 100:
                            row_data.append(f" {category}")   # 2位数：一个空格+数字
                        else:
                            row_data.append(f"{category}")    # 3位数：直接显示（不应该出现在<100列）
                    else:
                        # 100+列：每个位置占4个字符
                        if category < 10:
                            row_data.append(f"   {category}")  # 1位数：三个空格+数字
                        elif category < 100:
                            row_data.append(f"  {category}")   # 2位数：两个空格+数字
                        else:
                            row_data.append(f" {category}")    # 3位数：一个空格+数字

            # Join row data without separator (直接连接)
            row_parts.append("".join(row_data))
            lines.append("".join(row_parts))

        return "\n".join(lines)
    
    def generate_middle_part(self, map_file_path: str) -> str:
        """Generate middle part: Wafer Information (Nepes format)"""
        lines = []
        lines.append("============ Wafer Information (Nepes) ===========")
        
        # Device Name from config D2
        device_name = ""
        if self.config_reader and self.config_reader.has_config_loaded():
            device_name = self.config_reader.device_name
        lines.append(f"Device Name: {device_name}")
        
        # Extract binary data from MAP file (从1-byte开始计算位置)
        lot_no = self.extract_ascii_data(map_file_path, 82, 18)  # 83-1=82
        slot_no = self.extract_binary_to_decimal(map_file_path, 102, 2)  # 103-1=102, 二进制转十进制
        wafer_id = self.extract_ascii_data(map_file_path, 60, 21)  # 61-1=60

        lines.append(f"Lot No.: {lot_no}")
        lines.append(f"Slot No: {slot_no}")
        lines.append(f"Wafer Id: {wafer_id}")

        # Test program from config A2
        test_program = ""
        if self.config_reader and self.config_reader.has_config_loaded():
            test_program = self.config_reader.test_program_name
        lines.append(f"Test program: {test_program}")

        # Test NO from config D5
        test_no = ""
        if self.config_reader and self.config_reader.has_config_loaded():
            test_no = self.config_reader.tester_name
        lines.append(f"Test NO: {test_no}")

        # Probe card_id from config F5
        probe_card_id = ""
        if self.config_reader and self.config_reader.has_config_loaded():
            probe_card_id = self.config_reader.probe_card_no
        lines.append(f"Probe card_id: {probe_card_id}")

        # Operator Name from config L5
        operator_name = ""
        if self.config_reader and self.config_reader.has_config_loaded():
            operator_name = self.config_reader.get_operator_name()
        lines.append(f"Operator Name : {operator_name}")

        # Wafer Size from config J5
        wafer_size = ""
        if self.config_reader and self.config_reader.has_config_loaded():
            wafer_size = self.config_reader.get_wafer_size()
        lines.append(f"Wafer Size: {wafer_size}")

        lines.append("Flat: 180 degree")

        # Time data from MAP file (从1-byte开始计算位置)
        test_start_time = self.extract_ascii_data(map_file_path, 148, 10)  # 149-1=148 //fix .. in the time format
        test_finish_time = self.extract_ascii_data(map_file_path, 160, 10)  # 161-1=160
        wafer_load_time = self.extract_ascii_data(map_file_path, 172, 10)  # 173-1=172
        wafer_unload_time = self.extract_ascii_data(map_file_path, 184, 10)  # 185-1=184

        lines.append(f"Test Start Time: {test_start_time}")
        lines.append(f"Test Finish Time: {test_finish_time}")
        lines.append(f"Wafer Load Time: {wafer_load_time}")
        lines.append(f"Wafer Unload Time : {wafer_unload_time}")
        
        # Statistics from TSK processor
        if self.tsk_processor:
            stats = self.tsk_processor.get_test_statistics()
            fail_count = stats['total_tested'] - stats['pass_count']  # Calculate fail count
            lines.append(f"Gross Dice: {stats['total_tested']}")
            lines.append(f"Pass Die: {stats['pass_count']}")
            lines.append(f"Fail Die: {fail_count}")
            lines.append(f"Yield: {stats['yield_percentage']:.2f}%")
        
        return "\n".join(lines)
    
    def generate_lower_part(self) -> str:
        """Generate lower part: Category statistics (Cat 00~Cat 255)"""
        if not self.tsk_processor:
            return "No processor data available"

        lines = []

        # 修正逻辑：正确统计所有有效测试数据，包括bin0
        category_counts = {}
        if self.tsk_processor.map_data:
            for row in self.tsk_processor.map_data:
                for col_data in row:
                    category = col_data[0]
                    color = col_data[1]

                    # 只有color!=0的位置才是有效测试数据
                    # 包括bin0 (category==0) 如果它有测试数据 (color!=0)
                    if color != 0:  # 有测试数据的位置
                        category_counts[category] = category_counts.get(category, 0) + 1

        # Generate Cat 00~Cat 255 entries
        for cat in range(256):
            count = category_counts.get(cat, 0)
            lines.append(f"Cat {cat:02d}: {count}")

        return "\n".join(lines)
    
    def extract_ascii_data(self, file_path: str, start_byte: int, length: int) -> str:
        """Extract ASCII data from binary file at specified position"""
        try:
            with open(file_path, 'rb') as f:
                f.seek(start_byte)
                data = f.read(length)
                # Convert to ASCII, replacing non-printable characters
                ascii_str = ''.join(chr(b) if 32 <= b <= 126 else '.' for b in data)
                return ascii_str.strip()
        except Exception as e:
            print(f"Warning: Error extracting ASCII data: {e}")
            return ""

    def extract_binary_to_decimal(self, file_path: str, start_byte: int, length: int) -> str:
        """Extract binary data and convert to decimal string with correct endianness"""
        try:
            with open(file_path, 'rb') as f:
                f.seek(start_byte)
                data = f.read(length)

                # Convert binary bytes to decimal using big-endian (most significant byte first)
                if length == 1:
                    decimal_value = data[0]
                elif length == 2:
                    # For 2 bytes: treat as big-endian (0x00, 0x13) -> 0x0013 = 19
                    decimal_value = int.from_bytes(data, byteorder='big')
                elif length == 4:
                    decimal_value = int.from_bytes(data, byteorder='big')
                else:
                    # For other lengths, use big-endian
                    decimal_value = int.from_bytes(data, byteorder='big')

                return str(decimal_value)
        except Exception as e:
            print(f"Warning: Error extracting binary data: {e}")
            return "0"
    
    def process_multiple_maps_to_tmb(self, map_file_paths: List[str]) -> List[str]:
        """Process multiple MAP files to TMB format"""
        successful_outputs = []
        
        for map_file_path in map_file_paths:
            tmb_output = self.process_map_to_tmb(map_file_path)
            if tmb_output:
                successful_outputs.append(tmb_output)
        
        return successful_outputs
