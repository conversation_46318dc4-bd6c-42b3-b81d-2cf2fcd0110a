#!/usr/bin/env python3
"""
精确验证写入位置
确认x=79,y=0是否真的从15365开始写入，还是从15366开始

Author: Yuribytes
Company: Chipone TE development Team
"""

import os
import sys

# 添加父目录到路径
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, parent_dir)

from nepes_enhanced_processor import NEPESEnhancedProcessor

def create_test_file_with_markers():
    """创建带有位置标记的测试文件"""
    print("🔧 创建带位置标记的测试文件")
    print("=" * 40)
    
    dummy_file = "009.NNS157-09-E4"
    marked_file = "marked_test_file.tsk"
    
    # 读取原始文件
    with open(dummy_file, 'rb') as f:
        data = bytearray(f.read())
    
    # 在关键位置设置特殊标记
    # x=79,y=0: 位置15365-15368
    data[15365] = 0xAA  # 标记字节1
    data[15366] = 0xBB  # 标记字节2
    data[15367] = 0xCC  # 标记字节3
    data[15368] = 0xDD  # 标记字节4
    
    # x=149,y=2: 位置18085-18088
    data[18085] = 0xEE  # 标记字节1
    data[18086] = 0xFF  # 标记字节2
    data[18087] = 0x11  # 标记字节3
    data[18088] = 0x22  # 标记字节4
    
    # 保存标记文件
    with open(marked_file, 'wb') as f:
        f.write(data)
    
    print(f"✅ 创建标记文件: {marked_file}")
    print(f"   x=79,y=0 (15365-15368): AA BB CC DD")
    print(f"   x=149,y=2 (18085-18088): EE FF 11 22")
    
    return marked_file

def test_processing_with_marked_file():
    """使用标记文件测试处理"""
    print(f"\n🧪 使用标记文件测试处理")
    print("=" * 40)
    
    bump_file = "D97127.09"
    marked_file = create_test_file_with_markers()
    output_file = "marked_output.tsk"
    
    # 显示处理前的标记位置
    with open(marked_file, 'rb') as f:
        data = f.read()
    
    print(f"📊 处理前标记位置:")
    print(f"   位置15365-15368: {data[15365:15369].hex().upper()}")
    print(f"   位置18085-18088: {data[18085:18089].hex().upper()}")
    
    # 执行NEPES处理
    processor = NEPESEnhancedProcessor()
    success = processor.process_nepes_enhanced(bump_file, marked_file, output_file)
    
    if not success:
        print("❌ 处理失败")
        return False
    
    # 显示处理后的位置
    with open(output_file, 'rb') as f:
        processed_data = f.read()
    
    print(f"\n📊 处理后位置:")
    print(f"   位置15365-15368: {processed_data[15365:15369].hex().upper()}")
    print(f"   位置18085-18088: {processed_data[18085:18089].hex().upper()}")
    
    # 分析变化
    print(f"\n🎯 变化分析:")
    
    # x=79,y=0分析
    original_79 = data[15365:15369]
    processed_79 = processed_data[15365:15369]
    
    print(f"   x=79,y=0 (位置15365-15368):")
    print(f"     原始: {original_79.hex().upper()} (AA BB CC DD)")
    print(f"     处理后: {processed_79.hex().upper()}")
    
    if processed_79.hex().upper() == "0000003F":
        print(f"     ✅ 正确写入Big-Endian格式 0000003F")
    else:
        print(f"     ❌ 写入错误，应该是 0000003F")
    
    # x=149,y=2分析
    original_149 = data[18085:18089]
    processed_149 = processed_data[18085:18089]
    
    print(f"   x=149,y=2 (位置18085-18088):")
    print(f"     原始: {original_149.hex().upper()} (EE FF 11 22)")
    print(f"     处理后: {processed_149.hex().upper()}")
    
    if processed_149.hex().upper() == "0000003B":
        print(f"     ✅ 正确写入Big-Endian格式 0000003B")
    else:
        print(f"     ❌ 写入错误，应该是 0000003B")
    
    # 检查是否有位置偏移
    print(f"\n🔍 检查位置偏移:")
    
    # 检查x=79,y=0周围位置
    for offset in range(-2, 6):
        pos = 15365 + offset
        if 0 <= pos < len(processed_data):
            original_byte = data[pos]
            processed_byte = processed_data[pos]
            
            marker = ""
            if 15365 <= pos <= 15368:
                marker = " ← 目标位置"
            
            if original_byte != processed_byte:
                print(f"     位置{pos}: {original_byte:02X} → {processed_byte:02X} (变化){marker}")
            else:
                print(f"     位置{pos}: {original_byte:02X} → {processed_byte:02X} (不变){marker}")
    
    return True

def main():
    """主测试函数"""
    print("🧪 精确位置写入验证")
    print("验证x=79,y=0是否从正确位置15365开始写入")
    print("=" * 60)
    
    # 切换到测试目录
    os.chdir(os.path.dirname(os.path.abspath(__file__)))
    
    try:
        # 测试标记文件处理
        result = test_processing_with_marked_file()
        
        print("\n" + "=" * 60)
        print("🎉 验证结果:")
        if result:
            print("✅ 测试完成，请检查上述输出确认写入位置")
        else:
            print("❌ 测试失败")
            
        return result
            
    except Exception as e:
        print(f"❌ 测试过程出错: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
