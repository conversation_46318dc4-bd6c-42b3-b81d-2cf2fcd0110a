#!/usr/bin/env python3
"""
Test script for Full Map Tool centered messagebox functionality
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import tkinter as tk
from tkinter import ttk
import centered_messagebox as messagebox

def test_full_map_dialogs():
    """Test the Full Map Tool centered messagebox functionality"""
    root = tk.Tk()
    root.title("Full Map Tool - Centered MessageBox Test")
    root.geometry("900x700+100+100")
    root.configure(bg='lightgray')
    
    # Create a frame to simulate the Full Map Tool interface
    main_frame = ttk.Frame(root, padding="20")
    main_frame.pack(fill=tk.BOTH, expand=True)
    
    title_label = ttk.Label(main_frame, text="Full Map Tool - Process Multiple MAP Files", 
                           font=("Arial", 14, "bold"))
    title_label.pack(pady=(0, 20))
    
    def test_folder_confirmation():
        """Test folder addition confirmation dialog"""
        result = messagebox.askyesno(
            "Add Folder",
            "This will add ALL files from the selected folder.\n\n"
            "Please ensure the folder contains only supported MAP/TSK format files.\n"
            "Unsupported files may cause processing errors.\n\n"
            "Continue?",
            parent=root
        )
        if result:
            messagebox.showinfo("Result", "Folder addition confirmed.", parent=root)
        else:
            messagebox.showinfo("Result", "Folder addition cancelled.", parent=root)
    
    def test_processing_success():
        """Test processing success dialog"""
        messagebox.showinfo(
            "Success", 
            "All MAP files processed successfully!\n\n"
            "Output files have been generated in the specified folder.\n"
            "You can now review the Excel output files.",
            parent=root
        )
    
    def test_processing_error():
        """Test processing error dialog"""
        messagebox.showerror(
            "Error", 
            "Some files failed to process. Check the output for details.\n\n"
            "Common causes:\n"
            "• Invalid file format\n"
            "• Corrupted data\n"
            "• Insufficient memory\n\n"
            "Please check the files and try again.",
            parent=root
        )
    
    def test_no_files_warning():
        """Test no files selected warning"""
        messagebox.showwarning(
            "No Files", 
            "Please select MAP files to process.\n\n"
            "Use 'Add MAP Files...' or 'Add Folder...' to select files first.",
            parent=root
        )
    
    def test_tmb_success():
        """Test TMB processing success dialog"""
        success_msg = "TMB Processing Complete!\n\n"
        success_msg += "Generated 5 TMB files:\n"
        success_msg += "• 019.3AD416-19-F4_20250812_143021.tmb\n"
        success_msg += "• 020.3AD416-20-F4_20250812_143022.tmb\n"
        success_msg += "• 021.3AD416-21-F4_20250812_143023.tmb\n"
        success_msg += "• 022.3AD416-22-F4_20250812_143024.tmb\n"
        success_msg += "• 023.3AD416-23-F4_20250812_143025.tmb\n\n"
        success_msg += "Output folder: C:/Users/<USER>/Desktop/map_raw_data/map/test/tmb"
        
        messagebox.showinfo("TMB Processing Complete", success_msg, parent=root)
    
    def test_tmb_error():
        """Test TMB processing error dialog"""
        messagebox.showerror(
            "Error", 
            "An error occurred during TMB processing:\n\n"
            "TMB processor module not found. Please check the installation.\n\n"
            "Make sure tmb_processor.py is available in the project directory.",
            parent=root
        )
    
    def test_move_file_info():
        """Test file move information dialog"""
        messagebox.showinfo(
            "Cannot Move", 
            "File is already at the top.\n\n"
            "The selected file cannot be moved up further in the list.",
            parent=root
        )
    
    def test_selection_warning():
        """Test selection warning dialog"""
        messagebox.showwarning(
            "Multiple Selection", 
            "Please select only one file to move.\n\n"
            "Multiple file selection is not supported for move operations.",
            parent=root
        )
    
    # Create test buttons in a grid layout
    button_frame = ttk.Frame(main_frame)
    button_frame.pack(fill=tk.BOTH, expand=True)
    
    buttons = [
        ("Folder Confirmation", test_folder_confirmation, "Test folder addition confirmation"),
        ("Processing Success", test_processing_success, "Test successful processing message"),
        ("Processing Error", test_processing_error, "Test processing error message"),
        ("No Files Warning", test_no_files_warning, "Test file selection warning"),
        ("TMB Success", test_tmb_success, "Test TMB processing success"),
        ("TMB Error", test_tmb_error, "Test TMB processing error"),
        ("Move File Info", test_move_file_info, "Test file move information"),
        ("Selection Warning", test_selection_warning, "Test selection warning")
    ]
    
    for i, (text, command, tooltip) in enumerate(buttons):
        btn = ttk.Button(button_frame, text=text, command=command, width=20)
        btn.grid(row=i//2, column=i%2, padx=10, pady=10, sticky="ew")
    
    # Configure grid weights
    button_frame.columnconfigure(0, weight=1)
    button_frame.columnconfigure(1, weight=1)
    
    # Add instructions
    instructions = ttk.Label(main_frame, 
                           text="Click the buttons above to test Full Map Tool centered messagebox dialogs.\n"
                                "Notice how they appear centered on this window with detailed content preserved.",
                           justify=tk.CENTER, font=("Arial", 10))
    instructions.pack(pady=(20, 0))
    
    # Add window position info
    def update_position():
        x = root.winfo_x()
        y = root.winfo_y()
        w = root.winfo_width()
        h = root.winfo_height()
        pos_label.config(text=f"Window Position: ({x}, {y}) Size: {w}x{h}")
        root.after(1000, update_position)
    
    pos_label = ttk.Label(main_frame, text="", font=("Arial", 8))
    pos_label.pack(pady=(10, 0))
    update_position()
    
    root.mainloop()

if __name__ == "__main__":
    test_full_map_dialogs()
