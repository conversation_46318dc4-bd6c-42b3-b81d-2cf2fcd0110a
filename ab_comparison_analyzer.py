#!/usr/bin/env python3
"""
AB Comparison Analyzer
Provides advanced comparison analysis between Amap and Bmap data
Generates Summary, Correlation, and Map_compare sheets
"""

from typing import Dict, List, Tuple, Any
from openpyxl import Workbook
from openpyxl.styles import Font, Alignment, PatternFill, Border, Side
from openpyxl.utils import get_column_letter


class ABComparisonAnalyzer:
    """Handles advanced AB comparison analysis and Excel generation"""
    
    def __init__(self):
        self.amap_processor = None
        self.bmap_processor = None
        self.amap_data = None
        self.bmap_data = None
        
        # Styling
        self.header_font = Font(bold=True, size=10)
        self.data_font = Font(size=9)
        self.center_alignment = Alignment(horizontal='center', vertical='center')
        self.thin_border = Border(
            left=Side(style='thin'), right=Side(style='thin'),
            top=Side(style='thin'), bottom=Side(style='thin')
        )
        
        # Colors for Map_compare sheet - Updated to deep colors as requested
        self.same_fill = PatternFill(start_color="006400", end_color="006400", fill_type="solid")  # Deep green
        self.diff_fill = PatternFill(start_color="C71585", end_color="C71585", fill_type="solid")  # Deep pink

        # Colors for Map_compare_full sheet
        self.amap_fill = PatternFill(start_color="87CEEB", end_color="87CEEB", fill_type="solid")  # Sky blue for Amap
        self.bmap_fill = PatternFill(start_color="F0E68C", end_color="F0E68C", fill_type="solid")  # Khaki for Bmap
    
    def set_data(self, amap_processor, bmap_processor, amap_data, bmap_data):
        """Set the processors and data for analysis"""
        self.amap_processor = amap_processor
        self.bmap_processor = bmap_processor
        self.amap_data = amap_data
        self.bmap_data = bmap_data

    def clear_data(self):
        """Clear analysis data to free memory"""
        try:
            self.amap_processor = None
            self.bmap_processor = None
            self.amap_data = None
            self.bmap_data = None
            print("AB comparison analyzer data cleared")
        except Exception as e:
            print(f"Warning: Error clearing analyzer data: {e}")

    def get_memory_usage_mb(self) -> float:
        """Estimate memory usage of stored data"""
        try:
            memory_mb = 0.0

            # Estimate amap_data memory
            if self.amap_data:
                rows = len(self.amap_data)
                cols = len(self.amap_data[0]) if rows > 0 else 0
                memory_mb += (rows * cols * 2 * 4) / (1024 * 1024)

            # Estimate bmap_data memory
            if self.bmap_data:
                rows = len(self.bmap_data)
                cols = len(self.bmap_data[0]) if rows > 0 else 0
                memory_mb += (rows * cols * 2 * 4) / (1024 * 1024)

            return memory_mb
        except Exception:
            return 0.0
    
    def generate_summary_analysis(self) -> Dict[str, Any]:
        """
        Generate Summary sheet data - bin别差异统计
        Returns dict with bin comparison statistics
        """
        if not self.amap_data or not self.bmap_data:
            return {}
        
        # Get bin statistics from both maps
        amap_bins = self._get_bin_counts(self.amap_data)
        bmap_bins = self._get_bin_counts(self.bmap_data)
        
        # Calculate total dies
        total_dies = self._get_total_tested_dies()
        
        # Combine all unique bins
        all_bins = set(amap_bins.keys()) | set(bmap_bins.keys())
        
        summary_data = []
        
        for bin_num in sorted(all_bins):
            amap_count = amap_bins.get(bin_num, 0)
            bmap_count = bmap_bins.get(bin_num, 0)
            diff_count = amap_count - bmap_count
            
            amap_pct = (amap_count / total_dies * 100) if total_dies > 0 else 0.0
            bmap_pct = (bmap_count / total_dies * 100) if total_dies > 0 else 0.0
            diff_pct = amap_pct - bmap_pct
            
            summary_data.append({
                'bin': bin_num,
                'amap_count': amap_count,
                'bmap_count': bmap_count,
                'diff_count': diff_count,
                'amap_pct': amap_pct,
                'bmap_pct': bmap_pct,
                'diff_pct': diff_pct
            })
        
        # Add PASS/FAIL summary
        amap_pass, amap_fail = self._get_pass_fail_counts(self.amap_data)
        bmap_pass, bmap_fail = self._get_pass_fail_counts(self.bmap_data)
        
        pass_diff = amap_pass - bmap_pass
        fail_diff = amap_fail - bmap_fail
        
        amap_pass_pct = (amap_pass / total_dies * 100) if total_dies > 0 else 0.0
        bmap_pass_pct = (bmap_pass / total_dies * 100) if total_dies > 0 else 0.0
        amap_fail_pct = (amap_fail / total_dies * 100) if total_dies > 0 else 0.0
        bmap_fail_pct = (bmap_fail / total_dies * 100) if total_dies > 0 else 0.0
        
        return {
            'total_dies': total_dies,
            'bin_data': summary_data,
            'pass_fail': {
                'pass': {
                    'amap_count': amap_pass, 'bmap_count': bmap_pass, 'diff_count': pass_diff,
                    'amap_pct': amap_pass_pct, 'bmap_pct': bmap_pass_pct, 'diff_pct': amap_pass_pct - bmap_pass_pct
                },
                'fail': {
                    'amap_count': amap_fail, 'bmap_count': bmap_fail, 'diff_count': fail_diff,
                    'amap_pct': amap_fail_pct, 'bmap_pct': bmap_fail_pct, 'diff_pct': amap_fail_pct - bmap_fail_pct
                }
            }
        }
    
    def generate_correlation_analysis(self) -> Dict[str, Any]:
        """
        Generate Correlation sheet data - 跳bin差异矩阵
        Returns dict with correlation matrix data
        """
        if not self.amap_data or not self.bmap_data:
            return {}
        
        # Get all unique bins from both maps
        amap_bins = self._get_bin_counts(self.amap_data)
        bmap_bins = self._get_bin_counts(self.bmap_data)
        all_bins = sorted(set(amap_bins.keys()) | set(bmap_bins.keys()))
        
        total_dies = self._get_total_tested_dies()
        
        # Create correlation matrix
        correlation_matrix = {}
        
        # Initialize matrix
        for abin in all_bins:
            correlation_matrix[abin] = {}
            for bbin in all_bins:
                correlation_matrix[abin][bbin] = 0
        
        # Fill correlation matrix by comparing each position
        rows_a = len(self.amap_data)
        cols_a = len(self.amap_data[0]) if rows_a > 0 else 0
        rows_b = len(self.bmap_data)
        cols_b = len(self.bmap_data[0]) if rows_b > 0 else 0
        
        # Use minimum dimensions to avoid index errors
        min_rows = min(rows_a, rows_b)
        min_cols = min(cols_a, cols_b)
        
        for i in range(min_rows):
            for j in range(min_cols):
                amap_bin, amap_color = self.amap_data[i][j]
                bmap_bin, bmap_color = self.bmap_data[i][j]
                
                # Only count tested dies (non-zero color)
                if amap_color != 0 and bmap_color != 0:
                    correlation_matrix[amap_bin][bmap_bin] += 1
        
        return {
            'bins': all_bins,
            'matrix': correlation_matrix,
            'total_dies': total_dies
        }
    
    def _get_bin_counts(self, data) -> Dict[int, int]:
        """Get bin counts from map data"""
        bin_counts = {}
        
        for row in data:
            for category, color in row:
                if color != 0:  # Only count tested dies
                    bin_counts[category] = bin_counts.get(category, 0) + 1
        
        return bin_counts
    
    def _get_total_tested_dies(self) -> int:
        """Get total number of tested dies"""
        total = 0
        
        if self.amap_data:
            for row in self.amap_data:
                for category, color in row:
                    if color != 0:
                        total += 1
        
        return total
    
    def _get_pass_fail_counts(self, data) -> Tuple[int, int]:
        """Get pass/fail counts from map data"""
        pass_count = 0
        fail_count = 0
        
        for row in data:
            for category, color in row:
                if color != 0:  # Tested die
                    if color == 4:  # Pass color (green)
                        pass_count += 1
                    else:  # Fail color
                        fail_count += 1
        
        return pass_count, fail_count

    def write_summary_sheet(self, workbook: Workbook, summary_data: Dict[str, Any]):
        """Write Summary sheet with bin difference analysis"""
        try:
            # Create Summary sheet
            if "Summary" in workbook.sheetnames:
                ws = workbook["Summary"]
            else:
                ws = workbook.create_sheet("Summary")

            # Write header
            ws['A1'] = f"Grossdie={summary_data['total_dies']}"
            ws['A1'].font = self.header_font

            # Write column headers
            headers = ['', 'A map', 'B map', 'Diff', 'A map(%)', 'B map(%)', 'Diff(%)']
            for col, header in enumerate(headers, 1):
                cell = ws.cell(row=2, column=col)
                if header:  # Only set value if header is not empty
                    cell.value = header
                cell.font = self.header_font
                cell.alignment = self.center_alignment
                cell.border = self.thin_border

            # Write bin data
            row = 3
            for bin_data in summary_data['bin_data']:
                if bin_data['amap_count'] > 0 or bin_data['bmap_count'] > 0:  # Only show bins with data
                    ws.cell(row=row, column=1, value=bin_data['bin'])
                    ws.cell(row=row, column=2, value=bin_data['amap_count'])
                    ws.cell(row=row, column=3, value=bin_data['bmap_count'])
                    ws.cell(row=row, column=4, value=bin_data['diff_count'])
                    ws.cell(row=row, column=5, value=f"{bin_data['amap_pct']:.2f}")
                    ws.cell(row=row, column=6, value=f"{bin_data['bmap_pct']:.2f}")
                    ws.cell(row=row, column=7, value=f"{bin_data['diff_pct']:.2f}")

                    # Apply formatting
                    for col in range(1, 8):
                        cell = ws.cell(row=row, column=col)
                        cell.alignment = self.center_alignment
                        cell.border = self.thin_border

                    row += 1

            # Add empty row
            row += 1

            # Write PASS/FAIL summary
            pass_data = summary_data['pass_fail']['pass']
            fail_data = summary_data['pass_fail']['fail']

            # PASS row
            ws.cell(row=row, column=1, value="PASS")
            ws.cell(row=row, column=2, value=pass_data['amap_count'])
            ws.cell(row=row, column=3, value=pass_data['bmap_count'])
            ws.cell(row=row, column=4, value=pass_data['diff_count'])
            ws.cell(row=row, column=5, value=f"{pass_data['amap_pct']:.2f}")
            ws.cell(row=row, column=6, value=f"{pass_data['bmap_pct']:.2f}")
            ws.cell(row=row, column=7, value=f"{pass_data['diff_pct']:.2f}")

            # FAIL row
            row += 1
            ws.cell(row=row, column=1, value="FAIL")
            ws.cell(row=row, column=2, value=fail_data['amap_count'])
            ws.cell(row=row, column=3, value=fail_data['bmap_count'])
            ws.cell(row=row, column=4, value=fail_data['diff_count'])
            ws.cell(row=row, column=5, value=f"{fail_data['amap_pct']:.2f}")
            ws.cell(row=row, column=6, value=f"{fail_data['bmap_pct']:.2f}")
            ws.cell(row=row, column=7, value=f"{fail_data['diff_pct']:.2f}")

            # Apply formatting to PASS/FAIL rows
            for r in [row-1, row]:
                for col in range(1, 8):
                    cell = ws.cell(row=r, column=col)
                    cell.alignment = self.center_alignment
                    cell.border = self.thin_border
                    cell.font = self.header_font

            # Auto-adjust column widths
            for col in range(1, 8):
                ws.column_dimensions[get_column_letter(col)].width = 12

            return True

        except Exception as e:
            print(f"Error writing Summary sheet: {e}")
            return False

    def write_correlation_sheet(self, workbook: Workbook, correlation_data: Dict[str, Any]):
        """Write Correlation sheet with bin jump analysis matrix"""
        try:
            # Create Correlation sheet
            if "Correlation" in workbook.sheetnames:
                ws = workbook["Correlation"]
            else:
                ws = workbook.create_sheet("Correlation")

            bins = correlation_data['bins']
            matrix = correlation_data['matrix']
            total_dies = correlation_data['total_dies']

            # Write total dies in A1
            ws.cell(row=1, column=1, value=total_dies)
            ws.cell(row=1, column=1).font = self.header_font

            # Write B map bin headers (top row)
            for col, bin_num in enumerate(bins, 2):  # Start from column B (2)
                ws.cell(row=1, column=col, value=bin_num)
                ws.cell(row=1, column=col).font = self.header_font
                ws.cell(row=1, column=col).alignment = self.center_alignment

            # Add "B Map", "Match", "Not" headers at the end
            end_col = len(bins) + 2
            ws.cell(row=1, column=end_col, value="B Map")
            ws.cell(row=1, column=end_col + 1, value="Match")
            ws.cell(row=1, column=end_col + 2, value="Not")

            for col in [end_col, end_col + 1, end_col + 2]:
                ws.cell(row=1, column=col).font = self.header_font
                ws.cell(row=1, column=col).alignment = self.center_alignment

            # Write A map data (rows)
            for row, abin in enumerate(bins, 2):  # Start from row 2
                # A map bin number in first column
                ws.cell(row=row, column=1, value=abin)
                ws.cell(row=row, column=1).font = self.header_font
                ws.cell(row=row, column=1).alignment = self.center_alignment

                # Fill correlation matrix
                total_abin = 0
                match_count = 0

                for col, bbin in enumerate(bins, 2):
                    count = matrix[abin][bbin]
                    if count > 0:
                        ws.cell(row=row, column=col, value=count)
                        ws.cell(row=row, column=col).alignment = self.center_alignment
                        total_abin += count

                        if abin == bbin:  # Same bin = match
                            match_count += count

                # Calculate totals and percentages
                not_match_count = total_abin - match_count
                match_pct = (match_count / total_dies * 100) if total_dies > 0 else 0.0
                not_match_pct = (not_match_count / total_dies * 100) if total_dies > 0 else 0.0

                # Write totals
                ws.cell(row=row, column=end_col, value=total_abin)
                ws.cell(row=row, column=end_col + 1, value=f"{match_pct:.2f}%")
                ws.cell(row=row, column=end_col + 2, value=f"{not_match_pct:.2f}%")

                for col in [end_col, end_col + 1, end_col + 2]:
                    ws.cell(row=row, column=col).alignment = self.center_alignment

            # Write bottom summary rows
            bottom_row = len(bins) + 2

            # "A Map" row
            ws.cell(row=bottom_row, column=1, value="A Map")
            ws.cell(row=bottom_row, column=1).font = self.header_font

            for col, bin_num in enumerate(bins, 2):
                total_for_bin = sum(matrix[bin_num][bbin] for bbin in bins)
                if total_for_bin > 0:
                    ws.cell(row=bottom_row, column=col, value=total_for_bin)
                    ws.cell(row=bottom_row, column=col).alignment = self.center_alignment

            # "Match" row
            match_row = bottom_row + 1
            ws.cell(row=match_row, column=1, value="Match")
            ws.cell(row=match_row, column=1).font = self.header_font

            for col, bin_num in enumerate(bins, 2):
                total_for_bin = sum(matrix[bin_num][bbin] for bbin in bins)
                match_pct = (total_for_bin / total_dies * 100) if total_dies > 0 else 0.0
                ws.cell(row=match_row, column=col, value=f"{match_pct:.2f}%")
                ws.cell(row=match_row, column=col).alignment = self.center_alignment

            # "Not" row
            not_row = bottom_row + 2
            ws.cell(row=not_row, column=1, value="Not")
            ws.cell(row=not_row, column=1).font = self.header_font

            for col, bin_num in enumerate(bins, 2):
                ws.cell(row=not_row, column=col, value="0.00%")  # Assuming perfect match for now
                ws.cell(row=not_row, column=col).alignment = self.center_alignment

            # Auto-adjust column widths
            for col in range(1, len(bins) + 5):
                ws.column_dimensions[get_column_letter(col)].width = 8

            return True

        except Exception as e:
            print(f"Error writing Correlation sheet: {e}")
            return False

    def write_map_compare_sheet(self, workbook: Workbook):
        """Write Map_compare sheet with position-by-position comparison"""
        try:
            # Create Map_compare sheet
            if "Map_compare" in workbook.sheetnames:
                ws = workbook["Map_compare"]
            else:
                ws = workbook.create_sheet("Map_compare")

            if not self.amap_data or not self.bmap_data:
                ws['A1'] = "No data available for comparison"
                return False

            # Get dimensions (use minimum to avoid index errors)
            rows_a = len(self.amap_data)
            cols_a = len(self.amap_data[0]) if rows_a > 0 else 0
            rows_b = len(self.bmap_data)
            cols_b = len(self.bmap_data[0]) if rows_b > 0 else 0

            min_rows = min(rows_a, rows_b)
            min_cols = min(cols_a, cols_b)

            if min_rows == 0 or min_cols == 0:
                ws['A1'] = "No comparable data available"
                return False

            # Write headers
            ws['A1'] = "Position"
            ws['B1'] = "Amap Bin"
            ws['C1'] = "Bmap Bin"
            ws['D1'] = "Status"

            for col in range(1, 5):
                cell = ws.cell(row=1, column=col)
                cell.font = self.header_font
                cell.alignment = self.center_alignment
                cell.border = self.thin_border

            # Collect all comparison data first
            comparison_data = []
            same_count = 0
            diff_count = 0

            for i in range(min_rows):
                for j in range(min_cols):
                    amap_bin, amap_color = self.amap_data[i][j]
                    bmap_bin, bmap_color = self.bmap_data[i][j]

                    # Only process tested dies (non-zero color)
                    if amap_color != 0 or bmap_color != 0:
                        position = f"({i+1},{j+1})"
                        status = "Same" if amap_bin == bmap_bin else "Different"

                        comparison_data.append({
                            'position': position,
                            'amap_bin': amap_bin if amap_color != 0 else "N/A",
                            'bmap_bin': bmap_bin if bmap_color != 0 else "N/A",
                            'status': status
                        })

                        if status == "Same":
                            same_count += 1
                        else:
                            diff_count += 1

                        # Limit collection for very large files
                        if len(comparison_data) > 10000:  # Limit to 10k rows for performance
                            break

                if len(comparison_data) > 10000:
                    break

            # Sort data: Different first, then Same
            comparison_data.sort(key=lambda x: (x['status'] == "Same", x['position']))

            # Write sorted data to Excel
            row = 2
            for data in comparison_data:
                ws.cell(row=row, column=1, value=data['position'])
                ws.cell(row=row, column=2, value=data['amap_bin'])
                ws.cell(row=row, column=3, value=data['bmap_bin'])
                ws.cell(row=row, column=4, value=data['status'])

                # Apply color coding (deep colors as requested)
                fill = self.same_fill if data['status'] == "Same" else self.diff_fill

                for col in range(1, 5):
                    cell = ws.cell(row=row, column=col)
                    cell.alignment = self.center_alignment
                    cell.border = self.thin_border
                    cell.fill = fill

                row += 1

            # Add summary at the bottom (ensure it's always written)
            summary_row = max(row + 2, 10)  # Ensure summary is at least at row 10
            ws.cell(row=summary_row, column=1, value="Summary:")
            ws.cell(row=summary_row, column=1).font = self.header_font

            ws.cell(row=summary_row + 1, column=1, value="Same positions:")
            ws.cell(row=summary_row + 1, column=2, value=same_count)
            ws.cell(row=summary_row + 1, column=2).fill = self.same_fill

            ws.cell(row=summary_row + 2, column=1, value="Different positions:")
            ws.cell(row=summary_row + 2, column=2, value=diff_count)
            ws.cell(row=summary_row + 2, column=2).fill = self.diff_fill

            total_compared = same_count + diff_count
            match_rate = (same_count / total_compared * 100) if total_compared > 0 else 0.0

            ws.cell(row=summary_row + 3, column=1, value="Match rate:")
            ws.cell(row=summary_row + 3, column=2, value=f"{match_rate:.2f}%")

            # Add formatting to summary rows
            for r in range(summary_row, summary_row + 4):
                for c in range(1, 3):
                    cell = ws.cell(row=r, column=c)
                    cell.border = self.thin_border

            # Auto-adjust column widths
            ws.column_dimensions['A'].width = 15
            ws.column_dimensions['B'].width = 12
            ws.column_dimensions['C'].width = 12
            ws.column_dimensions['D'].width = 12

            return True

        except Exception as e:
            print(f"Error writing Map_compare sheet: {e}")
            return False

    def write_map_compare_full_sheet(self, workbook: Workbook):
        """Write Map_compare_full sheet with combined comparison format"""
        try:
            # Create Map_compare_full sheet
            if "Map_compare_full" in workbook.sheetnames:
                ws = workbook["Map_compare_full"]
            else:
                ws = workbook.create_sheet("Map_compare_full")

            if not self.amap_data or not self.bmap_data:
                ws['A1'] = "No data available for map comparison"
                return False

            # Get dimensions (use minimum to avoid index errors)
            rows_a = len(self.amap_data)
            cols_a = len(self.amap_data[0]) if rows_a > 0 else 0
            rows_b = len(self.bmap_data)
            cols_b = len(self.bmap_data[0]) if rows_b > 0 else 0

            min_rows = min(rows_a, rows_b)
            min_cols = min(cols_a, cols_b)

            if min_rows == 0 or min_cols == 0:
                ws['A1'] = "No comparable data available"
                return False

            # Write title
            ws['A1'] = "Map Comparison (R0 Format)"
            ws['A1'].font = self.header_font

            # Write column headers (numbers 1, 2, 3, ...)
            for j in range(min_cols):
                header_cell = ws.cell(row=1, column=j + 2)  # Start from column B
                header_cell.value = j + 1
                header_cell.font = self.header_font
                header_cell.alignment = self.center_alignment
                header_cell.border = self.thin_border

            # Write data for each row with combined comparison
            for i in range(min_rows):
                data_row = i + 2  # Data starts from row 2

                # Write row number
                ws.cell(row=data_row, column=1, value=i + 1)
                ws.cell(row=data_row, column=1).font = self.header_font
                ws.cell(row=data_row, column=1).alignment = self.center_alignment
                ws.cell(row=data_row, column=1).border = self.thin_border

                # Write data for each column in this row
                for j in range(min_cols):
                    amap_bin, amap_color = self.amap_data[i][j]
                    bmap_bin, bmap_color = self.bmap_data[i][j]

                    # Only process if at least one has data
                    if amap_color != 0 or bmap_color != 0:
                        cell = ws.cell(row=data_row, column=j + 2)

                        # Create combined display text
                        amap_display = str(amap_bin) if amap_color != 0 else "N/A"
                        bmap_display = str(bmap_bin) if bmap_color != 0 else "N/A"

                        # Format: corr value on top, qual value on bottom
                        cell.value = f"{amap_display}\n{bmap_display}"
                        cell.alignment = Alignment(horizontal='center', vertical='center', wrap_text=True)
                        cell.border = self.thin_border

                        # Apply color based on comparison
                        if amap_color != 0 and bmap_color != 0:
                            if amap_bin == bmap_bin:  # Same bin values
                                # Check if both are pass or both are fail
                                if amap_color == 4 and bmap_color == 4:  # Both pass
                                    cell.fill = PatternFill(start_color="00FF00", end_color="00FF00", fill_type="solid")  # Green
                                elif amap_color != 4 and bmap_color != 4:  # Both fail
                                    cell.fill = PatternFill(start_color="FF0000", end_color="FF0000", fill_type="solid")  # Red
                                else:  # Mixed pass/fail
                                    cell.fill = PatternFill(start_color="FFA500", end_color="FFA500", fill_type="solid")  # Orange
                            else:  # Different bin values
                                cell.fill = PatternFill(start_color="FFFF00", end_color="FFFF00", fill_type="solid")  # Yellow
                        elif amap_color != 0:  # Only Amap has data
                            cell.fill = PatternFill(start_color="87CEEB", end_color="87CEEB", fill_type="solid")  # Light blue
                        elif bmap_color != 0:  # Only Bmap has data
                            cell.fill = PatternFill(start_color="F0E68C", end_color="F0E68C", fill_type="solid")  # Light yellow

            # Add legend
            legend_start_row = min_rows + 4
            ws.cell(row=legend_start_row, column=1, value="Legend:")
            ws.cell(row=legend_start_row, column=1).font = self.header_font

            legend_items = [
                ("Green: Same bin, both Pass", "00FF00"),
                ("Red: Same bin, both Fail", "FF0000"),
                ("Orange: Same bin, mixed Pass/Fail", "FFA500"),
                ("Yellow: Different bins", "FFFF00"),
                ("Light Blue: Only Amap data", "87CEEB"),
                ("Light Yellow: Only Bmap data", "F0E68C")
            ]

            for idx, (text, color) in enumerate(legend_items):
                legend_row = legend_start_row + idx + 1
                ws.cell(row=legend_row, column=1, value=text)
                ws.cell(row=legend_row, column=1).fill = PatternFill(start_color=color, end_color=color, fill_type="solid")
                ws.cell(row=legend_row, column=1).border = self.thin_border

            # Add explanation
            explanation_row = legend_start_row + len(legend_items) + 2
            ws.cell(row=explanation_row, column=1, value="Format: Top line = Amap (corr), Bottom line = Bmap (qual)")
            ws.cell(row=explanation_row, column=1).font = self.header_font

            # Auto-adjust column widths and row heights
            for col in range(1, min_cols + 3):
                ws.column_dimensions[get_column_letter(col)].width = 6

            # Set row height for data rows to accommodate two lines
            for row in range(2, min_rows + 2):
                ws.row_dimensions[row].height = 30

            return True

        except Exception as e:
            print(f"Error writing Map_compare_full sheet: {e}")
            return False
