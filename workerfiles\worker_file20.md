任务流程：
针对Full Map Tool工具功能完善:
1，process tmb执行内容修改：
	  ---tmb生成文件格式命名修改：
	     --原命名方式为waferid+"_"+load配置文件excel中的H5+时间戳.tmb(generate_tmb_filename 函数）
	     --设置增加两个变量{lotid（和waferid获取方式一样，获取MAP file，起始地址：82，抓18个bytes，每个bytes转换成asic2码显示。)slotid(和waferid获取方式一样，获取MAP file，起始地址：102，抓2个bytes，每个bytes转换成十进制显示。）
		 --增加判断load配置文件excel中的H5，如果H5为CP1，命名格式中显示1，如果H5为CP2，命名格式中显示2。
		 --命名格式：lotid+"_"+load配置文件excel中的H5后的内容的判断（如果为CP1，这里填1）+ slotid.tmb

开发注意：

上述功能开发注意事项：
     --- 主要功能不要破坏，架构做到统一
     --- GUI尽量统一，不要占用太多空间
     --- 复用代码部分，尽量做到代码统一

要求：
1，测试脚本和文件请放到test文件夹
2，生成的md说明文档，请放到temp文件夹，不能在主项目文件夹下生成
3，程序架构统一，框架统一，代码尽量简洁

测试文件说明：
1，测试map可以选择test下的019.3AD416-19-F4
2，配置文件excel为test下的CP1_program_bin.xlsx
