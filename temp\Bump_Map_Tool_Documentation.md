# Bump Map Tool - 功能设计文档

## 📋 项目概述

**Bump Map Tool** 是 TSK/MAP 文件处理工具套件的第三个核心工具，专门用于处理不同测试厂的 bump map 文件和原始 dummy map 文件的叠加处理，生成最终的 TSK map 文件。

### 🎯 核心功能
- **多测试厂支持**：支持20个主要测试厂的不同格式
- **文件叠加处理**：将 bump map 和 dummy map 进行智能叠加
- **格式保持**：输出文件保持原 dummy map 的格式（.tsk/.map等）
- **GUI 统一性**：与现有工具保持一致的用户界面风格

## 🏗️ 架构设计

### 核心组件架构

```
Bump Map Tool
├── bump_map_tool_frame.py     # GUI 界面框架
├── bump_map_processor.py      # 核心处理引擎
├── test_house_processors/     # 测试厂特定处理器
│   ├── ASEGroupProcessor      # ASE Group 格式处理
│   ├── SPILProcessor          # SPIL 格式处理
│   ├── AmkorProcessor         # Amkor 格式处理
│   └── CustomProcessor        # 自定义格式处理
└── test/                      # 测试脚本
    └── test_bump_map_tool.py  # 综合测试套件
```

### 设计模式
- **策略模式**：不同测试厂使用不同的处理策略
- **工厂模式**：动态创建测试厂特定的处理器
- **模板方法模式**：统一的处理流程，具体实现由子类完成

## 🖥️ GUI 设计

### 界面布局

```
┌─────────────────────────────────────────────────────────────┐
│ Bump Map To TSK Map Tool                    [← Back to Tool Selection] │
├─────────────────────────────────────────────────────────────┤
│ File Selection                                              │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ Bump Map File:  [________________________] [Browse...] │ │
│ │ Dummy Map File: [________________________] [Browse...] │ │
│ └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│ Test House Selection                                        │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ Select the test house format for proper alignment:     │ │
│ │ ○ ASE Group              ○ SPIL                        │ │
│ │ ○ Amkor                  ○ JCET                        │ │
│ │ ○ ChipMOS                ○ Powertech                   │ │
│ │ ... (scrollable area for 20 test houses)               │ │
│ └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│ Output Settings                                             │
│ ┌─────────────────────────────────────────────────────────┐ │
│ │ Output File: [________________________] [Browse...]    │ │
│ └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│ [Process Maps] [Clear Memory]                               │
├─────────────────────────────────────────────────────────────┤
│ Status: Ready - Select bump and dummy map files            │
└─────────────────────────────────────────────────────────────┘
```

### 关键特性
- **文件选择**：支持 .map、.tsk、.txt 等多种格式
- **测试厂选择**：20个测试厂的单选按钮，支持滚动
- **输出格式保持**：自动检测 dummy map 格式并保持一致
- **状态反馈**：实时显示处理状态和进度

## 🔧 支持的测试厂

### 主要测试厂列表

| 测试厂 ID | 测试厂名称 | 状态 |
|-----------|------------|------|
| ASE_Group | ASE Group (Advanced Semiconductor Engineering) | 🚧 开发中 |
| SPIL | Siliconware Precision Industries | 🚧 开发中 |
| Amkor | Amkor Technology | 📋 计划中 |
| JCET | Jiangsu Changjiang Electronics Technology | 📋 计划中 |
| ChipMOS | ChipMOS Technologies | 📋 计划中 |
| Powertech | Powertech Technology | 📋 计划中 |
| KYEC | King Yuan Electronics | 📋 计划中 |
| Chipbond | Chipbond Technology Corporation | 📋 计划中 |
| Unisem | Unisem Group | 📋 计划中 |
| UTAC | United Test and Assembly Center | 📋 计划中 |
| Formosa | Formosa Advanced Technologies | 📋 计划中 |
| Lingsen | Lingsen Precision Industries | 📋 计划中 |
| Tianshui | Tianshui Huatian Technology | 📋 计划中 |
| NEPES | NEPES Corporation | 📋 计划中 |
| ChipPAC | ChipPAC (STATS ChipPAC) | 📋 计划中 |
| Carsem | Carsem Semiconductor | 📋 计划中 |
| Signetics | Signetics Corporation | 📋 计划中 |
| ASEN | ASEN Semiconductors | 📋 计划中 |
| TongFu | TongFu Microelectronics | 📋 计划中 |
| Custom | Custom Test House Format | 📋 计划中 |

## 🔄 处理流程

### 标准处理流程

```mermaid
graph TD
    A[选择 Bump Map 文件] --> B[选择 Dummy Map 文件]
    B --> C[选择测试厂格式]
    C --> D[设置输出路径]
    D --> E[开始处理]
    E --> F[解析 Bump Map]
    F --> G[解析 Dummy Map]
    G --> H[执行对齐算法]
    H --> I[生成输出文件]
    I --> J[处理完成]
```

### 处理步骤详解

1. **文件解析阶段**
   - 根据测试厂格式解析 bump map 文件
   - 解析 dummy map 文件结构
   - 验证文件格式和完整性

2. **对齐算法阶段**
   - 应用测试厂特定的对齐算法
   - 处理坐标系转换
   - 执行 bump 和 dummy 数据叠加

3. **输出生成阶段**
   - 保持原 dummy map 文件格式
   - 生成最终的 TSK/MAP 文件
   - 验证输出文件完整性

## 🧪 测试策略

### 测试覆盖范围

1. **单元测试**
   - BumpMapProcessor 核心功能
   - 各测试厂处理器的基本功能
   - 文件验证和内存管理

2. **集成测试**
   - GUI 与处理器的集成
   - 文件选择和处理流程
   - 错误处理和异常情况

3. **GUI 测试**
   - 界面初始化和显示
   - 用户交互和状态更新
   - 文件选择和测试厂选择

### 测试文件
- `test/test_bump_map_tool.py` - 综合测试套件
- 包含自动化测试和手动验证

## 🔮 未来扩展计划

### 第一阶段（当前）
- ✅ GUI 框架完成
- ✅ 核心处理器架构
- ✅ 测试套件建立
- 🚧 基础功能集成

### 第二阶段（下一步）
- 📋 实现 ASE Group 格式解析
- 📋 实现 SPIL 格式解析
- 📋 开发对齐算法
- 📋 完成输出文件生成

### 第三阶段（扩展）
- 📋 添加更多测试厂支持
- 📋 优化处理性能
- 📋 增加批处理功能
- 📋 添加预览功能

## 📁 文件结构

```
tsk_map_develop_08072025/
├── bump_map_tool_frame.py          # GUI 框架 ✅
├── bump_map_processor.py           # 核心处理器 ✅
├── tool_selector_frame.py          # 工具选择器（已更新）✅
├── main_application.py             # 主应用（已更新）✅
├── test/
│   └── test_bump_map_tool.py       # 测试脚本 ✅
└── temp/
    └── Bump_Map_Tool_Documentation.md  # 本文档 ✅
```

## 🎯 开发状态

### 已完成 ✅
- [x] GUI 界面设计和实现
- [x] 核心处理器架构
- [x] 测试厂处理器抽象基类
- [x] 主应用集成
- [x] 测试套件建立
- [x] 文档编写

### 进行中 🚧
- [ ] 具体测试厂格式解析实现
- [ ] 对齐算法开发
- [ ] 输出文件生成逻辑

### 计划中 📋
- [ ] 性能优化
- [ ] 错误处理增强
- [ ] 用户体验改进

---

**开发团队**: Yuribytes  
**公司**: Chipone TE development Team  
**版本**: 1.0.0  
**最后更新**: 2025-08-10
