#!/usr/bin/env python3
"""
Test script for Bump Map Tool Back button functionality
Tests that Back button now includes memory cleanup and popup like Full Map Tool

Author: Yuribytes
Company: Chipone TE development Team
"""

import sys
import os
import tkinter as tk
from tkinter import ttk
import time

# Add parent directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


class BumpMapBackButtonTester:
    """Test class for Bump Map Tool Back button functionality"""
    
    def __init__(self):
        self.test_results = {}
    
    def test_back_button_functionality(self):
        """Test Bump Map Tool Back button with memory cleanup and popup"""
        print("🧪 Testing Bump Map Tool - Enhanced Back Button Functionality")
        print("=" * 60)
        
        try:
            from bump_map_tool_frame import BumpMapToolFrame
            
            # Create test window
            test_root = tk.Tk()
            test_root.title("Bump Map Tool - Back Button Test")
            test_root.geometry("1000x800")
            test_root.withdraw()  # Hide for automated test
            
            # Mock app controller
            class MockController:
                def __init__(self, root):
                    self.root = root
                    self.show_tool_selector_called = False
                
                def return_to_selector_with_confirmation(self, tool_name, has_unsaved):
                    print(f"Mock: return_to_selector_with_confirmation called for {tool_name}, unsaved: {has_unsaved}")
                    return True
                
                def show_tool_selector(self):
                    print("Mock: show_tool_selector called")
                    self.show_tool_selector_called = True
            
            controller = MockController(test_root)
            bump_gui = BumpMapToolFrame(test_root, controller)
            
            # Test 1: Check if return_to_selector method exists and has enhanced functionality
            print("📋 Test 1: Checking return_to_selector method...")
            
            if hasattr(bump_gui, 'return_to_selector'):
                print("✅ return_to_selector method exists")
                self.test_results["method_exists"] = True
            else:
                print("❌ return_to_selector method missing")
                self.test_results["method_exists"] = False
                return False
            
            # Test 2: Check if show_return_cleanup_popup method exists
            print("\n📋 Test 2: Checking show_return_cleanup_popup method...")
            
            if hasattr(bump_gui, 'show_return_cleanup_popup'):
                print("✅ show_return_cleanup_popup method exists")
                self.test_results["popup_method_exists"] = True
            else:
                print("❌ show_return_cleanup_popup method missing")
                self.test_results["popup_method_exists"] = False
            
            # Test 3: Test the popup functionality directly
            print("\n📋 Test 3: Testing popup functionality...")
            
            try:
                # Test popup with different scenarios
                print("   Testing popup with memory freed...")
                bump_gui.show_return_cleanup_popup(15.5, 1)
                time.sleep(0.1)  # Allow popup to be created
                print("   ✅ Popup with memory freed - OK")
                
                print("   Testing popup with no memory to free...")
                bump_gui.show_return_cleanup_popup(0.0, 1)
                time.sleep(0.1)  # Allow popup to be created
                print("   ✅ Popup with no memory - OK")
                
                print("   Testing popup with no processors...")
                bump_gui.show_return_cleanup_popup(0.0, 0)
                time.sleep(0.1)  # Allow popup to be created
                print("   ✅ Popup with no processors - OK")
                
                self.test_results["popup_functionality"] = True
                
            except Exception as e:
                print(f"   ❌ Popup functionality failed: {e}")
                self.test_results["popup_functionality"] = False
            
            # Test 4: Test the enhanced return_to_selector functionality
            print("\n📋 Test 4: Testing enhanced return_to_selector...")
            
            try:
                # Set some state to simulate unsaved work
                bump_gui.bump_map_file_path.set("test_bump.map")
                bump_gui.dummy_map_file_path.set("test_dummy.map")
                bump_gui.processing_count = 1
                
                # Mock the processor for memory testing
                class MockProcessor:
                    def get_memory_usage_mb(self):
                        return 10.5
                
                bump_gui.bump_processor = MockProcessor()
                
                print("   Calling return_to_selector with simulated data...")
                
                # Call return_to_selector (this should trigger cleanup and popup)
                bump_gui.return_to_selector()
                
                # Check if state was cleared
                if (not bump_gui.bump_map_file_path.get() and 
                    not bump_gui.dummy_map_file_path.get() and
                    not bump_gui.output_file_path.get() and
                    not bump_gui.selected_test_house.get()):
                    print("   ✅ State cleared successfully")
                    self.test_results["state_cleared"] = True
                else:
                    print("   ❌ State not properly cleared")
                    self.test_results["state_cleared"] = False
                
                # Wait a moment for the delayed call
                time.sleep(1.2)
                
                # Check if show_tool_selector was called
                if controller.show_tool_selector_called:
                    print("   ✅ show_tool_selector called after delay")
                    self.test_results["selector_called"] = True
                else:
                    print("   ❌ show_tool_selector not called")
                    self.test_results["selector_called"] = False
                
                self.test_results["enhanced_functionality"] = True
                
            except Exception as e:
                print(f"   ❌ Enhanced return_to_selector failed: {e}")
                self.test_results["enhanced_functionality"] = False
            
            # Test 5: Compare with Full Map Tool functionality
            print("\n📋 Test 5: Comparing with Full Map Tool...")
            
            try:
                from full_map_tool_frame import FullMapToolFrame
                
                # Create Full Map Tool for comparison
                full_controller = MockController(test_root)
                full_gui = FullMapToolFrame(test_root, full_controller)
                
                # Check if both tools have the same methods
                bump_methods = [method for method in dir(bump_gui) if 'return' in method or 'popup' in method]
                full_methods = [method for method in dir(full_gui) if 'return' in method or 'popup' in method]
                
                common_methods = set(bump_methods) & set(full_methods)
                
                if 'return_to_selector' in common_methods and 'show_return_cleanup_popup' in common_methods:
                    print("   ✅ Bump Map Tool has same methods as Full Map Tool")
                    self.test_results["method_parity"] = True
                else:
                    print("   ❌ Method parity with Full Map Tool incomplete")
                    self.test_results["method_parity"] = False
                
            except Exception as e:
                print(f"   ❌ Comparison with Full Map Tool failed: {e}")
                self.test_results["method_parity"] = False
            
            test_root.destroy()
            return True
            
        except Exception as e:
            print(f"❌ Bump Map Tool Back button test failed: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def run_all_tests(self):
        """Run all Back button tests"""
        print("🚀 Bump Map Tool - Enhanced Back Button Test Suite")
        print("=" * 65)
        
        # Run the test
        success = self.test_back_button_functionality()
        
        # Summary
        print("\n" + "=" * 65)
        print("📋 Test Results Summary")
        print("=" * 65)
        
        total_tests = len(self.test_results)
        passed_tests = sum(self.test_results.values())
        
        for test_name, result in self.test_results.items():
            status = "✅ PASS" if result else "❌ FAIL"
            print(f"{test_name:<25} {status}")
        
        print("-" * 65)
        print(f"Total Tests: {total_tests}")
        print(f"Passed: {passed_tests}")
        print(f"Failed: {total_tests - passed_tests}")
        print(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
        
        if passed_tests == total_tests:
            print("\n🎉 All Bump Map Tool Back button tests PASSED!")
            print("✅ Back button now includes:")
            print("   • Automatic memory cleanup")
            print("   • 1-second cleanup popup")
            print("   • State clearing")
            print("   • Delayed return to main menu")
            print("   • Same functionality as Full Map Tool")
        else:
            print(f"\n⚠️  {total_tests - passed_tests} test(s) failed.")
        
        return passed_tests == total_tests


def main():
    """Main test function"""
    tester = BumpMapBackButtonTester()
    success = tester.run_all_tests()
    
    if success:
        print("\n🎯 Bump Map Tool Back button enhancement is complete and working!")
        print("🔄 Now matches Full Map Tool functionality perfectly!")
    else:
        print("\n🔧 Some Back button enhancements need attention.")
    
    return success


if __name__ == "__main__":
    main()
