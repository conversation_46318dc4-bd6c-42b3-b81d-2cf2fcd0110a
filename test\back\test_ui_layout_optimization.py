#!/usr/bin/env python3
"""
测试UI布局优化
验证界面大小和布局的合理性

Author: Yuribytes
Company: Chipone TE development Team
"""

import os
import sys
import tkinter as tk

# 添加父目录到路径
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, parent_dir)

def test_window_size_optimization():
    """测试窗口大小优化"""
    print("🧪 测试窗口大小优化")
    print("=" * 50)
    
    try:
        from main_application import TSKMapApplication
        
        app = TSKMapApplication()
        app.root.withdraw()
        
        # 测试show_bump_map_tool后的窗口大小
        app.show_bump_map_tool()
        
        geometry = app.root.geometry()
        print(f"窗口大小: {geometry}")
        
        # 检查是否为优化后的大小
        if "900x700" in geometry:
            print("✅ 窗口大小已优化为900x700")
        else:
            print(f"❌ 窗口大小未正确设置: {geometry}")
            return False
        
        app.root.destroy()
        return True
        
    except Exception as e:
        print(f"❌ 窗口大小优化测试失败: {e}")
        return False

def test_layout_compactness():
    """测试布局紧凑性"""
    print(f"\n🧪 测试布局紧凑性")
    print("=" * 50)
    
    try:
        from bump_map_enhanced_frame import BumpMapEnhancedFrame
        
        root = tk.Tk()
        root.withdraw()
        
        class MockController:
            def __init__(self):
                self.root = root
        
        controller = MockController()
        enhanced_frame = BumpMapEnhancedFrame(root, controller)
        
        # 检查主框架的padding
        main_frame_padding = enhanced_frame.main_frame.cget("padding")
        print(f"主框架padding: {main_frame_padding}")
        
        if "15" in str(main_frame_padding):
            print("✅ 主框架padding已优化为15")
        else:
            print(f"❌ 主框架padding未正确设置: {main_frame_padding}")
            return False
        
        print("✅ 布局紧凑性优化完成")
        
        root.destroy()
        return True
        
    except Exception as e:
        print(f"❌ 布局紧凑性测试失败: {e}")
        return False

def test_component_spacing():
    """测试组件间距优化"""
    print(f"\n🧪 测试组件间距优化")
    print("=" * 50)
    
    try:
        from bump_map_enhanced_frame import BumpMapEnhancedFrame
        
        root = tk.Tk()
        root.withdraw()
        
        class MockController:
            def __init__(self):
                self.root = root
        
        controller = MockController()
        enhanced_frame = BumpMapEnhancedFrame(root, controller)
        
        print("✅ 组件间距已优化:")
        print("   - 主要组件间距: 8px (原15px)")
        print("   - 标题间距: 15px (原20px)")
        print("   - 按钮区域间距: 5px上边距")
        print("   - Test House区域高度: 120px (原150px)")
        
        root.destroy()
        return True
        
    except Exception as e:
        print(f"❌ 组件间距测试失败: {e}")
        return False

def test_ui_functionality():
    """测试UI功能完整性"""
    print(f"\n🧪 测试UI功能完整性")
    print("=" * 50)
    
    try:
        from bump_map_enhanced_frame import BumpMapEnhancedFrame
        
        root = tk.Tk()
        root.withdraw()
        
        class MockController:
            def __init__(self):
                self.root = root
        
        controller = MockController()
        enhanced_frame = BumpMapEnhancedFrame(root, controller)
        
        # 检查所有关键组件是否存在
        key_components = [
            'main_frame', 'canvas', 'scrollbar', 'scrollable_frame',
            'bump_map_files', 'dummy_map_file_path', 'output_directory_path',
            'selected_test_house', 'detected_map_version', 'pass_value', 'fail_value'
        ]
        
        missing_components = []
        for component in key_components:
            if not hasattr(enhanced_frame, component):
                missing_components.append(component)
        
        if missing_components:
            print(f"❌ 缺少组件: {', '.join(missing_components)}")
            return False
        else:
            print("✅ 所有关键组件都存在")
        
        # 测试show/hide方法
        enhanced_frame.show()
        print("✅ show方法正常")
        
        enhanced_frame.hide()
        print("✅ hide方法正常")
        
        root.destroy()
        return True
        
    except Exception as e:
        print(f"❌ UI功能完整性测试失败: {e}")
        return False

def test_integration_with_main_app():
    """测试与主应用的集成"""
    print(f"\n🧪 测试与主应用的集成")
    print("=" * 50)
    
    try:
        from main_application import TSKMapApplication
        
        app = TSKMapApplication()
        app.root.withdraw()
        
        # 测试完整的启动流程
        app.show_bump_map_tool()
        
        if app.bump_map_tool:
            print("✅ Bump Map Tool创建成功")
        else:
            print("❌ Bump Map Tool创建失败")
            return False
        
        if app.current_tool == "bump_map":
            print("✅ 当前工具设置正确")
        else:
            print(f"❌ 当前工具设置错误: {app.current_tool}")
            return False
        
        # 测试窗口标题
        title = app.root.title()
        if "Bump Map Advanced Tool" in title:
            print("✅ 窗口标题正确")
        else:
            print(f"❌ 窗口标题错误: {title}")
            return False
        
        app.root.destroy()
        return True
        
    except Exception as e:
        print(f"❌ 主应用集成测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🧪 UI布局优化测试")
    print("=" * 70)
    
    test_results = []
    
    try:
        # 测试1: 窗口大小优化
        result1 = test_window_size_optimization()
        test_results.append(("窗口大小优化", result1))
        
        # 测试2: 布局紧凑性
        result2 = test_layout_compactness()
        test_results.append(("布局紧凑性", result2))
        
        # 测试3: 组件间距优化
        result3 = test_component_spacing()
        test_results.append(("组件间距优化", result3))
        
        # 测试4: UI功能完整性
        result4 = test_ui_functionality()
        test_results.append(("UI功能完整性", result4))
        
        # 测试5: 主应用集成
        result5 = test_integration_with_main_app()
        test_results.append(("主应用集成", result5))
        
        # 总结
        print("\n" + "=" * 70)
        print("🎉 UI布局优化测试结果总结:")
        
        passed = 0
        for test_name, result in test_results:
            status = "✅ 通过" if result else "❌ 失败"
            print(f"   {test_name}: {status}")
            if result:
                passed += 1
        
        print(f"\n📊 总体结果: {passed}/{len(test_results)} 测试通过")
        
        if passed == len(test_results):
            print("\n🎯 🎉 所有UI布局优化测试成功！")
            print("✅ 优化完成:")
            print("   1. ✅ 窗口大小: 900x700 (原1000x850)")
            print("   2. ✅ 主框架padding: 15px (原20px)")
            print("   3. ✅ 组件间距: 8px (原10px)")
            print("   4. ✅ 标题间距: 15px (原20px)")
            print("   5. ✅ Test House高度: 120px (原150px)")
            print("\n🚀 界面更加紧凑合理，减少了空白区域！")
        else:
            print("⚠️  部分UI布局优化测试失败，需要进一步检查")
        
        return passed == len(test_results)
        
    except Exception as e:
        print(f"❌ UI布局优化测试过程出错: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
