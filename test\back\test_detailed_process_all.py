#!/usr/bin/env python3
"""
Test script for Full Map Tool detailed "Process All Files" messagebox
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import tkinter as tk
from tkinter import ttk
import centered_messagebox as messagebox

def test_detailed_process_all():
    """Test the detailed Process All Files messagebox"""
    root = tk.Tk()
    root.title("Full Map Tool - Detailed Process All Files Test")
    root.geometry("900x700+100+100")
    root.configure(bg='lightgray')
    
    # Create a frame to simulate the Full Map Tool interface
    main_frame = ttk.Frame(root, padding="20")
    main_frame.pack(fill=tk.BOTH, expand=True)
    
    title_label = ttk.Label(main_frame, text="Full Map Tool - Process All Files Detailed Messages", 
                           font=("Arial", 14, "bold"))
    title_label.pack(pady=(0, 20))
    
    def test_success_few_files():
        """Test success message with few files"""
        success_msg = "Full Map Processing Complete!\n\n"
        success_msg += f"Successfully processed 3 MAP files:\n"
        success_msg += f"• 019.3AD416-19-F4\n"
        success_msg += f"• 020.3AD416-20-F4\n"
        success_msg += f"• 021.3AD416-21-F4\n"
        success_msg += f"\nOutput file: FullMap_3files_YYYYMMDD_HHMMSS.xlsx"
        success_msg += f"\nOutput folder: C:/Users/<USER>/Desktop/map_raw_data/map/test"
        success_msg += f"\n\nProcessing options applied:"
        success_msg += f"\n• Rotation angle: 0°"
        success_msg += f"\n• Filter empty areas: Yes"
        success_msg += f"\n• Sort bins by quantity: Yes"
        success_msg += f"\n• Configuration file: CP1_program_bin.xlsx"
        
        messagebox.showinfo("Processing Complete", success_msg, parent=root)
    
    def test_success_many_files():
        """Test success message with many files"""
        success_msg = "Full Map Processing Complete!\n\n"
        success_msg += f"Successfully processed 8 MAP files:\n"
        success_msg += f"• 019.3AD416-19-F4\n"
        success_msg += f"• 020.3AD416-20-F4\n"
        success_msg += f"• 021.3AD416-21-F4\n"
        success_msg += f"• 022.3AD416-22-F4\n"
        success_msg += f"• 023.3AD416-23-F4\n"
        success_msg += f"• ... and 3 more files\n"
        success_msg += f"\nOutput file: FullMap_8files_YYYYMMDD_HHMMSS.xlsx"
        success_msg += f"\nOutput folder: C:/Users/<USER>/Desktop/map_raw_data/map/test"
        success_msg += f"\n\nProcessing options applied:"
        success_msg += f"\n• Rotation angle: 90°"
        success_msg += f"\n• Filter empty areas: No"
        success_msg += f"\n• Sort bins by quantity: Yes"
        success_msg += f"\n• Configuration file: CP1_program_bin.xlsx"
        
        messagebox.showinfo("Processing Complete", success_msg, parent=root)
    
    def test_processing_failed():
        """Test processing failed message"""
        error_msg = "Full Map Processing Failed!\n\n"
        error_msg += f"Attempted to process 5 MAP files, but some failed.\n\n"
        error_msg += "Common causes of processing failures:\n"
        error_msg += "• Invalid or corrupted MAP file format\n"
        error_msg += "• Unsupported MAP file version\n"
        error_msg += "• Insufficient memory for large files\n"
        error_msg += "• File access permissions issues\n"
        error_msg += "• Missing or invalid configuration file\n\n"
        error_msg += "Please check the console output for detailed error messages\n"
        error_msg += "and verify that all selected files are valid MAP format files."
        
        messagebox.showerror("Processing Failed", error_msg, parent=root)
    
    def test_processing_error():
        """Test processing exception error message"""
        error_msg = "Full Map Processing Error!\n\n"
        error_msg += f"An unexpected error occurred while processing 4 MAP files.\n\n"
        error_msg += f"Error details:\nFileNotFoundError: [Errno 2] No such file or directory: 'output.xlsx'\n\n"
        error_msg += "Troubleshooting steps:\n"
        error_msg += "• Verify all selected files are valid MAP format\n"
        error_msg += "• Check available disk space and memory\n"
        error_msg += "• Ensure output folder has write permissions\n"
        error_msg += "• Try processing fewer files at once\n"
        error_msg += "• Check console output for additional details"
        
        messagebox.showerror("Processing Error", error_msg, parent=root)
    
    def test_no_files_warning():
        """Test no files selected warning"""
        messagebox.showwarning(
            "No Files", 
            "Please select MAP files to process.\n\n"
            "Use 'Add MAP Files...' or 'Add Folder...' to select files first.",
            parent=root
        )
    
    # Create test buttons
    button_frame = ttk.Frame(main_frame)
    button_frame.pack(fill=tk.BOTH, expand=True)
    
    buttons = [
        ("Success - Few Files", test_success_few_files, "Test success with 3 files"),
        ("Success - Many Files", test_success_many_files, "Test success with 8 files"),
        ("Processing Failed", test_processing_failed, "Test processing failure"),
        ("Processing Error", test_processing_error, "Test exception error"),
        ("No Files Warning", test_no_files_warning, "Test no files warning")
    ]
    
    for i, (text, command, tooltip) in enumerate(buttons):
        btn = ttk.Button(button_frame, text=text, command=command, width=20)
        btn.grid(row=i//2, column=i%2, padx=10, pady=10, sticky="ew")
    
    # Configure grid weights
    button_frame.columnconfigure(0, weight=1)
    button_frame.columnconfigure(1, weight=1)
    
    # Add instructions
    instructions = ttk.Label(main_frame, 
                           text="Click the buttons above to test detailed Full Map Tool 'Process All Files' messagebox dialogs.\n"
                                "Notice the comprehensive information including file counts, options, and troubleshooting guidance.",
                           justify=tk.CENTER, font=("Arial", 10))
    instructions.pack(pady=(20, 0))
    
    # Add comparison note
    comparison_note = ttk.Label(main_frame, 
                              text="Before: 'All MAP files processed successfully!'\n"
                                   "After: Detailed processing summary with file counts, options, and output information",
                              justify=tk.CENTER, font=("Arial", 9), foreground="blue")
    comparison_note.pack(pady=(10, 0))
    
    root.mainloop()

if __name__ == "__main__":
    test_detailed_process_all()
