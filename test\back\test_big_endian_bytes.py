#!/usr/bin/env python3
"""
测试Big-Endian字节序列
验证struct.pack('>I', 63)的结果

Author: Yuribytes
Company: Chipone TE development Team
"""

import struct

def test_big_endian_packing():
    """测试Big-Endian打包"""
    print("🔍 测试Big-Endian字节打包")
    print("=" * 40)
    
    # 测试Pass值 (63)
    pass_value = 63
    pass_bytes_be = struct.pack('>I', pass_value)
    pass_bytes_le = struct.pack('<I', pass_value)
    
    print(f"Pass值 (63):")
    print(f"   Big-Endian (>I): {pass_bytes_be} → {pass_bytes_be.hex().upper()}")
    print(f"   字节列表: {list(pass_bytes_be)} → {[hex(b) for b in pass_bytes_be]}")
    print(f"   Little-Endian (<I): {pass_bytes_le} → {pass_bytes_le.hex().upper()}")
    print(f"   字节列表: {list(pass_bytes_le)} → {[hex(b) for b in pass_bytes_le]}")
    
    # 测试Fail值 (59)
    fail_value = 59
    fail_bytes_be = struct.pack('>I', fail_value)
    fail_bytes_le = struct.pack('<I', fail_value)
    
    print(f"\nFail值 (59):")
    print(f"   Big-Endian (>I): {fail_bytes_be} → {fail_bytes_be.hex().upper()}")
    print(f"   字节列表: {list(fail_bytes_be)} → {[hex(b) for b in fail_bytes_be]}")
    print(f"   Little-Endian (<I): {fail_bytes_le} → {fail_bytes_le.hex().upper()}")
    print(f"   字节列表: {list(fail_bytes_le)} → {[hex(b) for b in fail_bytes_le]}")
    
    # 验证预期格式
    print(f"\n🎯 格式验证:")
    expected_pass = "0000003F"
    expected_fail = "0000003B"
    
    actual_pass = pass_bytes_be.hex().upper()
    actual_fail = fail_bytes_be.hex().upper()
    
    print(f"   Pass: 预期 {expected_pass}, 实际 {actual_pass}, 匹配 {'✅' if actual_pass == expected_pass else '❌'}")
    print(f"   Fail: 预期 {expected_fail}, 实际 {actual_fail}, 匹配 {'✅' if actual_fail == expected_fail else '❌'}")
    
    return pass_bytes_be, fail_bytes_be

def test_manual_write():
    """测试手动写入字节"""
    print(f"\n🔧 测试手动写入字节")
    print("=" * 40)
    
    # 创建测试数组
    test_array = bytearray(20)  # 20字节的测试数组
    
    print(f"原始数组: {test_array.hex().upper()}")
    
    # 获取Big-Endian字节
    pass_bytes, fail_bytes = test_big_endian_packing()
    
    # 测试写入Pass字节 (位置0-3)
    for i in range(4):
        test_array[i] = pass_bytes[i]
    
    print(f"写入Pass后: {test_array.hex().upper()}")
    print(f"位置0-3: {test_array[0:4].hex().upper()}")
    
    # 测试写入Fail字节 (位置10-13)
    for i in range(4):
        test_array[10 + i] = fail_bytes[i]
    
    print(f"写入Fail后: {test_array.hex().upper()}")
    print(f"位置10-13: {test_array[10:14].hex().upper()}")
    
    # 验证结果
    pass_result = test_array[0:4].hex().upper()
    fail_result = test_array[10:14].hex().upper()
    
    print(f"\n📊 写入结果验证:")
    print(f"   Pass写入结果: {pass_result} (预期: 0000003F)")
    print(f"   Fail写入结果: {fail_result} (预期: 0000003B)")
    print(f"   Pass正确: {'✅' if pass_result == '0000003F' else '❌'}")
    print(f"   Fail正确: {'✅' if fail_result == '0000003B' else '❌'}")

def main():
    """主测试函数"""
    print("🧪 Big-Endian字节序列测试")
    print("=" * 50)
    
    try:
        # 测试1: Big-Endian打包
        test_big_endian_packing()
        
        # 测试2: 手动写入
        test_manual_write()
        
        print("\n" + "=" * 50)
        print("🎉 测试完成")
        print("如果Big-Endian打包正确，问题可能在NEPES处理器的写入逻辑中")
        
        return True
            
    except Exception as e:
        print(f"❌ 测试过程出错: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
