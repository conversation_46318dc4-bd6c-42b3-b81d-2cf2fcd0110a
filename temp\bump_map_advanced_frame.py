#!/usr/bin/env python3
"""
Bump Map Advanced Tool Frame - Enhanced version with Map Version 2/3/4 support and dynamic configuration
Frame-based version for main application controller

Author: Yuribytes
Company: Chipone TE development Team
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import os
from typing import Dict, List
from nepes_advanced_processor import NEPESAdvancedProcessor


class BumpMapAdvancedFrame:
    """Advanced Bump Map Tool Frame with multi-version support and dynamic configuration"""
    
    def __init__(self, parent, app_controller):
        self.parent = parent
        self.app_controller = app_controller
        self.main_frame = None
        
        # File paths (增强版 - 支持多个Bump Map)
        self.bump_map_files = []  # 存储多个bump map文件路径
        self.bump_map_display = tk.StringVar(value="No bump map files selected")
        self.dummy_map_file_path = tk.StringVar()
        self.output_directory_path = tk.StringVar()  # 改为输出目录路径

        # Test house selection (复用原始功能)
        self.selected_test_house = tk.StringVar(value="")
        self.selected_test_house_display = tk.StringVar(value="No test house selected")

        # Map version detection (复用原始功能)
        self.detected_map_version = tk.IntVar(value=0)
        self.map_version_info = tk.StringVar(value="Map Version: Not detected")

        # Dynamic configuration variables (新功能)
        self.pass_value = tk.IntVar(value=63)  # Default pass value
        self.fail_value = tk.IntVar(value=59)  # Default fail value

        # Status and processing
        self.status_var = tk.StringVar(value="Ready - Select bump and dummy map files")
        self.processing_count = 0
        self.last_memory_usage = 0.0
        self.bump_processor = None

        # Test house configurations (按用户指定的优先级排序)
        priority_test_houses = [
            ("Chipmore", "Chipmore Technologies"),  # 用户指定第1优先级
            ("Unisem", "Unisem Group"),            # 用户指定第2优先级
            ("TongFu", "TongFu Microelectronics"), # 用户指定第3优先级
            ("ChipMOS", "ChipMOS Technologies"),   # 用户指定第4优先级
            ("NEPES", "NEPES Corporation"),        # 用户指定第5优先级
            ("Chipbond", "Chipbond Technology Corporation")  # 用户指定第6优先级
        ]

        other_test_houses = [
            ("ASEN", "ASEN Semiconductors"),
            ("Amkor", "Amkor Technology"),
            ("ASE_Group", "ASE Group (Advanced Semiconductor Engineering)"),
            ("Carsem", "Carsem Semiconductor"),
            ("ChipPAC", "ChipPAC (STATS ChipPAC)"),
            ("Custom", "Custom Test House Format"),
            ("Formosa", "Formosa Advanced Technologies"),
            ("JCET", "Jiangsu Changjiang Electronics Technology"),
            ("KYEC", "King Yuan Electronics"),
            ("Lingsen", "Lingsen Precision Industries"),
            ("Signetics", "Signetics Corporation"),
            ("SPIL", "Siliconware Precision Industries"),
            ("Tianshui", "Tianshui Huatian Technology"),
            ("UTAC", "United Test and Assembly Center")
        ]

        # 合并测试厂列表（优先级在前）
        self.test_houses = {}
        for test_id, test_name in priority_test_houses + other_test_houses:
            self.test_houses[test_id] = test_name
        
        self.create_widgets()
    
    def create_widgets(self):
        """Create and layout all widgets"""
        # Main frame
        self.main_frame = ttk.Frame(self.parent, padding="15")
        
        # Title
        title_label = ttk.Label(self.main_frame, text="🔧 Bump Map Advanced Tool", 
                               font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 20))
        
        # File selection section
        self.create_file_selection_section()

        # Test house selection section (复用原始功能)
        self.create_test_house_section()

        # Map version detection section (复用原始功能)
        self.create_map_version_section()

        # Dynamic configuration section (NEW)
        self.create_configuration_section()

        # Status bar (移到Processing controls之前)
        self.create_status_bar()

        # Processing controls (移到最后，避免遮挡)
        self.create_processing_controls()
        
        # Configure grid weights
        self.main_frame.columnconfigure(1, weight=1)
    
    def create_file_selection_section(self):
        """Create file selection widgets"""
        # File selection frame
        file_frame = ttk.LabelFrame(self.main_frame, text="📁 File Selection", padding="10")
        file_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 15))
        file_frame.columnconfigure(1, weight=1)
        
        # Bump map file
        ttk.Label(file_frame, text="Bump Map File:").grid(row=0, column=0, sticky=tk.W, pady=(0, 5))
        bump_entry = ttk.Entry(file_frame, textvariable=self.bump_map_file_path, width=50)
        bump_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(10, 5), pady=(0, 5))
        ttk.Button(file_frame, text="Browse", 
                  command=self.browse_bump_map_file).grid(row=0, column=2, pady=(0, 5))
        
        # Dummy map file
        ttk.Label(file_frame, text="Dummy Map File:").grid(row=1, column=0, sticky=tk.W, pady=(0, 5))
        dummy_entry = ttk.Entry(file_frame, textvariable=self.dummy_map_file_path, width=50)
        dummy_entry.grid(row=1, column=1, sticky=(tk.W, tk.E), padx=(10, 5), pady=(0, 5))
        ttk.Button(file_frame, text="Browse", 
                  command=self.browse_dummy_map_file).grid(row=1, column=2, pady=(0, 5))
        
        # Output file (optional)
        ttk.Label(file_frame, text="Output File (Optional):").grid(row=2, column=0, sticky=tk.W)
        output_entry = ttk.Entry(file_frame, textvariable=self.output_file_path, width=50)
        output_entry.grid(row=2, column=1, sticky=(tk.W, tk.E), padx=(10, 5))
        ttk.Button(file_frame, text="Browse",
                  command=self.browse_output_file).grid(row=2, column=2)

    def create_test_house_section(self):
        """Create test house selection section (复用原始功能)"""
        test_house_frame = ttk.LabelFrame(self.main_frame, text="🏭 Test House Selection", padding="10")
        test_house_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 15))
        test_house_frame.columnconfigure(0, weight=3)
        test_house_frame.columnconfigure(1, weight=1)

        # Instructions
        instruction_label = ttk.Label(test_house_frame,
                                     text="Select the test house format for proper bump and dummy map alignment:",
                                     font=("Arial", 9))
        instruction_label.grid(row=0, column=0, columnspan=2, sticky=tk.W, pady=(0, 10))

        # Create left and right sections
        left_frame = ttk.Frame(test_house_frame)
        left_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 15))

        right_frame = ttk.Frame(test_house_frame)
        right_frame.grid(row=1, column=1, sticky=(tk.W, tk.E, tk.N, tk.S))

        # Test house selection with scrollable frame (left side)
        self.create_test_house_selection(left_frame)

        # Selected test house display (right side)
        self.create_selection_display(right_frame)

    def create_map_version_section(self):
        """Create map version detection section (复用原始功能)"""
        version_frame = ttk.LabelFrame(self.main_frame, text="🔍 Map Version (Auto-Detected)", padding="10")
        version_frame.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 15))

        version_info_label = ttk.Label(version_frame, textvariable=self.map_version_info,
                                      font=("Arial", 10, "bold"), foreground="blue")
        version_info_label.grid(row=0, column=0, sticky=tk.W)

        version_note = ttk.Label(version_frame,
                                text="Map version is automatically detected from dummy map file",
                                font=("Arial", 8), foreground="gray")
        version_note.grid(row=1, column=0, sticky=tk.W, pady=(5, 0))

    def create_configuration_section(self):
        """Create dynamic configuration section (NEW FEATURE)"""
        # Configuration frame
        config_frame = ttk.LabelFrame(self.main_frame, text="⚙️ Dynamic Configuration", padding="10")
        config_frame.grid(row=4, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 15))
        config_frame.columnconfigure(1, weight=1)
        
        # Pass value configuration
        ttk.Label(config_frame, text="Pass Value (for '00' positions):").grid(row=0, column=0, sticky=tk.W, pady=(0, 5))
        pass_frame = ttk.Frame(config_frame)
        pass_frame.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(10, 0), pady=(0, 5))
        
        pass_spinbox = ttk.Spinbox(pass_frame, from_=0, to=255, width=10, textvariable=self.pass_value)
        pass_spinbox.pack(side=tk.LEFT)
        
        # Pass value info
        pass_info = ttk.Label(pass_frame, text="(Default: 63, Range: 0-255)", foreground="gray")
        pass_info.pack(side=tk.LEFT, padx=(10, 0))
        
        # Fail value configuration
        ttk.Label(config_frame, text="Fail Value (for 'XX' positions):").grid(row=1, column=0, sticky=tk.W)
        fail_frame = ttk.Frame(config_frame)
        fail_frame.grid(row=1, column=1, sticky=(tk.W, tk.E), padx=(10, 0))
        
        fail_spinbox = ttk.Spinbox(fail_frame, from_=0, to=255, width=10, textvariable=self.fail_value)
        fail_spinbox.pack(side=tk.LEFT)
        
        # Fail value info
        fail_info = ttk.Label(fail_frame, text="(Default: 59, Range: 0-255)", foreground="gray")
        fail_info.pack(side=tk.LEFT, padx=(10, 0))
        
        # Format preview
        preview_frame = ttk.Frame(config_frame)
        preview_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(10, 0))
        
        ttk.Label(preview_frame, text="Format Preview:", font=("Arial", 9, "bold")).pack(anchor=tk.W)
        
        self.format_preview_label = ttk.Label(preview_frame, text="", foreground="blue", font=("Courier", 9))
        self.format_preview_label.pack(anchor=tk.W, pady=(5, 0))
        
        # Update preview when values change
        self.pass_value.trace('w', self.update_format_preview)
        self.fail_value.trace('w', self.update_format_preview)
        
        # Initial preview update
        self.update_format_preview()
    
    def create_processing_controls(self):
        """Create processing control buttons"""
        # Processing controls frame
        controls_frame = ttk.Frame(self.main_frame)
        controls_frame.grid(row=6, column=0, columnspan=2, pady=(0, 15))
        
        # Buttons layout: Process Maps | Clear Memory | Back | Exit
        ttk.Button(controls_frame, text="🚀 Process Maps",
                  command=self.process_maps, style="Accent.TButton").pack(side=tk.LEFT, padx=(0, 15))
        ttk.Button(controls_frame, text="🧹 Clear Memory",
                  command=self.clear_memory).pack(side=tk.LEFT, padx=(0, 15))
        ttk.Button(controls_frame, text="← Back",
                  command=self.return_to_selector).pack(side=tk.LEFT, padx=(0, 15))
        ttk.Button(controls_frame, text="Exit",
                  command=self.exit_application).pack(side=tk.LEFT)
    
    def create_status_bar(self):
        """Create status bar"""
        status_frame = ttk.Frame(self.main_frame)
        status_frame.grid(row=5, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(10, 0))
        status_frame.columnconfigure(0, weight=1)
        
        status_label = ttk.Label(status_frame, textvariable=self.status_var, relief=tk.SUNKEN, padding="5")
        status_label.grid(row=0, column=0, sticky=(tk.W, tk.E))
    
    def update_format_preview(self, *args):
        """Update format preview based on current values"""
        try:
            pass_val = self.pass_value.get()
            fail_val = self.fail_value.get()
            
            # Show both Map Version formats
            preview_text = (f"Map Version 4 (Big-Endian): Pass=0000{pass_val:04X}, Fail=0000{fail_val:04X}\n"
                           f"Map Version 2/3 (Special): Pass=00{pass_val:02X}0000, Fail=00{fail_val:02X}0000")
            
            self.format_preview_label.config(text=preview_text)
        except:
            self.format_preview_label.config(text="Invalid values")
    
    def browse_bump_map_file(self):
        """Browse for bump map file"""
        filename = filedialog.askopenfilename(
            title="Select Bump Map File",
            filetypes=[("All files", "*.*"), ("Map files", "*.map"), ("Text files", "*.txt")]
        )
        if filename:
            self.bump_map_file_path.set(filename)
            self.status_var.set(f"Bump map selected: {os.path.basename(filename)}")
    
    def browse_dummy_map_file(self):
        """Browse for dummy map file and auto-detect map version (All files优先)"""
        filename = filedialog.askopenfilename(
            title="Select Dummy Map File",
            filetypes=[
                ("All supported files", "*.*"),  # All files优先，便于选择
                ("TSK files", "*.tsk"),
                ("MAP files", "*.map"),
                ("Text files", "*.txt")
            ]
        )
        if filename:
            self.dummy_map_file_path.set(filename)

            # Auto-detect map version (复用原始功能)
            self.auto_detect_map_version(filename)

            # Auto-generate output filename (复用原始功能)
            self.auto_generate_output_path(filename)

            self.status_var.set(f"Dummy map file selected: {os.path.basename(filename)}")
    
    def browse_output_file(self):
        """Browse for output file location (复用原始功能，修正格式优先级)"""
        # Get the dummy map file extension to maintain format
        dummy_file = self.dummy_map_file_path.get()
        if dummy_file:
            _, ext = os.path.splitext(dummy_file)
            default_ext = ext if ext else ".map"
        else:
            default_ext = ".map"

        # 按照要求调整格式优先级：All files → .map → .tsk
        filename = filedialog.asksaveasfilename(
            title="Save Output File As",
            defaultextension=default_ext,
            filetypes=[
                ("All files", "*.*"),  # All files优先
                ("MAP files", "*.map"),
                ("TSK files", "*.tsk")
            ]
        )
        if filename:
            self.output_file_path.set(filename)
            self.status_var.set(f"Output file set: {os.path.basename(filename)}")

    def auto_detect_map_version(self, dummy_file_path):
        """Auto-detect map version from dummy map file (完全复用原始功能)"""
        try:
            from tsk_map_processor import TSKMapProcessor

            processor = TSKMapProcessor()
            success = processor.read_file(dummy_file_path)

            if success:
                header_success = processor.parse_file_header()
                if header_success:
                    version = processor.May_version  # 使用正确的属性名
                    self.detected_map_version.set(version)

                    version_text = f"Map Version: {version}"
                    if version == 4:
                        version_text += " (Enhanced - NEPES Compatible)"
                    elif version in [2, 3]:
                        version_text += " (Standard)"

                    self.map_version_info.set(version_text)
                    print(f"✅ Auto-detected Map Version: {version}")
                    return version
                else:
                    self.map_version_info.set("Map Version: Detection failed (header parse error)")
            else:
                self.map_version_info.set("Map Version: Detection failed (file read error)")

        except Exception as e:
            self.map_version_info.set(f"Map Version: Detection failed ({str(e)})")
            print(f"❌ Map version detection error: {e}")

        return None

    def auto_generate_output_path(self, dummy_file_path):
        """Auto-generate output path based on bump and dummy map files (复用原始功能)"""
        try:
            bump_file = self.bump_map_file_path.get()
            if not bump_file:
                return

            # 获取bump文件的basename（不使用splitext，因为.09等不是真正的扩展名）
            bump_basename = os.path.basename(bump_file)

            # 将点号替换为连字符 (例如: D97127.09 -> D97127-09)
            output_base = bump_basename.replace('.', '-')

            # Create output filename: 只使用bump map名字，格式为lot-01（不包含扩展名）
            output_filename = output_base

            # Use the same directory as the dummy file
            output_dir = os.path.dirname(dummy_file_path)
            output_path = os.path.join(output_dir, output_filename)

            self.output_file_path.set(output_path)
            print(f"✅ Auto-generated output path: {output_filename}")

        except Exception as e:
            print(f"❌ Output path generation error: {e}")

    def process_maps(self):
        """Process bump map and dummy map with advanced features"""
        try:
            # Validate inputs
            if not self.bump_map_file_path.get():
                messagebox.showerror("Error", "Please select a bump map file")
                return

            if not self.dummy_map_file_path.get():
                messagebox.showerror("Error", "Please select a dummy map file")
                return

            if not os.path.exists(self.bump_map_file_path.get()):
                messagebox.showerror("Error", "Bump map file does not exist")
                return

            if not os.path.exists(self.dummy_map_file_path.get()):
                messagebox.showerror("Error", "Dummy map file does not exist")
                return

            # Validate test house selection (复用原始功能)
            if not self.selected_test_house.get():
                messagebox.showerror("Error", "Please select a test house format")
                return

            # Validate configuration values
            try:
                pass_val = self.pass_value.get()
                fail_val = self.fail_value.get()

                if not (0 <= pass_val <= 255):
                    messagebox.showerror("Error", "Pass value must be between 0 and 255")
                    return

                if not (0 <= fail_val <= 255):
                    messagebox.showerror("Error", "Fail value must be between 0 and 255")
                    return

            except tk.TclError:
                messagebox.showerror("Error", "Please enter valid numeric values")
                return

            # Update status
            self.status_var.set("Processing maps... Please wait")
            self.parent.update()

            # Get auto-detected map version (复用原始功能)
            map_version = self.detected_map_version.get()
            if map_version == 0:
                messagebox.showerror("Error", "Map version not detected. Please select a valid dummy map file.")
                return

            # Check if NEPES test house is selected and use enhanced processor (复用原始功能)
            selected_test_house = self.selected_test_house.get()

            if selected_test_house == "NEPES":
                self.status_var.set(f"Using NEPES Advanced Processor (Map Version {map_version})...")

                # Create processor
                self.bump_processor = NEPESAdvancedProcessor()

                # Get output path
                output_path = self.output_file_path.get() if self.output_file_path.get() else None

                # Process with advanced features
                success = self.bump_processor.process_advanced_nepes(
                    self.bump_map_file_path.get(),
                    self.dummy_map_file_path.get(),
                    output_path,
                    pass_val,
                    fail_val
                )
            else:
                # For other test houses, show info message (复用原始功能)
                selected_house_name = self.test_houses.get(selected_test_house, selected_test_house)
                info_msg = (
                    f"Selected Configuration:\n"
                    f"• Test House: {selected_house_name}\n"
                    f"• Map Version: {map_version} (Auto-detected)\n"
                    f"• Pass Value: {pass_val}\n"
                    f"• Fail Value: {fail_val}\n\n"
                    f"ℹ️ Processing Logic:\n"
                    f"Advanced processing is currently implemented for NEPES Corporation only.\n\n"
                    f"For {selected_house_name}:\n"
                    f"• Processing logic is not yet implemented\n"
                    f"• Will be added in future updates\n"
                    f"• Current focus is on NEPES format optimization\n\n"
                    f"Please select 'NEPES Corporation' to use the advanced processing features."
                )

                messagebox.showinfo("Processing Info", info_msg)
                self.status_var.set(f"Ready - {selected_house_name} selected (processing not implemented)")
                return

            success = True  # For NEPES processing

            if success:
                # Get final output path (might be auto-generated)
                if not output_path:
                    output_path = self.bump_processor.generate_output_filename(
                        self.bump_map_file_path.get(),
                        self.dummy_map_file_path.get()
                    )

                # Update processing count
                self.processing_count += 1

                # Show success message
                stats = self.bump_processor.get_processing_stats()
                success_msg = (f"✅ Processing completed successfully!\n\n"
                              f"📊 Summary:\n"
                              f"• Map Version: {stats['map_version']}\n"
                              f"• Pass Value: {stats['pass_value']}\n"
                              f"• Fail Value: {stats['fail_value']}\n"
                              f"• Pass Positions: {stats['processing_stats']['pass_positions']}\n"
                              f"• Fail Positions: {stats['processing_stats']['fail_positions']}\n"
                              f"• Output: {os.path.basename(output_path)}")

                messagebox.showinfo("Success", success_msg)
                self.status_var.set(f"Processing completed - Output: {os.path.basename(output_path)}")

            else:
                messagebox.showerror("Error", "Processing failed. Check console for details.")
                self.status_var.set("Processing failed - Check inputs and try again")

        except Exception as e:
            error_msg = f"Processing error: {str(e)}"
            messagebox.showerror("Error", error_msg)
            self.status_var.set("Processing failed - Check inputs and try again")
            print(f"Bump Map Advanced Processing Error: {e}")

    def clear_memory(self):
        """Clear memory and reset state"""
        try:
            memory_freed = 0.0
            processor_count = 0

            # Clear bump processor
            if self.bump_processor:
                if hasattr(self.bump_processor, 'get_memory_usage_mb'):
                    memory_freed += self.bump_processor.get_memory_usage_mb()
                if hasattr(self.bump_processor, 'clear_memory'):
                    self.bump_processor.clear_memory()
                processor_count = 1
                self.bump_processor = None

            # Reset counters
            self.processing_count = 0
            self.last_memory_usage = 0.0

            # Provide feedback
            if memory_freed > 0:
                print(f"Bump Map Advanced Tool - Total memory cleared: ~{memory_freed:.1f} MB from {processor_count} processors")
                self.status_var.set(f"Memory cleared: {memory_freed:.1f} MB freed from {processor_count} processors")
            else:
                print("Bump Map Advanced Tool - Memory cleared (no active processors)")
                self.status_var.set("Memory cleared - no active processors")

        except Exception as e:
            print(f"Warning: Error during Bump Map Advanced memory cleanup: {e}")
            self.status_var.set("Memory cleanup completed with warnings")

    def return_to_selector(self):
        """Return to tool selector"""
        try:
            # Check for unsaved work
            has_unsaved = (self.bump_map_file_path.get() or
                          self.dummy_map_file_path.get() or
                          self.processing_count > 0)

            # Use app controller to return with confirmation
            if self.app_controller and hasattr(self.app_controller, 'return_to_selector_with_confirmation'):
                self.app_controller.return_to_selector_with_confirmation("Bump Map Advanced Tool", has_unsaved)
            else:
                # Fallback for standalone mode
                if has_unsaved:
                    result = messagebox.askyesno(
                        "Return to Selector",
                        "You have unsaved work. Clear memory before returning?",
                        icon='question'
                    )
                    if result:
                        self.clear_memory()

                print("Returning to tool selector...")

        except Exception as e:
            print(f"Error returning to selector: {e}")

    def exit_application(self):
        """Exit application with cleanup"""
        try:
            # Get memory info before clearing
            memory_freed = 0.0
            processor_count = 0

            # Calculate memory usage from bump processor
            if self.bump_processor and hasattr(self.bump_processor, 'get_memory_usage_mb'):
                memory_freed += self.bump_processor.get_memory_usage_mb()
                processor_count = 1

            # Clear all memory before exit
            self.clear_memory()
            print("Bump Map Advanced Tool - Application memory cleared before exit")

            # Show cleanup popup and exit
            self.show_memory_cleanup_popup(memory_freed, processor_count)
            self.app_controller.root.after(2100, self.app_controller.root.quit)

        except Exception as e:
            print(f"Warning: Error during Bump Map Advanced exit cleanup: {e}")
            self.app_controller.root.quit()

    def show_memory_cleanup_popup(self, memory_freed: float, processor_count: int):
        """Show memory cleanup popup"""
        try:
            # Create popup window
            popup = tk.Toplevel(self.app_controller.root)
            popup.title("Memory Cleanup")
            popup.geometry("350x120")
            popup.resizable(False, False)

            # Center popup on parent window
            popup.transient(self.app_controller.root)
            popup.grab_set()

            # Center popup
            x = self.app_controller.root.winfo_x() + (self.app_controller.root.winfo_width() // 2) - 175
            y = self.app_controller.root.winfo_y() + (self.app_controller.root.winfo_height() // 2) - 60
            popup.geometry(f"350x120+{x}+{y}")

            # Create content
            main_frame = ttk.Frame(popup, padding="20")
            main_frame.pack(fill=tk.BOTH, expand=True)

            # Icon and message
            if memory_freed > 0:
                message = f"✅ Memory Cleanup Complete\n\n{memory_freed:.1f} MB freed from {processor_count} processors"
            elif processor_count > 0:
                message = f"✅ Memory Cleanup Complete\n\nProcessors cleared (no memory to free)"
            else:
                message = "✅ Application Closing\n\nThank you for using Bump Map Advanced Tool!"

            ttk.Label(main_frame, text=message, justify=tk.CENTER,
                     font=("Arial", 10)).pack(expand=True)

            # Auto-close after 2 seconds
            popup.after(2000, popup.destroy)

            # Allow manual close with Escape key
            popup.bind('<Escape>', lambda e: popup.destroy())
            popup.focus_set()

        except Exception as e:
            print(f"Warning: Error showing Bump Map Advanced cleanup popup: {e}")

    def show(self):
        """Show the Bump Map Advanced Tool frame"""
        if self.main_frame:
            self.main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

    def hide(self):
        """Hide the Bump Map Advanced Tool frame"""
        if self.main_frame:
            self.main_frame.grid_remove()

    def create_test_house_selection(self, parent_frame):
        """Create scrollable test house selection area (完全复用原始功能)"""
        # Create canvas and scrollbar for scrollable area
        canvas_frame = ttk.Frame(parent_frame)
        canvas_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        canvas_frame.columnconfigure(0, weight=1)
        canvas_frame.rowconfigure(0, weight=1)

        # 增加canvas宽度以容纳两栏内容
        canvas = tk.Canvas(canvas_frame, height=150, width=450)  # 明确设置宽度
        scrollbar = ttk.Scrollbar(canvas_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # Add test house radio buttons - 优化两栏布局
        row = 0
        col = 0
        for test_house_id, test_house_name in self.test_houses.items():
            radio_button = ttk.Radiobutton(scrollable_frame,
                                          text=test_house_name,
                                          variable=self.selected_test_house,
                                          value=test_house_id)
            radio_button.grid(row=row, column=col, sticky=tk.W, padx=5, pady=2)

            col += 1
            if col >= 2:  # 2 columns layout
                col = 0
                row += 1

        # 确保scrollable_frame有足够的列宽度
        scrollable_frame.columnconfigure(0, weight=1, minsize=200)
        scrollable_frame.columnconfigure(1, weight=1, minsize=200)

        # Bind mousewheel to entire test house selection area
        def _on_mousewheel(event):
            canvas.yview_scroll(int(-1*(event.delta/120)), "units")

        # Bind mousewheel to the entire canvas frame and all its children
        def bind_mousewheel_recursive(widget):
            widget.bind("<MouseWheel>", _on_mousewheel)
            for child in widget.winfo_children():
                bind_mousewheel_recursive(child)

        bind_mousewheel_recursive(canvas_frame)

        # 绑定选择变化事件
        self.selected_test_house.trace('w', self.on_test_house_selection_changed)

    def create_selection_display(self, parent_frame):
        """创建右侧选中测试厂显示区域 (完全复用原始功能)"""
        # 选中状态显示框 - 减少padding
        display_frame = ttk.LabelFrame(parent_frame, text="Selected", padding="5")
        display_frame.pack(fill=tk.BOTH, expand=True)

        # 显示选中的测试厂 - 更紧凑的布局
        self.selection_label = ttk.Label(display_frame,
                                        textvariable=self.selected_test_house_display,
                                        font=("Arial", 9, "bold"),  # 稍小字体
                                        foreground="blue",
                                        wraplength=150,  # 减少换行宽度
                                        justify=tk.CENTER)
        self.selection_label.pack(pady=(5, 5))

        # 添加简化的说明文字
        info_label = ttk.Label(display_frame,
                              text="Format for alignment",
                              font=("Arial", 7),  # 更小字体
                              foreground="gray",
                              justify=tk.CENTER)
        info_label.pack()

    def on_test_house_selection_changed(self, *args):
        """测试厂选择变化时的回调函数 (完全复用原始功能)"""
        selected_id = self.selected_test_house.get()
        if selected_id and selected_id in self.test_houses:
            selected_name = self.test_houses[selected_id]
            self.selected_test_house_display.set(f"✓ {selected_name}")
            self.status_var.set(f"Test house selected: {selected_name}")
        else:
            self.selected_test_house_display.set("No test house selected")
            self.status_var.set("Ready - Select bump and dummy map files")

    def return_to_selector(self):
        """Return to tool selector with automatic memory cleanup (完全复用原始功能)"""
        try:
            # Get memory info before clearing (for accurate reporting)
            memory_before_clear = 0.0
            processor_count = 1 if self.bump_processor else 0

            if self.bump_processor and hasattr(self.bump_processor, 'get_memory_usage_mb'):
                memory_before_clear = self.bump_processor.get_memory_usage_mb()

            # Clear memory automatically
            self.clear_memory()
            print(f"Bump Map Advanced Tool - Memory cleared when returning to main menu: {memory_before_clear:.1f} MB freed")

            # Verify memory was actually cleared
            memory_after_clear = 0.0
            if self.bump_processor and hasattr(self.bump_processor, 'get_memory_usage_mb'):
                memory_after_clear = self.bump_processor.get_memory_usage_mb()

            # Calculate actual memory freed
            actual_memory_freed = memory_before_clear - memory_after_clear
            print(f"Bump Map Advanced Tool - Actual memory freed: {actual_memory_freed:.1f} MB (before: {memory_before_clear:.1f} MB, after: {memory_after_clear:.1f} MB)")

            # Clear state when returning
            self.bump_map_file_path.set("")
            self.dummy_map_file_path.set("")
            self.output_file_path.set("")
            self.selected_test_house.set("")
            self.detected_map_version.set(0)
            self.map_version_info.set("Map Version: Not detected")
            self.status_var.set("Ready - Select bump and dummy map files")

            # Show 1-second cleanup popup with actual freed memory
            self.show_return_cleanup_popup(actual_memory_freed, processor_count)

            # Return to selector after popup
            self.app_controller.root.after(1100, self.app_controller.show_tool_selector)

        except Exception as e:
            print(f"Warning: Error during Bump Map Advanced return cleanup: {e}")
            # Still return to selector even if cleanup fails
            self.app_controller.show_tool_selector()

    def exit_application(self):
        """Exit application with cleanup (完全复用原始功能)"""
        try:
            # Get memory info before clearing (similar to Full Map Tool)
            memory_freed = 0.0
            processor_count = 0

            # Calculate memory usage from bump processor
            if self.bump_processor and hasattr(self.bump_processor, 'get_memory_usage_mb'):
                memory_freed += self.bump_processor.get_memory_usage_mb()
                processor_count = 1

            # Clear all memory before exit
            self.clear_memory()
            print("Bump Map Advanced Tool - Application memory cleared before exit")

            # Always show cleanup popup when exiting (user feedback) - matching Full Map Tool
            self.show_memory_cleanup_popup(memory_freed, processor_count)
            # Wait a moment for popup to show before quitting
            self.app_controller.root.after(2100, self.app_controller.root.quit)

        except Exception as e:
            print(f"Warning: Error during Bump Map Advanced exit cleanup: {e}")
            self.app_controller.root.quit()

    def show_return_cleanup_popup(self, memory_freed: float, processor_count: int):
        """Show 1-second cleanup popup when returning to main menu (完全复用原始功能)"""
        try:
            # Create popup window
            popup = tk.Toplevel(self.app_controller.root)
            popup.title("Memory Cleanup")
            popup.geometry("350x120")
            popup.resizable(False, False)

            # Center popup on parent window
            popup.transient(self.app_controller.root)
            popup.grab_set()

            # Center popup
            x = self.app_controller.root.winfo_x() + (self.app_controller.root.winfo_width() // 2) - 175
            y = self.app_controller.root.winfo_y() + (self.app_controller.root.winfo_height() // 2) - 60
            popup.geometry(f"350x120+{x}+{y}")

            # Create content
            main_frame = ttk.Frame(popup, padding="20")
            main_frame.pack(fill=tk.BOTH, expand=True)

            # Icon and message
            if memory_freed > 0:
                message = f"🧹 Returning to Main Menu\n\n{memory_freed:.1f} MB memory freed"
            elif processor_count > 0:
                message = f"🧹 Returning to Main Menu\n\nProcessors cleared"
            else:
                message = "🧹 Returning to Main Menu\n\nReady for next operation"

            ttk.Label(main_frame, text=message, justify=tk.CENTER,
                     font=("Arial", 10)).pack(expand=True)

            # Auto-close after 1 second
            popup.after(1000, popup.destroy)

            # Allow manual close with Escape key
            popup.bind('<Escape>', lambda e: popup.destroy())
            popup.focus_set()

        except Exception as e:
            print(f"Warning: Error showing Bump Map Advanced return cleanup popup: {e}")

    def show_memory_cleanup_popup(self, memory_freed: float, processor_count: int):
        """Show memory cleanup popup (完全复用原始功能)"""
        try:
            # Create popup window
            popup = tk.Toplevel(self.app_controller.root)
            popup.title("Memory Cleanup")
            popup.geometry("350x120")
            popup.resizable(False, False)

            # Center popup on parent window
            popup.transient(self.app_controller.root)
            popup.grab_set()

            # Center popup
            x = self.app_controller.root.winfo_x() + (self.app_controller.root.winfo_width() // 2) - 175
            y = self.app_controller.root.winfo_y() + (self.app_controller.root.winfo_height() // 2) - 60
            popup.geometry(f"350x120+{x}+{y}")

            # Create content
            main_frame = ttk.Frame(popup, padding="20")
            main_frame.pack(fill=tk.BOTH, expand=True)

            # Icon and message
            if memory_freed > 0:
                message = f"✅ Memory Cleanup Complete\n\n{memory_freed:.1f} MB freed from {processor_count} processors"
            elif processor_count > 0:
                message = f"✅ Memory Cleanup Complete\n\nProcessors cleared (no memory to free)"
            else:
                message = "✅ Application Closing\n\nThank you for using Bump Map Advanced Tool!"

            ttk.Label(main_frame, text=message, justify=tk.CENTER,
                     font=("Arial", 10)).pack(expand=True)

            # Auto-close after 2 seconds
            popup.after(2000, popup.destroy)

            # Allow manual close with Escape key
            popup.bind('<Escape>', lambda e: popup.destroy())
            popup.focus_set()

        except Exception as e:
            print(f"Warning: Error showing Bump Map Advanced cleanup popup: {e}")


def main():
    """Test function for standalone running"""
    root = tk.Tk()
    root.title("Bump Map Advanced Tool Test")
    root.geometry("900x700")

    # Mock app controller for testing
    class MockController:
        def __init__(self, root):
            self.root = root
        def return_to_selector_with_confirmation(self, tool_name, has_unsaved):
            print(f"Would return to selector from {tool_name}, unsaved: {has_unsaved}")
            return True

    controller = MockController(root)
    advanced_tool = BumpMapAdvancedFrame(root, controller)
    advanced_tool.show()

    root.mainloop()


if __name__ == "__main__":
    main()
