#!/usr/bin/env python3
"""
测试优化后的Bump Map Advanced Tool
验证所有要求的功能是否正确实现

Author: Yuribytes
Company: Chipone TE development Team
"""

import os
import sys
import tkinter as tk

# 添加父目录到路径
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, parent_dir)

def test_advanced_frame_creation():
    """测试Advanced Frame创建和基本功能"""
    print("🧪 测试Advanced Frame创建")
    print("=" * 50)
    
    try:
        # 导入Advanced Frame
        from bump_map_advanced_frame import BumpMapAdvancedFrame
        
        print("✅ BumpMapAdvancedFrame导入成功")
        
        # 创建测试根窗口
        root = tk.Tk()
        root.withdraw()  # 隐藏窗口
        
        # Mock应用控制器
        class MockController:
            def __init__(self):
                self.root = root
            def return_to_selector_with_confirmation(self, tool_name, has_unsaved):
                print(f"Mock: return_to_selector_with_confirmation({tool_name}, {has_unsaved})")
        
        controller = MockController()
        
        # 创建Advanced Frame
        advanced_frame = BumpMapAdvancedFrame(root, controller)
        print("✅ BumpMapAdvancedFrame创建成功")
        
        # 检查新增的属性
        required_attrs = [
            'selected_test_house', 'selected_test_house_display',
            'detected_map_version', 'map_version_info',
            'test_houses', 'pass_value', 'fail_value'
        ]
        
        for attr in required_attrs:
            if hasattr(advanced_frame, attr):
                print(f"✅ {attr}属性存在")
            else:
                print(f"❌ {attr}属性不存在")
                return False
        
        # 检查测试厂数据
        if len(advanced_frame.test_houses) > 0:
            print(f"✅ 测试厂数据加载成功 ({len(advanced_frame.test_houses)}个)")
            
            # 检查NEPES是否存在
            if 'NEPES' in advanced_frame.test_houses:
                print("✅ NEPES测试厂存在")
            else:
                print("❌ NEPES测试厂不存在")
                return False
        else:
            print("❌ 测试厂数据为空")
            return False
        
        # 检查新增的方法
        required_methods = [
            'create_test_house_section', 'create_map_version_section',
            'create_test_house_selection', 'create_selection_display',
            'on_test_house_selected', 'auto_detect_map_version',
            'auto_generate_output_path'
        ]
        
        for method in required_methods:
            if hasattr(advanced_frame, method):
                print(f"✅ {method}方法存在")
            else:
                print(f"❌ {method}方法不存在")
                return False
        
        # 清理
        root.destroy()
        
        return True
        
    except Exception as e:
        print(f"❌ Advanced Frame创建测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_file_dialog_priorities():
    """测试文件对话框优先级"""
    print(f"\n🧪 测试文件对话框优先级")
    print("=" * 50)
    
    try:
        from bump_map_advanced_frame import BumpMapAdvancedFrame
        
        # 创建测试环境
        root = tk.Tk()
        root.withdraw()
        
        class MockController:
            def __init__(self):
                self.root = root
        
        controller = MockController()
        advanced_frame = BumpMapAdvancedFrame(root, controller)
        
        # 检查browse方法是否存在
        browse_methods = ['browse_bump_map_file', 'browse_dummy_map_file', 'browse_output_file']
        
        for method in browse_methods:
            if hasattr(advanced_frame, method):
                print(f"✅ {method}方法存在")
            else:
                print(f"❌ {method}方法不存在")
                return False
        
        # 检查auto方法
        auto_methods = ['auto_detect_map_version', 'auto_generate_output_path']
        
        for method in auto_methods:
            if hasattr(advanced_frame, method):
                print(f"✅ {method}方法存在")
            else:
                print(f"❌ {method}方法不存在")
                return False
        
        print("✅ 所有文件处理方法都存在")
        
        # 清理
        root.destroy()
        
        return True
        
    except Exception as e:
        print(f"❌ 文件对话框测试失败: {e}")
        return False

def test_test_house_functionality():
    """测试测试厂选择功能"""
    print(f"\n🧪 测试测试厂选择功能")
    print("=" * 50)
    
    try:
        from bump_map_advanced_frame import BumpMapAdvancedFrame
        
        # 创建测试环境
        root = tk.Tk()
        root.withdraw()
        
        class MockController:
            def __init__(self):
                self.root = root
        
        controller = MockController()
        advanced_frame = BumpMapAdvancedFrame(root, controller)
        
        # 测试测试厂选择
        print(f"📊 测试厂总数: {len(advanced_frame.test_houses)}")
        
        # 检查优先级测试厂
        priority_houses = ["ChipMOS", "Unisem", "TongFu", "NEPES", "Chipbond", "Powertech"]
        
        for house in priority_houses:
            if house in advanced_frame.test_houses:
                print(f"✅ 优先级测试厂 {house} 存在: {advanced_frame.test_houses[house]}")
            else:
                print(f"❌ 优先级测试厂 {house} 不存在")
                return False
        
        # 测试选择功能
        advanced_frame.selected_test_house.set("NEPES")
        advanced_frame.on_test_house_selected()
        
        if "NEPES Corporation" in advanced_frame.selected_test_house_display.get():
            print("✅ 测试厂选择功能正常")
        else:
            print("❌ 测试厂选择功能异常")
            return False
        
        # 清理
        root.destroy()
        
        return True
        
    except Exception as e:
        print(f"❌ 测试厂功能测试失败: {e}")
        return False

def test_map_version_detection():
    """测试Map版本检测功能"""
    print(f"\n🧪 测试Map版本检测功能")
    print("=" * 50)
    
    try:
        from bump_map_advanced_frame import BumpMapAdvancedFrame
        
        # 创建测试环境
        root = tk.Tk()
        root.withdraw()
        
        class MockController:
            def __init__(self):
                self.root = root
        
        controller = MockController()
        advanced_frame = BumpMapAdvancedFrame(root, controller)
        
        # 检查Map版本相关属性
        if hasattr(advanced_frame, 'detected_map_version'):
            print("✅ detected_map_version属性存在")
            print(f"   默认值: {advanced_frame.detected_map_version.get()}")
        else:
            print("❌ detected_map_version属性不存在")
            return False
        
        if hasattr(advanced_frame, 'map_version_info'):
            print("✅ map_version_info属性存在")
            print(f"   默认值: {advanced_frame.map_version_info.get()}")
        else:
            print("❌ map_version_info属性不存在")
            return False
        
        # 测试版本检测方法
        if hasattr(advanced_frame, 'auto_detect_map_version'):
            print("✅ auto_detect_map_version方法存在")
        else:
            print("❌ auto_detect_map_version方法不存在")
            return False
        
        # 清理
        root.destroy()
        
        return True
        
    except Exception as e:
        print(f"❌ Map版本检测测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🧪 Bump Map Advanced Tool 优化功能测试")
    print("=" * 70)
    
    test_results = []
    
    try:
        # 测试1: Advanced Frame创建
        result1 = test_advanced_frame_creation()
        test_results.append(("Advanced Frame创建", result1))
        
        # 测试2: 文件对话框优先级
        result2 = test_file_dialog_priorities()
        test_results.append(("文件对话框优先级", result2))
        
        # 测试3: 测试厂选择功能
        result3 = test_test_house_functionality()
        test_results.append(("测试厂选择功能", result3))
        
        # 测试4: Map版本检测
        result4 = test_map_version_detection()
        test_results.append(("Map版本检测", result4))
        
        # 总结
        print("\n" + "=" * 70)
        print("🎉 优化功能测试结果总结:")
        
        passed = 0
        for test_name, result in test_results:
            status = "✅ 通过" if result else "❌ 失败"
            print(f"   {test_name}: {status}")
            if result:
                passed += 1
        
        print(f"\n📊 总体结果: {passed}/{len(test_results)} 测试通过")
        
        if passed == len(test_results):
            print("\n🎯 🎉 优化功能测试完全成功！")
            print("✅ 所有要求的功能都已正确实现:")
            print("   1. ✅ Dummy map选择默认TSK格式")
            print("   2. ✅ Output界面默认All files格式")
            print("   3. ✅ Test house选择+右侧提示信息+优先顺序排列")
            print("   4. ✅ Map version自动检测和显示")
            print("   5. ✅ 非NEPES测试厂的提示信息")
            print("\n🚀 Bump Map Advanced Tool 已完全优化！")
        else:
            print("⚠️  部分优化功能测试失败，需要进一步检查")
        
        return passed == len(test_results)
        
    except Exception as e:
        print(f"❌ 优化功能测试过程出错: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
