#!/usr/bin/env python3
"""
对比TSK处理器和NEPES处理器的逻辑
找出1个字节偏移的根本原因

Author: Yuribytes
Company: Chipone TE development Team
"""

import os
import sys

# 添加父目录到路径
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, parent_dir)

from tsk_map_processor import TSKMapProcessor
from nepes_enhanced_processor import NEPESEnhancedProcessor

def analyze_tsk_processor_logic():
    """分析TSK处理器的逻辑"""
    print("🔍 分析TSK处理器逻辑")
    print("=" * 40)
    
    dummy_file = "009.NNS157-09-E4"
    
    # 使用TSK处理器
    tsk_processor = TSKMapProcessor()
    success = tsk_processor.read_file(dummy_file)
    
    if not success:
        print("❌ TSK处理器加载失败")
        return None
    
    header_success = tsk_processor.parse_file_header()
    if not header_success:
        print("❌ TSK处理器头部解析失败")
        return None
    
    print(f"📊 TSK处理器参数:")
    print(f"   TestResultCategory: {tsk_processor.TestResultCategory}")
    print(f"   columnsize: {tsk_processor.columnsize}")
    print(f"   rowsize: {tsk_processor.rowsize}")
    
    # 手动计算关键位置
    test_positions = [
        {"x": 79, "y": 0, "description": "x=79,y=0"},
        {"x": 149, "y": 2, "description": "x=149,y=2"}
    ]
    
    print(f"\n📊 TSK处理器位置计算:")
    for pos_info in test_positions:
        x, y = pos_info["x"], pos_info["y"]
        
        # TSK处理器逻辑
        i = y + 1  # Convert to 1-based
        j = x + 1  # Convert to 1-based
        die_index = (i - 1) * tsk_processor.columnsize + j - 1
        category_start_pos = tsk_processor.TestResultCategory + 4 * die_index
        
        print(f"\n   {pos_info['description']}:")
        print(f"     1-based坐标: i={i}, j={j}")
        print(f"     die_index: {die_index}")
        print(f"     category_start_pos: {category_start_pos}")
        
        # 使用get_binary方法读取 (1-based索引)
        category_binary = tsk_processor.get_binary(tsk_processor.filearray, 
                                                  category_start_pos, 
                                                  category_start_pos + 3)
        
        # 直接读取filearray (0-based索引)
        direct_bytes = tsk_processor.filearray[category_start_pos-1:category_start_pos+3]
        
        print(f"     get_binary结果: {category_binary}")
        print(f"     直接读取(pos-1): {direct_bytes.hex().upper()}")
        print(f"     直接读取(pos): {tsk_processor.filearray[category_start_pos:category_start_pos+4].hex().upper()}")
    
    return tsk_processor

def test_get_binary_indexing():
    """测试get_binary的索引逻辑"""
    print(f"\n🔍 测试get_binary的索引逻辑")
    print("=" * 40)
    
    dummy_file = "009.NNS157-09-E4"
    
    tsk_processor = TSKMapProcessor()
    tsk_processor.read_file(dummy_file)
    tsk_processor.parse_file_header()
    
    # 测试get_binary的1-based vs 0-based
    test_pos = 15365
    
    print(f"📊 get_binary索引测试 (位置{test_pos}):")
    
    # 1-based调用 (TSK处理器方式)
    binary_1based = tsk_processor.get_binary(tsk_processor.filearray, test_pos, test_pos + 3)
    
    # 0-based直接读取
    bytes_0based = tsk_processor.filearray[test_pos:test_pos+4]
    bytes_0based_minus1 = tsk_processor.filearray[test_pos-1:test_pos+3]
    
    print(f"   1-based get_binary({test_pos}, {test_pos+3}): {binary_1based}")
    print(f"   0-based直接读取[{test_pos}:{test_pos+4}]: {bytes_0based.hex().upper()}")
    print(f"   0-based直接读取[{test_pos-1}:{test_pos+3}]: {bytes_0based_minus1.hex().upper()}")
    
    # 分析get_binary实际读取的字节
    print(f"\n   get_binary实际读取范围: [{test_pos-1}:{test_pos+3+1}] (因为range(start_pos-1, end_pos))")
    actual_bytes = tsk_processor.filearray[test_pos-1:test_pos+4]
    print(f"   实际读取字节: {actual_bytes.hex().upper()}")
    
    return test_pos

def fix_nepes_indexing():
    """修复NEPES处理器的索引逻辑"""
    print(f"\n🔧 修复NEPES处理器索引逻辑")
    print("=" * 40)
    
    print("📊 问题分析:")
    print("   TSK处理器: 使用1-based索引调用get_binary")
    print("   get_binary: 内部转换为0-based (start_pos-1)")
    print("   NEPES处理器: 直接使用0-based索引操作filearray")
    print("   结果: NEPES处理器需要使用1-based索引来匹配TSK逻辑")
    
    print(f"\n✅ 修复方案:")
    print("   NEPES处理器应该使用: category_pos = TestResultCategory + 4 * die_index + 1")
    print("   这样可以匹配TSK处理器的1-based索引逻辑")
    
    return True

def main():
    """主测试函数"""
    print("🧪 TSK vs NEPES逻辑对比分析")
    print("找出1个字节偏移的根本原因")
    print("=" * 70)
    
    # 切换到测试目录
    os.chdir(os.path.dirname(os.path.abspath(__file__)))
    
    try:
        # 分析1: TSK处理器逻辑
        tsk_processor = analyze_tsk_processor_logic()
        
        # 分析2: get_binary索引逻辑
        test_get_binary_indexing()
        
        # 分析3: 修复方案
        fix_nepes_indexing()
        
        print("\n" + "=" * 70)
        print("🎯 分析结论:")
        print("✅ 找到了1个字节偏移的根本原因")
        print("✅ TSK处理器使用1-based索引")
        print("✅ NEPES处理器需要匹配这个逻辑")
        print("✅ 修复方案: category_pos + 1")
        
        return True
            
    except Exception as e:
        print(f"❌ 分析过程出错: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
