#!/usr/bin/env python3
"""
调试total_dies问题
检查为什么在parse_bump_map时total_dies是0

Author: Yuribytes
Company: Chipone TE development Team
"""

import os
import sys

# 添加父目录到路径
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, parent_dir)

from nepes_enhanced_processor import NEPESEnhancedProcessor

def debug_total_dies():
    """调试total_dies问题"""
    print("🔍 调试total_dies问题")
    print("=" * 40)
    
    dummy_file = "009.NNS157-09-E4"
    bump_file = "D97127.09"
    
    # 创建处理器
    processor = NEPESEnhancedProcessor()
    
    print(f"📊 初始状态:")
    print(f"   total_dies: {getattr(processor, 'total_dies', 'undefined')}")
    print(f"   columnsize: {getattr(processor, 'columnsize', 'undefined')}")
    print(f"   rowsize: {getattr(processor, 'rowsize', 'undefined')}")
    
    # 步骤1: 加载dummy map
    print(f"\n📋 步骤1: 加载dummy map")
    success = processor.load_dummy_map(dummy_file)
    
    print(f"   加载结果: {'成功' if success else '失败'}")
    print(f"   total_dies: {getattr(processor, 'total_dies', 'undefined')}")
    print(f"   columnsize: {getattr(processor, 'columnsize', 'undefined')}")
    print(f"   rowsize: {getattr(processor, 'rowsize', 'undefined')}")
    
    if success:
        # 手动验证计算
        manual_total = processor.columnsize * processor.rowsize
        print(f"   手动计算: {processor.columnsize} × {processor.rowsize} = {manual_total}")
        print(f"   计算匹配: {'✅' if manual_total == processor.total_dies else '❌'}")
    
    # 步骤2: 解析bump map
    print(f"\n📋 步骤2: 解析bump map")
    
    # 在parse_bump_map之前再次检查
    print(f"   解析前total_dies: {getattr(processor, 'total_dies', 'undefined')}")
    
    success = processor.parse_bump_map(bump_file)
    
    print(f"   解析结果: {'成功' if success else '失败'}")
    print(f"   解析后total_dies: {getattr(processor, 'total_dies', 'undefined')}")
    
    if success:
        # 检查bump_data结构
        print(f"   bump_data行数: {len(processor.bump_data)}")
        print(f"   bump_data列数: {len(processor.bump_data[0]) if processor.bump_data else 0}")
        print(f"   bump_data总数: {len(processor.bump_data) * len(processor.bump_data[0]) if processor.bump_data else 0}")
        
        # 检查processor的rows和cols属性
        print(f"   processor.rows: {getattr(processor, 'rows', 'undefined')}")
        print(f"   processor.cols: {getattr(processor, 'cols', 'undefined')}")
    
    return success

def debug_bump_data_access():
    """调试bump_data访问"""
    print(f"\n🔍 调试bump_data访问")
    print("=" * 40)
    
    dummy_file = "009.NNS157-09-E4"
    bump_file = "D97127.09"
    
    processor = NEPESEnhancedProcessor()
    processor.load_dummy_map(dummy_file)
    processor.parse_bump_map(bump_file)
    
    print(f"📊 数据结构验证:")
    print(f"   rowsize (dummy): {processor.rowsize}")
    print(f"   columnsize (dummy): {processor.columnsize}")
    print(f"   rows (bump): {processor.rows}")
    print(f"   cols (bump): {processor.cols}")
    
    # 检查是否匹配
    dimensions_match = (processor.rowsize == processor.rows and 
                       processor.columnsize == processor.cols)
    
    print(f"   维度匹配: {'✅' if dimensions_match else '❌'}")
    
    if not dimensions_match:
        print(f"   ❌ 维度不匹配！这可能是问题的根源")
        print(f"   Dummy map: {processor.rowsize} × {processor.columnsize}")
        print(f"   Bump map: {processor.rows} × {processor.cols}")
    
    # 测试几个位置的访问
    print(f"\n📊 位置访问测试:")
    
    test_positions = [
        (0, 0), (79, 0), (149, 2), (304, 7)
    ]
    
    for x, y in test_positions:
        try:
            if y < len(processor.bump_data) and x < len(processor.bump_data[y]):
                value = processor.bump_data[y][x]
                print(f"   位置({x},{y}): '{value}'")
            else:
                print(f"   位置({x},{y}): 越界")
        except Exception as e:
            print(f"   位置({x},{y}): 错误 - {e}")

def main():
    """主调试函数"""
    print("🧪 total_dies问题调试")
    print("=" * 70)
    
    # 切换到测试目录
    os.chdir(os.path.dirname(os.path.abspath(__file__)))
    
    try:
        # 调试1: total_dies问题
        result1 = debug_total_dies()
        
        # 调试2: bump_data访问
        debug_bump_data_access()
        
        print("\n" + "=" * 70)
        print("🎉 调试完成")
        
        return result1
            
    except Exception as e:
        print(f"❌ 调试过程出错: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
