#!/usr/bin/env python3
"""
Test script for Bump Map Tool GUI improvements
Tests the enhanced GUI features including test house priority, selection display, and button layout
Author: Yuribytes
Company: Chipone TE development Team
"""

import sys
import os
import tkinter as tk
from tkinter import ttk
import time

# Add parent directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from bump_map_tool_frame import BumpMapToolFrame
except ImportError as e:
    print(f"Import error: {e}")
    print("Please ensure all required modules are available.")
    sys.exit(1)


class BumpMapGUITester:
    """Test class for Bump Map Tool GUI improvements"""
    
    def __init__(self):
        self.root = None
        self.tool_frame = None
        self.test_results = []
    
    def setup_gui(self):
        """Set up GUI for testing"""
        self.root = tk.Tk()
        self.root.title("Bump Map Tool GUI Improvements Test")
        self.root.geometry("1000x800")
        
        # Mock app controller
        class MockController:
            def __init__(self, root):
                self.root = root
            def return_to_selector_with_confirmation(self, tool_name, has_unsaved):
                print(f"Mock: Return to selector from {tool_name}, unsaved: {has_unsaved}")
                return True
        
        controller = MockController(self.root)
        self.tool_frame = BumpMapToolFrame(self.root, controller)
        self.tool_frame.show()
    
    def test_test_house_priority_order(self):
        """Test that priority test houses appear first"""
        print("🧪 Testing test house priority order...")
        
        # Expected priority test houses (first 6)
        expected_priority = [
            "ChipMOS", "Unisem", "TongFu", "NEPES", "Chipbond", "Powertech"
        ]
        
        # Get actual order from test_houses dict
        actual_order = list(self.tool_frame.test_houses.keys())
        actual_priority = actual_order[:6]
        
        print(f"Expected priority order: {expected_priority}")
        print(f"Actual priority order: {actual_priority}")
        
        # Check if priority test houses are in the first 6 positions
        priority_match = all(expected in actual_priority for expected in expected_priority)
        
        if priority_match:
            print("✅ Priority test houses are correctly positioned")
            self.test_results.append(("Priority Order", "PASS"))
        else:
            print("❌ Priority test houses order is incorrect")
            self.test_results.append(("Priority Order", "FAIL"))
        
        return priority_match
    
    def test_selection_display_functionality(self):
        """Test the selection display functionality"""
        print("\n🧪 Testing selection display functionality...")
        
        try:
            # Check if selection display variables exist
            assert hasattr(self.tool_frame, 'selected_test_house_display')
            assert hasattr(self.tool_frame, 'on_test_house_selection_changed')
            
            # Test initial state
            initial_display = self.tool_frame.selected_test_house_display.get()
            print(f"Initial display text: '{initial_display}'")
            
            # Simulate test house selection
            test_house_id = "ChipMOS"
            self.tool_frame.selected_test_house.set(test_house_id)
            
            # Allow GUI to update
            self.root.update_idletasks()
            time.sleep(0.1)
            
            # Check updated display
            updated_display = self.tool_frame.selected_test_house_display.get()
            print(f"Updated display text: '{updated_display}'")
            
            # Verify the display contains the test house name
            expected_name = self.tool_frame.test_houses[test_house_id]
            display_correct = expected_name in updated_display and "✓" in updated_display
            
            if display_correct:
                print("✅ Selection display functionality works correctly")
                self.test_results.append(("Selection Display", "PASS"))
            else:
                print("❌ Selection display functionality failed")
                self.test_results.append(("Selection Display", "FAIL"))
            
            return display_correct
            
        except Exception as e:
            print(f"❌ Selection display test failed with error: {e}")
            self.test_results.append(("Selection Display", "FAIL"))
            return False
    
    def test_button_layout(self):
        """Test the button layout and functionality"""
        print("\n🧪 Testing button layout...")
        
        try:
            # Check if required methods exist
            required_methods = [
                'process_maps', 'clear_memory', 'return_to_selector', 'exit_application'
            ]
            
            methods_exist = all(hasattr(self.tool_frame, method) for method in required_methods)
            
            if methods_exist:
                print("✅ All required button methods exist")
                self.test_results.append(("Button Methods", "PASS"))
            else:
                print("❌ Some button methods are missing")
                self.test_results.append(("Button Methods", "FAIL"))
                return False
            
            # Test clear_memory functionality (should not show popup)
            print("Testing clear_memory functionality...")
            original_status = self.tool_frame.status_var.get()
            
            # Call clear_memory
            self.tool_frame.clear_memory()
            
            # Check if status was updated
            new_status = self.tool_frame.status_var.get()
            memory_clear_works = "Memory cleared" in new_status
            
            if memory_clear_works:
                print("✅ Clear memory functionality works (no popup)")
                self.test_results.append(("Clear Memory", "PASS"))
            else:
                print("❌ Clear memory functionality failed")
                self.test_results.append(("Clear Memory", "FAIL"))
            
            return methods_exist and memory_clear_works
            
        except Exception as e:
            print(f"❌ Button layout test failed with error: {e}")
            self.test_results.append(("Button Layout", "FAIL"))
            return False
    
    def test_file_format_priority(self):
        """Test output file format priority"""
        print("\n🧪 Testing file format priority...")
        
        try:
            # This test would require actually opening the file dialog
            # For now, we'll just verify the method exists and can be called
            assert hasattr(self.tool_frame, 'browse_output_file')
            
            print("✅ Output file browse method exists")
            self.test_results.append(("File Format Priority", "PASS"))
            return True
            
        except Exception as e:
            print(f"❌ File format priority test failed: {e}")
            self.test_results.append(("File Format Priority", "FAIL"))
            return False
    
    def test_gui_layout_integrity(self):
        """Test overall GUI layout integrity"""
        print("\n🧪 Testing GUI layout integrity...")
        
        try:
            # Update GUI to ensure all widgets are rendered
            self.root.update_idletasks()
            
            # Check if main frame exists and is visible
            assert self.tool_frame.main_frame is not None
            
            # Check if key variables exist
            key_variables = [
                'bump_map_file_path', 'dummy_map_file_path', 'output_file_path',
                'selected_test_house', 'selected_test_house_display', 'status_var'
            ]
            
            variables_exist = all(hasattr(self.tool_frame, var) for var in key_variables)
            
            if variables_exist:
                print("✅ All key GUI variables exist")
                self.test_results.append(("GUI Layout", "PASS"))
            else:
                print("❌ Some GUI variables are missing")
                self.test_results.append(("GUI Layout", "FAIL"))
            
            return variables_exist
            
        except Exception as e:
            print(f"❌ GUI layout integrity test failed: {e}")
            self.test_results.append(("GUI Layout", "FAIL"))
            return False
    
    def run_all_tests(self):
        """Run all GUI improvement tests"""
        print("🚀 Bump Map Tool GUI Improvements - Test Suite")
        print("=" * 60)
        
        try:
            self.setup_gui()
            
            # Run individual tests
            test1 = self.test_test_house_priority_order()
            test2 = self.test_selection_display_functionality()
            test3 = self.test_button_layout()
            test4 = self.test_file_format_priority()
            test5 = self.test_gui_layout_integrity()
            
            # Summary
            print("\n" + "=" * 60)
            print("📊 Test Results Summary:")
            print("=" * 60)
            
            for test_name, result in self.test_results:
                status_icon = "✅" if result == "PASS" else "❌"
                print(f"{status_icon} {test_name}: {result}")
            
            total_tests = len(self.test_results)
            passed_tests = sum(1 for _, result in self.test_results if result == "PASS")
            
            print(f"\nTotal: {passed_tests}/{total_tests} tests passed")
            
            if passed_tests == total_tests:
                print("\n🎉 All GUI improvement tests passed!")
            else:
                print(f"\n⚠️  {total_tests - passed_tests} test(s) failed")
            
            # Show GUI for manual inspection
            print("\n📋 GUI is displayed for manual inspection...")
            print("Close the window to complete testing...")
            self.root.mainloop()
            
        except Exception as e:
            print(f"❌ Test suite failed with error: {e}")
        finally:
            if self.root:
                try:
                    self.root.destroy()
                except:
                    pass


def main():
    """Main test function"""
    tester = BumpMapGUITester()
    tester.run_all_tests()


if __name__ == "__main__":
    main()
