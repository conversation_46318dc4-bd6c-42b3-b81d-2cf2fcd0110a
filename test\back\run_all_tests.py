#!/usr/bin/env python3
"""
Test runner script that runs all tests in the test folder
Ensures all generated files stay within the test directory
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

# Get the test directory path
TEST_DIR = Path(__file__).parent
PROJECT_ROOT = TEST_DIR.parent

# Add project root to Python path
sys.path.insert(0, str(PROJECT_ROOT))

def setup_test_environment():
    """Setup test environment and clean up old test files"""
    print("Setting up test environment...")

    # Change to test directory
    os.chdir(TEST_DIR)

    # Clean up old test generated files
    cleanup_patterns = [
        "*.xlsx",
        "demo_*",
        "test_mixed_files",
        "test_valid_maps"
    ]

    for pattern in cleanup_patterns:
        for item in TEST_DIR.glob(pattern):
            if item.is_file():
                item.unlink()
                print(f"   Removed file: {item.name}")
            elif item.is_dir():
                shutil.rmtree(item)
                print(f"   Removed directory: {item.name}")

    print("Test environment ready")


def run_test_script(script_name):
    """Run a single test script"""
    script_path = TEST_DIR / script_name
    
    if not script_path.exists():
        print(f"❌ Test script not found: {script_name}")
        return False
    
    print(f"\n🧪 Running {script_name}...")
    print("-" * 50)
    
    try:
        # Run the test script in the test directory
        result = subprocess.run(
            [sys.executable, script_name],
            cwd=TEST_DIR,
            capture_output=True,
            text=True,
            timeout=120
        )
        
        # Print output
        if result.stdout:
            print(result.stdout)
        
        if result.stderr:
            print("STDERR:", result.stderr)
        
        if result.returncode == 0:
            print(f"✅ {script_name} PASSED")
            return True
        else:
            print(f"❌ {script_name} FAILED (exit code: {result.returncode})")
            return False
            
    except subprocess.TimeoutExpired:
        print(f"❌ {script_name} TIMEOUT")
        return False
    except Exception as e:
        print(f"❌ {script_name} ERROR: {e}")
        return False


def main():
    """Run all tests"""
    print("🚀 Running All Tests in Test Directory")
    print("=" * 60)
    
    # Setup test environment
    setup_test_environment()
    
    # List of test scripts to run
    test_scripts = [
        "test_output_folder_selection.py",
        "demo_new_features.py",
        "test_bin_statistics_fix.py",
        "test_yield_calculation_fix.py",
        "test_folder_loading_fix.py"
    ]
    
    # Run tests
    results = {}
    for script in test_scripts:
        results[script] = run_test_script(script)
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 Test Results Summary:")
    
    passed = 0
    total = len(results)
    
    for script, success in results.items():
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"   {script:<35} {status}")
        if success:
            passed += 1
    
    print(f"\n🎯 Overall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed!")
    else:
        print("⚠️ Some tests failed")
    
    # Show generated files
    print(f"\n📁 Generated files in test directory:")
    generated_files = []
    for pattern in ["*.xlsx", "demo_*"]:
        generated_files.extend(TEST_DIR.glob(pattern))
    
    if generated_files:
        for f in generated_files:
            if f.is_file():
                size = f.stat().st_size
                print(f"   📄 {f.name} ({size:,} bytes)")
            elif f.is_dir():
                file_count = len(list(f.glob("*")))
                print(f"   📁 {f.name}/ ({file_count} files)")
    else:
        print("   (No generated files)")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
