#!/usr/bin/env python3
"""
Test script for worker_file19.md requirements
Tests the modified TMB processor functionality
"""

import os
import sys
import tempfile
from datetime import datetime

# Add parent directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from tmb_processor import TMBProcessor
from config_reader import Config<PERSON>eader


def test_config_reader_l5():
    """Test ConfigReader L5 cell reading"""
    print("=" * 60)
    print("Testing ConfigReader L5 cell reading...")
    
    config_reader = ConfigReader()
    config_file = "test/CP1_program_bin.xlsx"
    
    if not os.path.exists(config_file):
        print(f"❌ Config file not found: {config_file}")
        return False
    
    if config_reader.read_config_file(config_file):
        print("✅ Config file loaded successfully")
        
        # Test L5 content (Operator Name)
        operator_name = config_reader.get_operator_name()
        print(f"L5 (Operator Name): '{operator_name}'")
        
        # Test H5 content (Test Flow)
        test_flow = config_reader.get_test_flow()
        print(f"H5 (Test Flow): '{test_flow}'")
        
        # Verify expected values
        if operator_name == "85016":
            print("✅ L5 content matches expected value")
        else:
            print(f"❌ L5 content mismatch. Expected: '85016', Got: '{operator_name}'")
            
        if test_flow == "CP1":
            print("✅ H5 content matches expected value")
        else:
            print(f"❌ H5 content mismatch. Expected: 'CP1', Got: '{test_flow}'")
            
        return True
    else:
        print("❌ Failed to load config file")
        return False


def test_tmb_filename_generation():
    """Test TMB filename generation with new format"""
    print("=" * 60)
    print("Testing TMB filename generation...")
    
    # Setup
    tmb_processor = TMBProcessor()
    config_reader = ConfigReader()
    
    # Load config
    config_file = "test/CP1_program_bin.xlsx"
    if not config_reader.read_config_file(config_file):
        print("❌ Failed to load config file")
        return False
    
    tmb_processor.set_config_reader(config_reader)
    
    # Test with sample MAP file
    map_file = "test/019.3AD416-19-F4"
    if not os.path.exists(map_file):
        print(f"❌ MAP file not found: {map_file}")
        return False
    
    # Generate filename
    tmb_filename = tmb_processor.generate_tmb_filename(map_file)
    filename_only = os.path.basename(tmb_filename)
    
    print(f"Generated filename: {filename_only}")
    
    # Check format: wafer_id + "_" + H5 + "_" + timestamp + ".tmb"
    expected_parts = ["3AD416-19-F4", "CP1"]  # wafer_id and H5 content
    
    success = True
    for part in expected_parts:
        if part not in filename_only:
            print(f"❌ Missing expected part '{part}' in filename")
            success = False
    
    if filename_only.endswith(".tmb"):
        print("✅ Filename has correct .tmb extension")
    else:
        print("❌ Filename missing .tmb extension")
        success = False
    
    if success:
        print("✅ Filename format is correct")
    
    return success


def test_tmb_content_generation():
    """Test TMB content generation with new requirements"""
    print("=" * 60)
    print("Testing TMB content generation...")
    
    # Setup
    tmb_processor = TMBProcessor()
    config_reader = ConfigReader()
    
    # Load config
    config_file = "test/CP1_program_bin.xlsx"
    if not config_reader.read_config_file(config_file):
        print("❌ Failed to load config file")
        return False
    
    tmb_processor.set_config_reader(config_reader)
    
    # Test with sample MAP file
    map_file = "test/019.3AD416-19-F4"
    if not os.path.exists(map_file):
        print(f"❌ MAP file not found: {map_file}")
        return False
    
    # Process MAP file
    tmb_output = tmb_processor.process_map_to_tmb(map_file)
    
    if tmb_output and os.path.exists(tmb_output):
        print(f"✅ TMB file generated: {os.path.basename(tmb_output)}")
        
        # Read and check content
        with open(tmb_output, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check Operator Name
        if "Operator Name : 85016" in content:
            print("✅ Operator Name correctly filled from L5")
        else:
            print("❌ Operator Name not correctly filled")
            print("Looking for 'Operator Name : 85016' in content...")
            # Show relevant lines
            lines = content.split('\n')
            for i, line in enumerate(lines):
                if "Operator Name" in line:
                    print(f"Line {i+1}: {line}")
        
        # Check format alignment (look for --+ pattern)
        lines = content.split('\n')
        separator_line = None
        for line in lines:
            if "--+" in line and line.count("--+") > 5:  # Find separator line
                separator_line = line
                break
        
        if separator_line:
            # Check if it uses consistent --+ pattern
            if "---+" not in separator_line:  # Should not have ---+
                print("✅ Format alignment uses consistent --+ separators")
            else:
                print("❌ Format alignment still has ---+ separators")
                print(f"Separator line: {separator_line[:100]}...")
        else:
            print("❌ Could not find separator line in TMB content")
        
        print(f"TMB file saved to: {tmb_output}")
        return True
    else:
        print("❌ Failed to generate TMB file")
        return False


def main():
    """Main test function"""
    print("Testing worker_file19.md requirements implementation")
    print("=" * 60)
    
    # Change to script directory
    script_dir = os.path.dirname(os.path.abspath(__file__))
    parent_dir = os.path.dirname(script_dir)
    os.chdir(parent_dir)
    
    print(f"Working directory: {os.getcwd()}")
    
    # Run tests
    tests = [
        ("ConfigReader L5 reading", test_config_reader_l5),
        ("TMB filename generation", test_tmb_filename_generation),
        ("TMB content generation", test_tmb_content_generation),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n🧪 Running test: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
            if result:
                print(f"✅ {test_name} PASSED")
            else:
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            print(f"❌ {test_name} ERROR: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 60)
    print("TEST SUMMARY")
    print("=" * 60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! worker_file19.md requirements implemented successfully.")
    else:
        print("⚠️  Some tests failed. Please check the implementation.")


if __name__ == "__main__":
    main()
