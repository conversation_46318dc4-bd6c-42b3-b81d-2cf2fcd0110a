#!/usr/bin/env python3
"""
测试1字节偏移修复
验证NEPES处理器现在是否与TSK处理器逻辑一致

Author: Yuribytes
Company: Chipone TE development Team
"""

import os
import sys

# 添加父目录到路径
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, parent_dir)

from nepes_enhanced_processor import NEPESEnhancedProcessor
from tsk_map_processor import TSKMapProcessor

def test_fixed_offset():
    """测试修复后的偏移"""
    print("🧪 测试1字节偏移修复")
    print("验证NEPES处理器与TSK处理器逻辑一致")
    print("=" * 60)
    
    bump_file = "D97127.09"
    dummy_file = "009.NNS157-09-E4"
    output_file = "offset_fix_test.tsk"
    
    # 读取原始文件
    with open(dummy_file, 'rb') as f:
        original_data = f.read()
    
    # 分析TSK处理器的实际读取位置
    tsk_processor = TSKMapProcessor()
    tsk_processor.read_file(dummy_file)
    tsk_processor.parse_file_header()
    
    print(f"📊 TSK处理器实际读取位置分析:")
    
    # x=79,y=0的位置
    die_index_79 = 79
    category_start_pos_79 = tsk_processor.TestResultCategory + 4 * die_index_79
    
    print(f"   x=79,y=0:")
    print(f"     计算位置: {category_start_pos_79}")
    print(f"     get_binary实际读取: [{category_start_pos_79-1}:{category_start_pos_79+4}]")
    print(f"     实际字节位置: 15364-15367")
    
    # x=149,y=2的位置
    die_index_149 = 2 * 305 + 149
    category_start_pos_149 = tsk_processor.TestResultCategory + 4 * die_index_149
    
    print(f"   x=149,y=2:")
    print(f"     计算位置: {category_start_pos_149}")
    print(f"     get_binary实际读取: [{category_start_pos_149-1}:{category_start_pos_149+4}]")
    print(f"     实际字节位置: 18084-18087")
    
    print(f"\n📊 原始数据 (TSK实际读取位置):")
    print(f"   位置15364-15367: {original_data[15364:15368].hex().upper()}")
    print(f"   位置18084-18087: {original_data[18084:18088].hex().upper()}")
    
    # 执行NEPES处理
    print(f"\n🚀 执行NEPES Enhanced处理...")
    processor = NEPESEnhancedProcessor()
    success = processor.process_nepes_enhanced(bump_file, dummy_file, output_file)
    
    if not success:
        print("❌ 处理失败")
        return False
    
    # 读取处理后文件
    with open(output_file, 'rb') as f:
        processed_data = f.read()
    
    print(f"\n📊 处理后数据 (TSK实际读取位置):")
    print(f"   位置15364-15367: {processed_data[15364:15368].hex().upper()}")
    print(f"   位置18084-18087: {processed_data[18084:18088].hex().upper()}")
    
    # 验证结果
    print(f"\n🎯 验证结果:")
    
    # x=79,y=0验证 (应该在15364-15367写入0000003F)
    pos_79_0 = processed_data[15364:15368]
    expected_79_0 = "0000003F"
    actual_79_0 = pos_79_0.hex().upper()
    
    print(f"   x=79,y=0 (TSK实际位置15364-15367):")
    print(f"     预期: {expected_79_0}")
    print(f"     实际: {actual_79_0}")
    print(f"     正确: {'✅' if actual_79_0 == expected_79_0 else '❌'}")
    
    # x=149,y=2验证 (应该在18084-18087写入0000003B)
    pos_149_2 = processed_data[18084:18088]
    expected_149_2 = "0000003B"
    actual_149_2 = pos_149_2.hex().upper()
    
    print(f"   x=149,y=2 (TSK实际位置18084-18087):")
    print(f"     预期: {expected_149_2}")
    print(f"     实际: {actual_149_2}")
    print(f"     正确: {'✅' if actual_149_2 == expected_149_2 else '❌'}")
    
    # 检查之前的错误位置是否还有数据
    print(f"\n🔍 检查之前的错误位置:")
    old_pos_79 = processed_data[15365:15369]
    old_pos_149 = processed_data[18085:18089]
    
    print(f"   旧位置15365-15368: {old_pos_79.hex().upper()} (应该是00000000)")
    print(f"   旧位置18085-18088: {old_pos_149.hex().upper()} (应该是00000000)")
    
    # 总结
    success_79 = actual_79_0 == expected_79_0
    success_149 = actual_149_2 == expected_149_2
    old_clean_79 = old_pos_79.hex().upper() == "00000000"
    old_clean_149 = old_pos_149.hex().upper() == "00000000"
    
    if success_79 and success_149 and old_clean_79 and old_clean_149:
        print(f"\n🎉 1字节偏移修复成功!")
        print(f"✅ x=79,y=0正确写入TSK实际位置15364-15367")
        print(f"✅ x=149,y=2正确写入TSK实际位置18084-18087")
        print(f"✅ 旧的错误位置已清理")
        print(f"✅ 现在与Full Map Tool逻辑完全一致")
        return True
    else:
        print(f"\n❌ 修复仍有问题")
        if not success_79:
            print(f"❌ x=79,y=0写入错误")
        if not success_149:
            print(f"❌ x=149,y=2写入错误")
        if not old_clean_79:
            print(f"❌ 旧位置15365-15368未清理")
        if not old_clean_149:
            print(f"❌ 旧位置18085-18088未清理")
        return False

def main():
    """主测试函数"""
    print("🧪 1字节偏移修复测试")
    print("=" * 70)
    
    # 切换到测试目录
    os.chdir(os.path.dirname(os.path.abspath(__file__)))
    
    try:
        # 测试修复后的偏移
        result = test_fixed_offset()
        
        print("\n" + "=" * 70)
        print("🎉 测试结果总结:")
        print(f"   1字节偏移修复: {'✅ 成功' if result else '❌ 失败'}")
        
        if result:
            print(f"\n🎯 修复完成!")
            print(f"✅ NEPES处理器现在与TSK/Full Map Tool逻辑完全一致")
            print(f"✅ 解决了'整体都错开了1个byte'的问题")
            print(f"✅ x=79,y=0现在写入正确位置15364")
        else:
            print(f"\n❌ 仍需进一步调试")
            
        return result
            
    except Exception as e:
        print(f"❌ 测试过程出错: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
