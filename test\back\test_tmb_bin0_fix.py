#!/usr/bin/env python3
"""
测试TMB处理器的bin0修复
验证bin0和其他测试bin的正确处理
"""

import os
import sys
from tmb_processor import TMBProcessor
from tsk_map_processor import TSKMapProcessor
from config_reader import Config<PERSON><PERSON><PERSON>


def test_tmb_bin0_processing():
    """测试TMB处理器对bin0的正确处理"""
    print("🧪 测试TMB处理器的bin0修复")
    print("=" * 50)
    
    # 查找测试文件
    test_files = []
    for root, dirs, files in os.walk('.'):
        for file in files:
            if file.endswith('.map') or file.endswith('.MAP'):
                test_files.append(os.path.join(root, file))
                if len(test_files) >= 1:
                    break
        if test_files:
            break
    
    if not test_files:
        print("❌ 未找到MAP测试文件")
        return False
    
    test_file = test_files[0]
    print(f"📁 测试文件: {os.path.basename(test_file)}")
    
    try:
        # 创建TMB处理器
        tmb_processor = TMBProcessor()
        
        # 创建TSK处理器进行对比
        tsk_processor = TSKMapProcessor()
        
        # 读取和解析文件
        if not tsk_processor.read_file(test_file):
            print("❌ 无法读取MAP文件")
            return False
            
        if not tsk_processor.parse_file_header():
            print("❌ 无法解析MAP文件头")
            return False
            
        if not tsk_processor.process_die_data():
            print("❌ 无法处理die数据")
            return False
        
        # 设置TMB处理器
        tmb_processor.tsk_processor = tsk_processor
        
        print(f"✅ 文件解析成功")
        print(f"   尺寸: {tsk_processor.rowsize} × {tsk_processor.columnsize}")
        
        # 分析原始数据中的bin分布
        print(f"\n📊 原始数据分析:")
        category_counts = {}
        tested_positions = 0
        untested_positions = 0
        
        for row in range(tsk_processor.rowsize):
            for col in range(tsk_processor.columnsize):
                category = tsk_processor.map_data[row][col][0]
                color = tsk_processor.map_data[row][col][1]
                
                if color != 0:  # 有测试数据
                    tested_positions += 1
                    category_counts[category] = category_counts.get(category, 0) + 1
                else:  # 未测试
                    untested_positions += 1
        
        print(f"   总位置: {tsk_processor.rowsize * tsk_processor.columnsize}")
        print(f"   已测试: {tested_positions}")
        print(f"   未测试: {untested_positions}")
        print(f"   不同bin数: {len(category_counts)}")
        
        # 显示bin分布（包括bin0）
        print(f"\n📋 Bin分布:")
        for cat in sorted(category_counts.keys()):
            count = category_counts[cat]
            print(f"   Bin {cat:2d}: {count:4d} dies")
        
        # 测试TMB生成
        print(f"\n🔧 测试TMB生成:")
        
        # 生成下半部分统计
        lower_part = tmb_processor.generate_lower_part()
        lower_lines = lower_part.split('\n')
        
        # 验证统计结果
        print(f"   TMB统计验证:")
        tmb_category_counts = {}
        
        for line in lower_lines:
            if line.startswith('Cat '):
                parts = line.split(': ')
                if len(parts) == 2:
                    cat_str = parts[0].replace('Cat ', '')
                    cat_num = int(cat_str)
                    count = int(parts[1])
                    if count > 0:
                        tmb_category_counts[cat_num] = count
        
        # 对比原始数据和TMB统计
        print(f"   原始bin数: {len(category_counts)}")
        print(f"   TMB bin数: {len(tmb_category_counts)}")
        
        all_match = True
        for cat, count in category_counts.items():
            tmb_count = tmb_category_counts.get(cat, 0)
            if count != tmb_count:
                print(f"   ❌ Bin {cat}: 原始={count}, TMB={tmb_count}")
                all_match = False
            else:
                print(f"   ✅ Bin {cat}: {count} dies")
        
        # 检查是否有bin0数据
        bin0_original = category_counts.get(0, 0)
        bin0_tmb = tmb_category_counts.get(0, 0)
        
        print(f"\n🎯 Bin0特别检查:")
        print(f"   原始bin0: {bin0_original} dies")
        print(f"   TMB bin0: {bin0_tmb} dies")
        print(f"   bin0匹配: {'✅' if bin0_original == bin0_tmb else '❌'}")
        
        if bin0_original > 0:
            print(f"   ✅ 发现bin0测试数据，TMB正确处理")
        else:
            print(f"   📝 此文件无bin0测试数据")
        
        # 生成完整TMB内容进行测试
        print(f"\n📄 生成完整TMB内容:")
        tmb_content = tmb_processor.generate_tmb_content(test_file)
        
        # 保存测试TMB文件
        test_tmb_path = "test_output.tmb"
        with open(test_tmb_path, 'w', encoding='utf-8') as f:
            f.write(tmb_content)
        
        print(f"   ✅ TMB文件已生成: {test_tmb_path}")
        print(f"   文件大小: {len(tmb_content)} 字符")
        
        # 最终结果
        success = all_match and (bin0_original == bin0_tmb)
        
        print(f"\n🎉 测试结果:")
        if success:
            print(f"   ✅ 所有bin统计正确匹配")
            print(f"   ✅ bin0处理逻辑修复成功")
            print(f"   ✅ TMB生成与Full Map Tool逻辑一致")
        else:
            print(f"   ❌ 仍有问题需要解决")
        
        return success
        
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主函数"""
    print("TMB处理器bin0修复测试")
    print("验证TMB生成与Full Map Tool的一致性")
    print("=" * 60)
    
    success = test_tmb_bin0_processing()
    
    if success:
        print(f"\n🎊 测试通过！TMB处理器已正确修复")
    else:
        print(f"\n💥 测试失败，需要进一步调试")
    
    return success


if __name__ == "__main__":
    main()
