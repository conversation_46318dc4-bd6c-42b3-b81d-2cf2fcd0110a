任务流程：
针对Full Map Tool工具功能完善:
1，process tmb执行内容：
      ---上部：按照下面描述的参考格式进行修改（test文件夹3AD416000-1-19.TMB作为参考格式文件）
      ---中部：
                 --bytes数读取有些问题，表现在读取bytes位置和已经设计的tsk map 处理不同，推测是从0-byte开始计算读取位置，需要修改为从1-byte开始计算读取位置。
开发注意：
1，提供tmb文件供参考格式（test文件夹3AD416000-1-19.TMB作为参考格式文件）

上述功能开发注意事项：
     --- 主要功能不要破坏，架构做到统一
     --- GUI尽量统一，不要占用太多空间
     --- 复用代码部分，尽量做到代码统一

要求：
1，测试脚本和文件请放到test文件夹
2，生成的md说明文档，请放到temp文件夹，不能在主项目文件夹下生成
3，程序架构统一，框架统一，代码尽量简洁

测试文件说明：
1，测试map可以选择test下的019.3AD416-19-F4
