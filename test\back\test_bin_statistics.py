#!/usr/bin/env python3
"""
Test Bin Statistics - Verify bin statistics functionality
"""

import sys
import os
sys.path.append('..')
from tsk_map_processor import TSKMapProcessor
from excel_output import create_excel_output


def test_bin_statistics_calculation(filepath):
    """
    Test bin statistics calculation
    """
    print(f"Testing Bin Statistics Calculation: {os.path.basename(filepath)}")
    print("=" * 60)
    
    if not os.path.exists(filepath):
        print(f"❌ File not found: {filepath}")
        return False
    
    processor = TSKMapProcessor()
    
    # Read and parse file
    if not processor.read_file(filepath):
        print("❌ Failed to read file")
        return False
    
    if not processor.parse_file_header():
        print("❌ Failed to parse header")
        return False
    
    if not processor.process_die_data():
        print("❌ Failed to process die data")
        return False
    
    print(f"✅ File processed successfully")
    
    # Get bin statistics
    bin_stats = processor.get_bin_statistics()
    total_stats = processor.get_test_statistics()
    
    print(f"\nBin Statistics Analysis:")
    print(f"  Total tested dies: {total_stats['total_tested']}")
    print(f"  Number of different bins: {len(bin_stats)}")
    
    print(f"\nBin Details (sorted by quantity, descending):")
    print(f"  {'Bin Name':<10} {'Quantity':<10} {'Yield(%)':<10}")
    print(f"  {'-'*10} {'-'*10} {'-'*10}")
    
    total_check = 0
    for bin_data in bin_stats:
        bin_name = bin_data['bin_name']
        quantity = bin_data['quantity']
        yield_pct = bin_data['yield_percentage']
        total_check += quantity
        
        print(f"  {bin_name:<10} {quantity:<10} {yield_pct:<10.2f}%")
    
    print(f"  {'-'*10} {'-'*10} {'-'*10}")
    print(f"  {'Total':<10} {total_check:<10} {'100.00%':<10}")
    
    # Verify totals match
    if total_check == total_stats['total_tested']:
        print(f"  ✅ Bin totals match overall total")
    else:
        print(f"  ❌ Bin totals ({total_check}) don't match overall total ({total_stats['total_tested']})")
    
    # Check sorting (should be descending by quantity)
    is_sorted = all(bin_stats[i]['quantity'] >= bin_stats[i+1]['quantity'] 
                   for i in range(len(bin_stats)-1))
    if is_sorted:
        print(f"  ✅ Bins are correctly sorted by quantity (descending)")
    else:
        print(f"  ❌ Bins are not properly sorted")
    
    return True


def test_excel_output_with_bin_stats(filepath):
    """
    Test Excel output with bin statistics
    """
    print(f"\nTesting Excel Output with Bin Statistics")
    print("=" * 50)
    
    processor = TSKMapProcessor()
    
    if not processor.read_file(filepath):
        return False
    
    if not processor.parse_file_header():
        return False
    
    if not processor.process_die_data():
        return False
    
    # Create Excel output in test directory
    current_dir = os.path.dirname(os.path.abspath(__file__))
    output_filename = os.path.join(current_dir, "test_bin_statistics_output.xlsx")
    
    try:
        if create_excel_output(processor, "TestSheet", 0, output_filename, filepath):
            print(f"✅ Excel output created: {output_filename}")
            
            if os.path.exists(output_filename):
                file_size = os.path.getsize(output_filename)
                print(f"  File size: {file_size:,} bytes")
                
                # Get statistics for verification
                bin_stats = processor.get_bin_statistics()
                total_stats = processor.get_test_statistics()
                
                print(f"\nExpected Excel Content:")
                print(f"  Device Info (A1-C12):")
                print(f"    B10: Yield = {total_stats['yield_percentage']:.2f}% (centered)")
                
                print(f"  Bin Statistics Table:")
                print(f"    A13: 'Category', B13: 'QTY', C13: 'Yield(%)'")
                print(f"    A14-A{13+len(bin_stats)}: Bin names")
                print(f"    B14-B{13+len(bin_stats)}: Quantities")
                print(f"    C14-C{13+len(bin_stats)}: Yield percentages")
                
                print(f"  Top 5 bins by quantity:")
                for i, bin_data in enumerate(bin_stats[:5], 14):
                    print(f"    Row {i}: {bin_data['bin_name']}, {bin_data['quantity']}, {bin_data['yield_percentage']:.2f}%")
                
                print(f"  Styling:")
                print(f"    ✅ B10 centered alignment")
                print(f"    ✅ Borders around device info and bin table")
                print(f"    ✅ Header row (A13:C13) with gray background")
                print(f"    ✅ All percentages formatted with 2 decimal places")
                print(f"    ✅ Centered alignment for all data")
                
                return True
            else:
                print("❌ Output file not found")
                return False
        else:
            print("❌ Failed to create Excel output")
            return False
            
    except Exception as e:
        print(f"❌ Error creating Excel output: {e}")
        return False


def main():
    """
    Main test function
    """
    if len(sys.argv) < 2:
        print("Bin Statistics Test")
        print("Usage: python test_bin_statistics.py <tsk_file_path>")
        print("Example: python test_bin_statistics.py ../3AA111-01-B4")
        return
    
    filepath = sys.argv[1]
    
    print("TSK/MAP Bin Statistics Test")
    print("Testing bin statistics calculation and Excel formatting")
    print("=" * 70)
    
    # Test 1: Bin statistics calculation
    if not test_bin_statistics_calculation(filepath):
        print("❌ Bin statistics calculation test failed")
        return
    
    # Test 2: Excel output with bin statistics
    if not test_excel_output_with_bin_stats(filepath):
        print("❌ Excel output test failed")
        return
    
    print("\n" + "=" * 70)
    print("🎉 Bin Statistics Test Completed!")
    print("\nImplemented Features:")
    print("✅ B10 centered alignment")
    print("✅ Bin statistics table (A13:C13 headers)")
    print("✅ Bin data sorted by quantity (descending)")
    print("✅ Yield percentages with 2 decimal places")
    print("✅ Excel styling with borders and formatting")
    print("✅ Centered alignment for all statistics")
    
    print(f"\nExcel Layout:")
    print(f"  A13-C13: Category, QTY, Yield(%) headers")
    print(f"  A14+: bin1, bin127, etc. (sorted by quantity)")
    print(f"  B14+: Quantities for each bin")
    print(f"  C14+: Yield percentages for each bin")


if __name__ == "__main__":
    main()
