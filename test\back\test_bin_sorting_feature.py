#!/usr/bin/env python3
"""
Test script for the new bin sorting feature in Full Map Tool
Tests both sorting by quantity (descending) and by bin number (ascending)
"""

import os
import sys
import tempfile
import shutil

# Add parent directory to path to import modules
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_bin_sorting_functionality():
    """Test the bin sorting functionality with a sample MAP file"""
    print("Testing Bin Sorting Functionality")
    print("=" * 50)
    
    try:
        from tsk_map_processor import TSKMapProcessor
        
        # Find a test MAP file
        test_files = []
        test_dir = os.path.dirname(__file__)
        parent_dir = os.path.dirname(test_dir)
        
        # Look for MAP files in test directory and parent directory
        for root, dirs, files in os.walk(parent_dir):
            for file in files:
                if file.endswith('.map') or file.endswith('.MAP'):
                    test_files.append(os.path.join(root, file))
                    if len(test_files) >= 1:  # We only need one file for testing
                        break
            if test_files:
                break
        
        if not test_files:
            print("❌ No MAP files found for testing")
            return False
        
        test_file = test_files[0]
        print(f"📁 Using test file: {os.path.basename(test_file)}")
        
        # Test 1: Sort by quantity (descending) - default behavior
        print("\n1️⃣ Testing sort by quantity (descending)...")
        processor1 = TSKMapProcessor()
        processor1.file_path = test_file

        if not processor1.parse_file_header():
            print("❌ Failed to parse file header")
            return False

        if not processor1.process_die_data():
            print("❌ Failed to process die data")
            return False
        
        bin_stats_by_quantity = processor1.get_bin_statistics(sort_by_quantity=True)
        
        print(f"   Found {len(bin_stats_by_quantity)} different bins")
        print("   Top 5 bins by quantity:")
        for i, bin_data in enumerate(bin_stats_by_quantity[:5], 1):
            bin_name = bin_data['bin_name']
            quantity = bin_data['quantity']
            yield_pct = bin_data['yield_percentage']
            print(f"   {i}. {bin_name}: {quantity} dies ({yield_pct:.2f}%)")
        
        # Verify sorting by quantity (descending)
        quantities = [bin_data['quantity'] for bin_data in bin_stats_by_quantity]
        is_sorted_desc = all(quantities[i] >= quantities[i+1] for i in range(len(quantities)-1))
        
        if is_sorted_desc:
            print("   ✅ Bins are correctly sorted by quantity (descending)")
        else:
            print("   ❌ Bins are NOT correctly sorted by quantity")
            return False
        
        # Test 2: Sort by bin number (ascending)
        print("\n2️⃣ Testing sort by bin number (ascending)...")
        bin_stats_by_number = processor1.get_bin_statistics(sort_by_quantity=False)
        
        print("   Top 5 bins by bin number:")
        for i, bin_data in enumerate(bin_stats_by_number[:5], 1):
            bin_name = bin_data['bin_name']
            bin_number = int(bin_name.replace('bin', ''))
            quantity = bin_data['quantity']
            yield_pct = bin_data['yield_percentage']
            print(f"   {i}. {bin_name} (#{bin_number}): {quantity} dies ({yield_pct:.2f}%)")
        
        # Verify sorting by bin number (ascending)
        bin_numbers = [int(bin_data['bin_name'].replace('bin', '')) for bin_data in bin_stats_by_number]
        is_sorted_asc = all(bin_numbers[i] <= bin_numbers[i+1] for i in range(len(bin_numbers)-1))
        
        if is_sorted_asc:
            print("   ✅ Bins are correctly sorted by bin number (ascending)")
        else:
            print("   ❌ Bins are NOT correctly sorted by bin number")
            return False
        
        # Test 3: Compare the two sorting methods
        print("\n3️⃣ Comparing sorting methods...")
        
        # Check that both methods return the same bins, just in different order
        bins_by_quantity = set(bin_data['bin_name'] for bin_data in bin_stats_by_quantity)
        bins_by_number = set(bin_data['bin_name'] for bin_data in bin_stats_by_number)
        
        if bins_by_quantity == bins_by_number:
            print("   ✅ Both sorting methods return the same set of bins")
        else:
            print("   ❌ Sorting methods return different sets of bins")
            return False
        
        # Check that the quantities are the same for each bin
        quantity_map1 = {bin_data['bin_name']: bin_data['quantity'] for bin_data in bin_stats_by_quantity}
        quantity_map2 = {bin_data['bin_name']: bin_data['quantity'] for bin_data in bin_stats_by_number}
        
        if quantity_map1 == quantity_map2:
            print("   ✅ Bin quantities are consistent between sorting methods")
        else:
            print("   ❌ Bin quantities differ between sorting methods")
            return False
        
        # Show the difference in ordering
        print("\n📊 Sorting comparison (first 3 bins):")
        print("   By Quantity (Desc)    |    By Number (Asc)")
        print("   " + "-" * 22 + "|" + "-" * 22)
        
        for i in range(min(3, len(bin_stats_by_quantity), len(bin_stats_by_number))):
            qty_bin = bin_stats_by_quantity[i]
            num_bin = bin_stats_by_number[i]
            
            qty_name = qty_bin['bin_name']
            qty_count = qty_bin['quantity']
            num_name = num_bin['bin_name']
            num_count = num_bin['quantity']
            
            print(f"   {qty_name}: {qty_count:<8} | {num_name}: {num_count:<8}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_full_map_processor_integration():
    """Test the integration with FullMapProcessor"""
    print("\n" + "=" * 50)
    print("Testing FullMapProcessor Integration")
    print("=" * 50)
    
    try:
        from full_map_processor import FullMapProcessor
        
        # Create a temporary processor
        processor = FullMapProcessor()
        
        # Test default setting
        print("1️⃣ Testing default sort_by_quantity setting...")
        if processor.sort_by_quantity:
            print("   ✅ Default sort_by_quantity is True (sort by quantity)")
        else:
            print("   ❌ Default sort_by_quantity should be True")
            return False
        
        # Test setting the option
        print("\n2️⃣ Testing set_sort_by_quantity method...")
        processor.set_sort_by_quantity(False)
        if not processor.sort_by_quantity:
            print("   ✅ Successfully set sort_by_quantity to False")
        else:
            print("   ❌ Failed to set sort_by_quantity to False")
            return False
        
        processor.set_sort_by_quantity(True)
        if processor.sort_by_quantity:
            print("   ✅ Successfully set sort_by_quantity back to True")
        else:
            print("   ❌ Failed to set sort_by_quantity back to True")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error during FullMapProcessor testing: {e}")
        return False


def test_excel_output_integration():
    """Test the integration with ExcelOutputHandler"""
    print("\n" + "=" * 50)
    print("Testing ExcelOutputHandler Integration")
    print("=" * 50)
    
    try:
        from excel_output import ExcelOutputHandler
        from openpyxl import Workbook
        
        # Create a test workbook and handler
        workbook = Workbook()
        worksheet = workbook.active
        handler = ExcelOutputHandler()
        handler.workbook = workbook
        handler.worksheet = worksheet
        
        print("1️⃣ Testing ExcelOutputHandler method signatures...")
        
        # Check if the methods accept the sort_by_quantity parameter
        import inspect
        
        # Check write_rotated_data signature
        write_rotated_data_sig = inspect.signature(handler.write_rotated_data)
        if 'sort_by_quantity' in write_rotated_data_sig.parameters:
            print("   ✅ write_rotated_data accepts sort_by_quantity parameter")
        else:
            print("   ❌ write_rotated_data missing sort_by_quantity parameter")
            return False
        
        # Check _write_device_info signature
        write_device_info_sig = inspect.signature(handler._write_device_info)
        if 'sort_by_quantity' in write_device_info_sig.parameters:
            print("   ✅ _write_device_info accepts sort_by_quantity parameter")
        else:
            print("   ❌ _write_device_info missing sort_by_quantity parameter")
            return False
        
        # Check _write_bin_statistics signature
        write_bin_stats_sig = inspect.signature(handler._write_bin_statistics)
        if 'sort_by_quantity' in write_bin_stats_sig.parameters:
            print("   ✅ _write_bin_statistics accepts sort_by_quantity parameter")
        else:
            print("   ❌ _write_bin_statistics missing sort_by_quantity parameter")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error during ExcelOutputHandler testing: {e}")
        return False


def main():
    """Run all tests for the bin sorting feature"""
    print("Bin Sorting Feature Test Suite")
    print("=" * 60)
    
    # Run tests
    test1 = test_bin_sorting_functionality()
    test2 = test_full_map_processor_integration()
    test3 = test_excel_output_integration()
    
    # Summary
    print("\n" + "=" * 60)
    print("TEST RESULTS SUMMARY")
    print("=" * 60)
    print(f"   Bin Sorting Functionality: {'PASS' if test1 else 'FAIL'}")
    print(f"   FullMapProcessor Integration: {'PASS' if test2 else 'FAIL'}")
    print(f"   ExcelOutputHandler Integration: {'PASS' if test3 else 'FAIL'}")
    
    all_passed = test1 and test2 and test3
    print(f"\nOVERALL RESULT: {'ALL TESTS PASSED' if all_passed else 'SOME TESTS FAILED'}")
    
    if all_passed:
        print("\n🎉 SUCCESS! Bin sorting feature is working correctly!")
        print("✅ Users can now choose between:")
        print("   • Sort by quantity (descending) - default")
        print("   • Sort by bin number (ascending) - A14~AXX from 0 onwards")
        print("✅ All integration points are working properly")
        print("✅ Backward compatibility is maintained")
    else:
        print("\n❌ Some tests failed. Please check the implementation.")
    
    return all_passed


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
