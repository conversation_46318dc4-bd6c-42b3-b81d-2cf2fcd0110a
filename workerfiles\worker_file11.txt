任务流程：
针对Bump map To Tsk map Tool现在针对工具代码检查并完善:
   -----Test House Selection 如果选择Nepes coporation执行nepes处理流程，如果选择其他测试厂，不要执行nepes执行处理逻辑。
   -----map version选择不是GUI选项方式，可以通过读取dummy map自动选择的（复用tsk_map_processor.py代码中parse_file_header函数（self.May_version = self.binary2val(self.get_binary(self.filearray, 52, 52)))
        这个部分可以获取，自动选择是什么map version，来自动选择可以执行的流程
   -----保存路径部分，现在选择output路径时候，默认必须选择文件名。保存路径部分，可以默认自动带出：dummy map名字_modified_时间戳作为名字，格式保存不变吗？也可以手动修改名字。

上述功能开发注意事项：
     --- 其他两个功能不要破坏，代码功能不要破坏
     --- GUI尽量统一，不要占用太多空间
     --- 复用代码部分，尽量做到代码统一

要求：
1，测试脚本和文件请放到test文件夹
2，生成的md说明文档，请放到temp文件夹
3，程序架构统一，框架统一，代码尽量简洁

测试文件说明：
1，可以使用test文件夹下的D97127.09作为bump map的load
2，可以使用test文件夹下的009.NNS157-09-E4作为dummy map的文件