#!/usr/bin/env python3
"""
分析数据写入逻辑问题
检查为什么有很多63但没有规律

Author: Yuribytes
Company: Chipone TE development Team
"""

import os
import sys

# 添加父目录到路径
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, parent_dir)

from nepes_enhanced_processor import NEPESEnhancedProcessor

def analyze_bump_map_data():
    """分析bump map数据"""
    print("🔍 分析Bump Map数据")
    print("=" * 40)
    
    bump_file = "D97127.09"
    
    # 创建NEPES处理器并解析bump map
    processor = NEPESEnhancedProcessor()
    success = processor.parse_bump_map(bump_file)
    
    if not success:
        print("❌ Bump map解析失败")
        return None
    
    # 统计bump map数据
    underscore_count = 0
    zero_count = 0
    xx_count = 0
    other_count = 0
    other_values = {}
    
    total_positions = 0
    
    for row in processor.bump_data:
        for value in row:
            total_positions += 1
            
            if value == "__":
                underscore_count += 1
            elif value == "00":
                zero_count += 1
            elif value in ["XX", "0C"]:  # XX可能显示为0C
                xx_count += 1
            else:
                other_count += 1
                if value not in other_values:
                    other_values[value] = 0
                other_values[value] += 1
    
    print(f"📊 Bump Map统计:")
    print(f"   总位置: {total_positions}")
    print(f"   '__'位置: {underscore_count}")
    print(f"   '00'位置: {zero_count}")
    print(f"   'XX'位置: {xx_count}")
    print(f"   其他值: {other_count}")
    
    if other_values:
        print(f"   其他值详情: {other_values}")
    
    # 验证总数
    expected_total = 2440
    calculated_total = underscore_count + zero_count + xx_count + other_count
    
    print(f"\n🎯 数据验证:")
    print(f"   预期总数: {expected_total}")
    print(f"   实际总数: {calculated_total}")
    print(f"   匹配: {'✅' if calculated_total == expected_total else '❌'}")
    
    return {
        'total': total_positions,
        'underscore': underscore_count,
        'zero': zero_count,
        'xx': xx_count,
        'other': other_count,
        'other_values': other_values
    }

def analyze_processing_logic():
    """分析处理逻辑"""
    print(f"\n🔍 分析处理逻辑")
    print("=" * 40)
    
    bump_file = "D97127.09"
    dummy_file = "009.NNS157-09-E4"
    output_file = "logic_analysis.tsk"
    
    # 读取原始dummy map
    with open(dummy_file, 'rb') as f:
        original_data = f.read()
    
    # 执行NEPES处理
    processor = NEPESEnhancedProcessor()
    success = processor.process_nepes_enhanced(bump_file, dummy_file, output_file)
    
    if not success:
        print("❌ 处理失败")
        return None
    
    # 读取处理后的数据
    with open(output_file, 'rb') as f:
        processed_data = f.read()
    
    # 分析修改的字节
    print(f"📊 字节修改分析:")
    
    total_changes = 0
    bin63_count = 0
    bin59_count = 0
    other_changes = {}
    
    for i in range(0, len(original_data), 4):
        if i + 4 <= len(processed_data):
            original_4bytes = original_data[i:i+4]
            processed_4bytes = processed_data[i:i+4]
            
            if original_4bytes != processed_4bytes:
                total_changes += 1
                
                # 检查是否是预期的值
                processed_hex = processed_4bytes.hex().upper()
                
                if processed_hex == "0000003F":
                    bin63_count += 1
                elif processed_hex == "0000003B":
                    bin59_count += 1
                else:
                    if processed_hex not in other_changes:
                        other_changes[processed_hex] = 0
                    other_changes[processed_hex] += 1
    
    print(f"   总修改位置: {total_changes}")
    print(f"   bin63 (0000003F): {bin63_count}")
    print(f"   bin59 (0000003B): {bin59_count}")
    print(f"   其他修改: {len(other_changes)}")
    
    if other_changes:
        print(f"   其他修改详情:")
        for hex_val, count in other_changes.items():
            print(f"     {hex_val}: {count}次")
    
    # 获取处理统计
    stats = processor.get_processing_stats()
    
    print(f"\n📊 处理器统计:")
    print(f"   '__'位置: {stats['processing_stats']['unchanged_positions']}")
    print(f"   '00'位置: {stats['processing_stats']['pass_positions']}")
    print(f"   'XX'位置: {stats['processing_stats']['fail_positions']}")
    print(f"   二进制修改: {stats['processing_stats']['binary_modifications']}")
    
    # 验证逻辑
    expected_bin63 = stats['processing_stats']['pass_positions']
    expected_bin59 = stats['processing_stats']['fail_positions']
    
    print(f"\n🎯 逻辑验证:")
    print(f"   预期bin63: {expected_bin63}")
    print(f"   实际bin63: {bin63_count}")
    print(f"   bin63匹配: {'✅' if bin63_count == expected_bin63 else '❌'}")
    
    print(f"   预期bin59: {expected_bin59}")
    print(f"   实际bin59: {bin59_count}")
    print(f"   bin59匹配: {'✅' if bin59_count == expected_bin59 else '❌'}")
    
    return {
        'total_changes': total_changes,
        'bin63_count': bin63_count,
        'bin59_count': bin59_count,
        'other_changes': other_changes,
        'stats': stats
    }

def find_pattern_issues():
    """找出模式问题"""
    print(f"\n🔍 查找模式问题")
    print("=" * 40)
    
    bump_file = "D97127.09"
    dummy_file = "009.NNS157-09-E4"
    
    # 创建处理器
    processor = NEPESEnhancedProcessor()
    
    # 加载数据
    processor.load_dummy_map(dummy_file)
    processor.parse_bump_map(bump_file)
    
    print(f"📊 详细位置分析:")
    
    # 检查前10个非"__"位置
    check_count = 0
    for i in range(1, processor.rowsize + 1):
        for j in range(1, processor.columnsize + 1):
            if check_count >= 10:
                break
                
            row_index = i - 1
            col_index = j - 1
            
            if (row_index < len(processor.bump_data) and 
                col_index < len(processor.bump_data[row_index])):
                
                bump_value = processor.bump_data[row_index][col_index]
                
                if bump_value != "__":
                    die_index = (i - 1) * processor.columnsize + j - 1
                    category_pos = processor.TestResultCategory + 4 * die_index - 1
                    
                    display_x = j - 1
                    display_y = i - 1
                    
                    print(f"   位置({display_x},{display_y}): bump='{bump_value}', die_index={die_index}, pos={category_pos}")
                    
                    # 预期的修改
                    if bump_value == "00":
                        expected = "0000003F"
                    elif bump_value in ["XX", "0C"]:
                        expected = "0000003B"
                    else:
                        expected = f"未知值'{bump_value}'"
                    
                    print(f"     预期修改: {expected}")
                    
                    check_count += 1
        
        if check_count >= 10:
            break

def main():
    """主分析函数"""
    print("🧪 数据写入逻辑分析")
    print("=" * 70)
    
    # 切换到测试目录
    os.chdir(os.path.dirname(os.path.abspath(__file__)))
    
    try:
        # 分析1: Bump Map数据
        bump_stats = analyze_bump_map_data()
        
        # 分析2: 处理逻辑
        processing_stats = analyze_processing_logic()
        
        # 分析3: 模式问题
        find_pattern_issues()
        
        print("\n" + "=" * 70)
        print("🎉 分析结果总结:")
        
        if bump_stats and processing_stats:
            print(f"✅ Bump Map数据: {bump_stats['total']}个位置")
            print(f"   '__': {bump_stats['underscore']}, '00': {bump_stats['zero']}, 'XX': {bump_stats['xx']}")
            
            print(f"✅ 处理结果: {processing_stats['total_changes']}个修改")
            print(f"   bin63: {processing_stats['bin63_count']}, bin59: {processing_stats['bin59_count']}")
            
            # 检查是否有问题
            expected_modifications = bump_stats['zero'] + bump_stats['xx']
            actual_modifications = processing_stats['bin63_count'] + processing_stats['bin59_count']
            
            if expected_modifications == actual_modifications:
                print(f"🎯 逻辑正确: 预期{expected_modifications}个修改，实际{actual_modifications}个修改")
            else:
                print(f"❌ 逻辑错误: 预期{expected_modifications}个修改，实际{actual_modifications}个修改")
                print(f"   可能原因: bump map中有其他值或处理逻辑有问题")
        
        return True
            
    except Exception as e:
        print(f"❌ 分析过程出错: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
