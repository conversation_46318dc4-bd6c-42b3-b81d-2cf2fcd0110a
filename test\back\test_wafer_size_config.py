#!/usr/bin/env python3
"""
Test script for wafer size configuration reading from J5 cell
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config_reader import Config<PERSON><PERSON><PERSON>

def test_wafer_size_reading():
    """Test wafer size reading from J5 cell"""
    print("Testing Wafer Size Reading from J5 Cell")
    print("=" * 50)

    # Test the ConfigReader class to ensure it has the wafer_size functionality
    reader = ConfigReader()

    # Check if the wafer_size attribute exists
    if hasattr(reader, 'wafer_size'):
        print("✅ ConfigReader has wafer_size attribute")
    else:
        print("❌ ConfigReader missing wafer_size attribute")
        return

    # Check if get_wafer_size method exists
    if hasattr(reader, 'get_wafer_size'):
        print("✅ ConfigReader has get_wafer_size() method")

        # Test the method
        wafer_size = reader.get_wafer_size()
        print(f"✅ get_wafer_size() returns: '{wafer_size}' (empty string expected for unloaded config)")
    else:
        print("❌ ConfigReader missing get_wafer_size() method")
        return

    # Test with existing config file if available
    config_files = [
        "test/CP1_program_bin.xlsx",
        "CP1_program_bin.xlsx",
        "config.xlsx"
    ]

    config_file = None
    for file_path in config_files:
        if os.path.exists(file_path):
            config_file = file_path
            break

    if not config_file:
        print(f"\n⚠️  No config file found in: {config_files}")
        print("Testing with mock data instead...")
        test_mock_config_reading()
        return
    
    # Create config reader
    reader = ConfigReader()
    
    # Read config file
    print(f"Reading config file: {config_file}")
    if reader.read_config_file(config_file):
        print("✅ Config file loaded successfully")
        
        # Test all the cell readings
        print("\n📊 Configuration Information:")
        print(f"Test Program (A2): '{reader.get_test_program_name()}'")
        print(f"Device Name (D2): '{reader.get_device_name()}'")
        print(f"Vendor Name (F2): '{reader.get_vendor_name()}'")
        print(f"Tester Name (D5): '{reader.get_tester_name()}'")
        print(f"Probe Card No (F5): '{reader.get_probe_card_no()}'")
        print(f"Test Flow (H5): '{reader.get_test_flow()}'")
        print(f"Wafer Size (J5): '{reader.get_wafer_size()}'")  # This is the new functionality
        
        # Check if wafer size was read correctly
        wafer_size = reader.get_wafer_size()
        if wafer_size:
            print(f"\n✅ Wafer Size successfully read from J5: '{wafer_size}'")
        else:
            print(f"\n⚠️  Wafer Size is empty - J5 cell might be empty or not exist")
        
        # Show bin mappings count
        bin_mappings = reader.get_all_bin_mappings()
        print(f"\n📋 Bin Mappings: {len(bin_mappings)} entries")
        
        # Show first few bin mappings as examples
        if bin_mappings:
            print("First few bin mappings:")
            for i, (bin_num, bin_name) in enumerate(list(bin_mappings.items())[:5]):
                print(f"  Bin {bin_num}: {bin_name}")
            if len(bin_mappings) > 5:
                print(f"  ... and {len(bin_mappings) - 5} more")
    else:
        print("❌ Failed to read config file")

def test_mock_config_reading():
    """Test config reading functionality with mock data"""
    print("\n📋 Testing ConfigReader Functionality (Mock Data)")
    print("-" * 50)

    reader = ConfigReader()

    # Manually set some test data to verify the attributes work
    reader.test_program_name = "TEST_PROGRAM"
    reader.device_name = "TEST_DEVICE"
    reader.vendor_name = "TEST_VENDOR"
    reader.tester_name = "TEST_TESTER"
    reader.probe_card_no = "TEST_PROBE_CARD"
    reader.test_flow = "TEST_FLOW"
    reader.wafer_size = "8inch"  # This is what we're testing

    # Test all getter methods
    print(f"Test Program: '{reader.get_test_program_name()}'")
    print(f"Device Name: '{reader.get_device_name()}'")
    print(f"Vendor Name: '{reader.get_vendor_name()}'")
    print(f"Tester Name: '{reader.get_tester_name()}'")
    print(f"Probe Card No: '{reader.get_probe_card_no()}'")
    print(f"Test Flow: '{reader.get_test_flow()}'")
    print(f"Wafer Size: '{reader.get_wafer_size()}'")  # This should show "8inch"

    if reader.get_wafer_size() == "8inch":
        print("✅ Wafer Size getter method works correctly")
    else:
        print("❌ Wafer Size getter method failed")

    print(f"Has Config Loaded: {reader.has_config_loaded()}")

    # Test the _get_cell_value method exists
    if hasattr(reader, '_get_cell_value'):
        print("✅ _get_cell_value method exists")
    else:
        print("❌ _get_cell_value method missing")

def test_tmb_processor_integration():
    """Test TMB processor integration with wafer size"""
    print("\n" + "=" * 50)
    print("Testing TMB Processor Integration")
    print("=" * 50)

    try:
        from tmb_processor import TMBProcessor

        # Create TMB processor
        tmb_processor = TMBProcessor()
        
        # Test with mock config reader
        reader = ConfigReader()
        reader.wafer_size = "8inch"  # Set mock wafer size
        reader.test_program_name = "TEST_PROGRAM"  # Ensure has_config_loaded returns True

        tmb_processor.set_config_reader(reader)
        print("✅ Mock config reader set for TMB processor")

        # Test the generate_middle_part method to see if wafer size is included
        try:
            middle_part = tmb_processor.generate_middle_part()
            print("\n📄 TMB Middle Part (Wafer Info):")
            print("-" * 30)
            print(middle_part)

            # Check if wafer size is included
            if "Wafer Size:" in middle_part:
                wafer_size_line = [line for line in middle_part.split('\n') if 'Wafer Size:' in line][0]
                print(f"\n✅ Wafer Size found in TMB output: {wafer_size_line}")

                if "8inch" in wafer_size_line:
                    print("✅ Correct wafer size value found in TMB output")
                else:
                    print("⚠️  Wafer size value might be empty or incorrect")
            else:
                print("\n❌ Wafer Size not found in TMB output")

        except Exception as e:
            print(f"❌ Error generating TMB middle part: {e}")
            print("This might be expected if TSK processor is not initialized")
            
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("Please ensure all required modules are available.")

if __name__ == "__main__":
    test_wafer_size_reading()
    test_tmb_processor_integration()
