#!/usr/bin/env python3
"""
Test script for worker_file20.md requirements
Tests the modified TMB filename generation
"""

import os
import sys

# Add parent directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from tmb_processor import TMBProcessor
from config_reader import Config<PERSON><PERSON><PERSON>


def test_data_extraction():
    """Test lotid and slotid extraction from MAP file"""
    print("=" * 60)
    print("Testing lotid and slotid extraction...")
    
    tmb_processor = TMBProcessor()
    map_file = "test/019.3AD416-19-F4"
    
    if not os.path.exists(map_file):
        print(f"❌ MAP file not found: {map_file}")
        return False
    
    # Test lotid extraction (地址82，18个bytes，ASCII)
    lotid = tmb_processor.extract_ascii_data(map_file, 82, 18).strip()
    print(f"Lotid (地址82, 18bytes, ASCII): '{lotid}'")
    
    # Test slotid extraction (地址102，2个bytes，十进制)
    slotid = tmb_processor.extract_binary_to_decimal(map_file, 102, 2)
    print(f"Slotid (地址102, 2bytes, 十进制): '{slotid}'")
    
    # Also show waferid for comparison
    waferid = tmb_processor.extract_ascii_data(map_file, 60, 21).strip()
    print(f"Waferid (地址60, 21bytes, ASCII): '{waferid}'")
    
    return True


def test_h5_judgment():
    """Test H5 content judgment logic"""
    print("=" * 60)
    print("Testing H5 judgment logic...")
    
    config_reader = ConfigReader()
    config_file = "test/CP1_program_bin.xlsx"
    
    if not config_reader.read_config_file(config_file):
        print("❌ Failed to load config file")
        return False
    
    h5_content = config_reader.get_test_flow()
    print(f"H5 content from config: '{h5_content}'")
    
    # Test judgment logic
    if h5_content == "CP1":
        h5_number = "1"
        print("✅ H5 judgment: CP1 -> 1")
    elif h5_content == "CP2":
        h5_number = "2"
        print("✅ H5 judgment: CP2 -> 2")
    else:
        h5_number = h5_content
        print(f"⚠️  H5 judgment: {h5_content} -> {h5_content} (not CP1 or CP2)")
    
    return True


def test_new_filename_generation():
    """Test new TMB filename generation format"""
    print("=" * 60)
    print("Testing new TMB filename generation...")
    
    # Setup
    tmb_processor = TMBProcessor()
    config_reader = ConfigReader()
    
    # Load config
    config_file = "test/CP1_program_bin.xlsx"
    if not config_reader.read_config_file(config_file):
        print("❌ Failed to load config file")
        return False
    
    tmb_processor.set_config_reader(config_reader)
    
    # Test with sample MAP file
    map_file = "test/019.3AD416-19-F4"
    if not os.path.exists(map_file):
        print(f"❌ MAP file not found: {map_file}")
        return False
    
    # Generate filename using new format
    tmb_filename = tmb_processor.generate_tmb_filename(map_file)
    filename_only = os.path.basename(tmb_filename)
    
    print(f"Generated filename: {filename_only}")
    
    # Extract components for verification
    lotid = tmb_processor.extract_ascii_data(map_file, 82, 18).strip()
    slotid = tmb_processor.extract_binary_to_decimal(map_file, 102, 2)
    h5_content = config_reader.get_test_flow()
    
    # Expected format: lotid + "_" + H5判断 + slotid + ".tmb"
    h5_number = "1" if h5_content == "CP1" else ("2" if h5_content == "CP2" else h5_content)
    expected_pattern = f"{lotid}_{h5_number}{slotid}.tmb"
    
    print(f"Expected pattern: {expected_pattern}")
    print(f"Components:")
    print(f"  - Lotid: '{lotid}'")
    print(f"  - H5 content: '{h5_content}' -> '{h5_number}'")
    print(f"  - Slotid: '{slotid}'")
    
    # Verify format
    if filename_only == expected_pattern:
        print("✅ Filename format matches expected pattern")
        return True
    else:
        print("❌ Filename format does not match expected pattern")
        return False


def test_tmb_generation_with_new_filename():
    """Test complete TMB generation with new filename"""
    print("=" * 60)
    print("Testing complete TMB generation with new filename...")
    
    # Setup
    tmb_processor = TMBProcessor()
    config_reader = ConfigReader()
    
    # Load config
    config_file = "test/CP1_program_bin.xlsx"
    if not config_reader.read_config_file(config_file):
        print("❌ Failed to load config file")
        return False
    
    tmb_processor.set_config_reader(config_reader)
    
    # Test with sample MAP file
    map_file = "test/019.3AD416-19-F4"
    if not os.path.exists(map_file):
        print(f"❌ MAP file not found: {map_file}")
        return False
    
    # Process MAP file to TMB
    tmb_output = tmb_processor.process_map_to_tmb(map_file)
    
    if tmb_output and os.path.exists(tmb_output):
        filename_only = os.path.basename(tmb_output)
        print(f"✅ TMB file generated: {filename_only}")
        
        # Verify the filename follows new format
        if "_" in filename_only and filename_only.endswith(".tmb"):
            print("✅ Filename has correct structure")
        else:
            print("❌ Filename structure is incorrect")
            return False
        
        # Check file content exists
        with open(tmb_output, 'r', encoding='utf-8') as f:
            content = f.read()
        
        if len(content) > 100:  # Basic content check
            print("✅ TMB file has content")
        else:
            print("❌ TMB file content seems too short")
            return False
        
        print(f"TMB file saved to: {tmb_output}")
        return True
    else:
        print("❌ Failed to generate TMB file")
        return False


def main():
    """Main test function"""
    print("Testing worker_file20.md requirements implementation")
    print("=" * 60)
    
    # Change to script directory
    script_dir = os.path.dirname(os.path.abspath(__file__))
    parent_dir = os.path.dirname(script_dir)
    os.chdir(parent_dir)
    
    print(f"Working directory: {os.getcwd()}")
    
    # Run tests
    tests = [
        ("Data extraction (lotid, slotid)", test_data_extraction),
        ("H5 judgment logic", test_h5_judgment),
        ("New filename generation", test_new_filename_generation),
        ("Complete TMB generation", test_tmb_generation_with_new_filename),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n🧪 Running test: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
            if result:
                print(f"✅ {test_name} PASSED")
            else:
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            print(f"❌ {test_name} ERROR: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 60)
    print("TEST SUMMARY")
    print("=" * 60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! worker_file20.md requirements implemented successfully.")
    else:
        print("⚠️  Some tests failed. Please check the implementation.")


if __name__ == "__main__":
    main()
