#!/usr/bin/env python3
"""
测试Bump Map Advanced Tool的新功能
验证Map Version 2/3/4支持和动态配置功能

Author: Yuribytes
Company: Chipone TE development Team
"""

import os
import sys
import struct

# 添加父目录到路径
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, parent_dir)

from nepes_advanced_processor import NEPESAdvancedProcessor

def test_map_version_4_processing():
    """测试Map Version 4处理 (Big-Endian)"""
    print("🧪 测试Map Version 4处理 (Big-Endian)")
    print("=" * 60)
    
    bump_file = "D97127.09"
    dummy_file = "009.NNS157-09-E4"
    output_file = "advanced_v4_test.tsk"
    
    processor = NEPESAdvancedProcessor()
    
    # 测试自定义值
    pass_value = 75  # 自定义Pass值
    fail_value = 45  # 自定义Fail值
    
    print(f"📊 测试参数:")
    print(f"   Pass Value: {pass_value} (0x{pass_value:02X})")
    print(f"   Fail Value: {fail_value} (0x{fail_value:02X})")
    print(f"   Expected Big-Endian Format:")
    print(f"     Pass: 0000{pass_value:04X}")
    print(f"     Fail: 0000{fail_value:04X}")
    
    success = processor.process_advanced_nepes(
        bump_file, dummy_file, output_file, 
        pass_value=pass_value, fail_value=fail_value
    )
    
    if success:
        # 验证输出文件
        with open(output_file, 'rb') as f:
            data = f.read()
        
        # 检查特定位置的值
        # x=79,y=0 应该是Pass值
        pos_79_0 = data[15364:15368]
        expected_pass = struct.pack('>I', pass_value)
        
        print(f"\n📊 验证结果:")
        print(f"   x=79,y=0 位置数据: {pos_79_0.hex().upper()}")
        print(f"   预期Pass值: {expected_pass.hex().upper()}")
        print(f"   匹配: {'✅' if pos_79_0 == expected_pass else '❌'}")
        
        return pos_79_0 == expected_pass
    
    return False

def test_map_version_2_3_simulation():
    """模拟测试Map Version 2/3处理 (Little-Endian)"""
    print("\n🧪 模拟测试Map Version 2/3处理 (Little-Endian)")
    print("=" * 60)
    
    # 创建模拟的Map Version 2处理器
    processor = NEPESAdvancedProcessor()
    
    # 手动设置map version为2
    processor.map_version = 2
    
    pass_value = 63
    fail_value = 59
    
    print(f"📊 测试参数:")
    print(f"   Map Version: {processor.map_version}")
    print(f"   Pass Value: {pass_value} (0x{pass_value:02X})")
    print(f"   Fail Value: {fail_value} (0x{fail_value:02X})")
    print(f"   Expected Special Format:")
    print(f"     Pass: 00{pass_value:02X}0000")
    print(f"     Fail: 00{fail_value:02X}0000")

    # 测试二进制模式生成
    pass_pattern = processor.get_binary_modification_pattern("00")
    fail_pattern = processor.get_binary_modification_pattern("XX")

    expected_pass = struct.pack('>HH', 0, pass_value)
    expected_fail = struct.pack('>HH', 0, fail_value)
    
    print(f"\n📊 二进制模式验证:")
    print(f"   Pass模式: {pass_pattern.hex().upper()}")
    print(f"   预期Pass: {expected_pass.hex().upper()}")
    print(f"   Pass匹配: {'✅' if pass_pattern == expected_pass else '❌'}")
    
    print(f"   Fail模式: {fail_pattern.hex().upper()}")
    print(f"   预期Fail: {expected_fail.hex().upper()}")
    print(f"   Fail匹配: {'✅' if fail_pattern == expected_fail else '❌'}")
    
    return (pass_pattern == expected_pass and fail_pattern == expected_fail)

def test_output_filename_generation():
    """测试输出文件名生成功能"""
    print("\n🧪 测试输出文件名生成功能")
    print("=" * 60)
    
    processor = NEPESAdvancedProcessor()
    
    # 测试用例
    test_cases = [
        {
            'bump_path': 'test/D97127.09',
            'dummy_path': 'test/009.NNS157-09-E4',
            'expected_pattern': r'D97127-09\.NNS157-09-E4$'
        },
        {
            'bump_path': 'data/bump_map.txt',
            'dummy_path': 'data/dummy.tsk',
            'expected_pattern': r'bump_map\.tsk$'
        }
    ]
    
    import re
    all_passed = True
    
    for i, case in enumerate(test_cases, 1):
        print(f"\n📊 测试用例 {i}:")
        print(f"   Bump Path: {case['bump_path']}")
        print(f"   Dummy Path: {case['dummy_path']}")
        
        output_filename = processor.generate_output_filename(
            case['bump_path'], case['dummy_path']
        )
        
        print(f"   生成文件名: {output_filename}")
        
        # 验证格式
        pattern_match = re.search(case['expected_pattern'], output_filename)
        
        print(f"   格式匹配: {'✅' if pattern_match else '❌'}")
        
        if not pattern_match:
            all_passed = False
    
    return all_passed

def test_dynamic_value_configuration():
    """测试动态值配置功能"""
    print("\n🧪 测试动态值配置功能")
    print("=" * 60)
    
    processor = NEPESAdvancedProcessor()
    
    # 测试不同的配置值
    test_values = [
        {'pass': 100, 'fail': 200},
        {'pass': 0, 'fail': 255},
        {'pass': 128, 'fail': 64}
    ]
    
    all_passed = True
    
    for i, values in enumerate(test_values, 1):
        print(f"\n📊 测试配置 {i}:")
        print(f"   Pass Value: {values['pass']}")
        print(f"   Fail Value: {values['fail']}")
        
        try:
            processor.set_pass_value(values['pass'])
            processor.set_fail_value(values['fail'])
            
            # 验证设置
            if processor.pass_value == values['pass'] and processor.fail_value == values['fail']:
                print(f"   配置成功: ✅")
            else:
                print(f"   配置失败: ❌")
                all_passed = False
                
        except Exception as e:
            print(f"   配置错误: ❌ {e}")
            all_passed = False
    
    # 测试边界值
    print(f"\n📊 边界值测试:")
    
    try:
        processor.set_pass_value(256)  # 应该失败
        print(f"   256值测试: ❌ (应该失败但成功了)")
        all_passed = False
    except ValueError:
        print(f"   256值测试: ✅ (正确拒绝)")
    
    try:
        processor.set_fail_value(-1)  # 应该失败
        print(f"   -1值测试: ❌ (应该失败但成功了)")
        all_passed = False
    except ValueError:
        print(f"   -1值测试: ✅ (正确拒绝)")
    
    return all_passed

def test_comprehensive_processing():
    """综合处理测试"""
    print("\n🧪 综合处理测试")
    print("=" * 60)
    
    bump_file = "D97127.09"
    dummy_file = "009.NNS157-09-E4"
    
    if not (os.path.exists(bump_file) and os.path.exists(dummy_file)):
        print("❌ 测试文件不存在，跳过综合测试")
        return True
    
    processor = NEPESAdvancedProcessor()
    
    # 使用自定义值进行完整处理
    custom_pass = 88
    custom_fail = 77
    
    output_file = processor.generate_output_filename(bump_file, dummy_file)
    
    print(f"📊 综合测试参数:")
    print(f"   Bump File: {bump_file}")
    print(f"   Dummy File: {dummy_file}")
    print(f"   Output File: {output_file}")
    print(f"   Custom Pass: {custom_pass}")
    print(f"   Custom Fail: {custom_fail}")
    
    success = processor.process_advanced_nepes(
        bump_file, dummy_file, output_file,
        pass_value=custom_pass, fail_value=custom_fail
    )
    
    if success:
        stats = processor.get_processing_stats()
        print(f"\n📊 处理统计:")
        print(f"   Map Version: {stats['map_version']}")
        print(f"   Pass Positions: {stats['processing_stats']['pass_positions']}")
        print(f"   Fail Positions: {stats['processing_stats']['fail_positions']}")
        print(f"   Binary Modifications: {stats['processing_stats']['binary_modifications']}")
        
        # 验证输出文件存在
        if os.path.exists(output_file):
            file_size = os.path.getsize(output_file)
            print(f"   输出文件大小: {file_size} bytes")
            print(f"   综合测试: ✅")
            return True
        else:
            print(f"   输出文件不存在: ❌")
            return False
    else:
        print(f"   处理失败: ❌")
        return False

def main():
    """主测试函数"""
    print("🧪 Bump Map Advanced Tool 功能测试")
    print("=" * 70)
    
    # 切换到测试目录
    os.chdir(os.path.dirname(os.path.abspath(__file__)))
    
    test_results = []
    
    try:
        # 测试1: Map Version 4处理
        result1 = test_map_version_4_processing()
        test_results.append(("Map Version 4处理", result1))
        
        # 测试2: Map Version 2/3模拟
        result2 = test_map_version_2_3_simulation()
        test_results.append(("Map Version 2/3模拟", result2))
        
        # 测试3: 输出文件名生成
        result3 = test_output_filename_generation()
        test_results.append(("输出文件名生成", result3))
        
        # 测试4: 动态值配置
        result4 = test_dynamic_value_configuration()
        test_results.append(("动态值配置", result4))
        
        # 测试5: 综合处理
        result5 = test_comprehensive_processing()
        test_results.append(("综合处理", result5))
        
        # 总结
        print("\n" + "=" * 70)
        print("🎉 测试结果总结:")
        
        passed = 0
        for test_name, result in test_results:
            status = "✅ 通过" if result else "❌ 失败"
            print(f"   {test_name}: {status}")
            if result:
                passed += 1
        
        print(f"\n📊 总体结果: {passed}/{len(test_results)} 测试通过")
        
        if passed == len(test_results):
            print("🎯 所有功能测试通过！Bump Map Advanced Tool准备就绪！")
        else:
            print("⚠️  部分测试失败，需要进一步调试")
        
        return passed == len(test_results)
        
    except Exception as e:
        print(f"❌ 测试过程出错: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
