# TSK/MAP File Processor Tool (GUI Version)

## Version Information
- Version: 1.0.0
- Author: <PERSON><PERSON><PERSON>
- Company: Chipone TE development Team
- Architecture: 64bit
- Built on: 2025-09-09 10:20:08
- Python Version: 3.11.9

## Features
- Pure GUI application (no console window)
- Process TSK and MAP files
- AB comparison analysis with 6 detailed sheets:
  * Summary: Bin difference statistics
  * Correlation: Bin jump analysis matrix
  * Map_compare: Position-by-position comparison (Different first, Same last)
  * Map_compare_full: Combined format comparison (most intuitive view)
  * Amap/Bmap: Original data sheets
  * Setup: Configuration information
- Support for file rotation (0°, 90°, 180°, 270°)
- Professional Excel reports with color coding
- User-friendly graphical interface

## Quick Start
1. Double-click TSK_MAP_Tool.exe to start the application
2. Select your desired tool:
   - AB Map Tool: For single file processing and comparison analysis (recommended)
   - Full Map Tool: For batch processing
3. For AB comparison:
   - Choose "AB Compare" mode
   - Select your Amap and Bmap files
   - Configure rotation angle if needed
   - Click PROCESS to generate analysis report
4. Check the Map_compare_full sheet for the most intuitive comparison view

## Color Coding in Map_compare_full Sheet
- 🟢 Green: Same bin values, both Pass (ideal state)
- 🔴 Red: Same bin values, both Fail (consistent failure)
- 🟠 Orange: Same bin values, mixed Pass/Fail (needs attention)
- 🟡 Yellow: Different bin values (requires investigation)
- 🔵 Light Blue: Only Amap has data
- 🟨 Light Yellow: Only Bmap has data

## System Requirements
- Windows 7/10/11 (64bit)
- Minimum 4GB RAM recommended
- 1GB free disk space

## Usage Tips
1. The application runs in pure GUI mode without console window
2. For best results, use files from the same test batch
3. The Map_compare_full sheet provides the most intuitive comparison
4. Green cells indicate perfect matching, yellow cells need attention
5. Use the Summary sheet for overall statistics

## Technical Information
- Standalone executable with all dependencies included
- No Python installation required
- Built with PyInstaller for maximum compatibility
- Optimized for Windows systems

## Support
This is a professional tool developed by Yuribytes for Chipone TE development Team.
For technical support, please contact the development team.

## Copyright
Copyright (C) 2025 Yuribytes
All rights reserved.
