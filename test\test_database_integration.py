#!/usr/bin/env python3
"""
Test script for database integration
Tests the complete database-driven configuration system
"""

import os
import sys

# Add parent directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database_config_reader import DatabaseConfigReader
from config_database_manager import ConfigDatabaseManager
from tmb_processor import TMBProcessor


def test_database_migration():
    """Test migrating Excel configuration to database"""
    print("=" * 60)
    print("Testing Excel to Database Migration...")
    
    # Initialize database reader
    db_reader = DatabaseConfigReader("test_config.db")
    
    # Test migration from Excel
    excel_file = "test/CP1_program_bin.xlsx"
    if not os.path.exists(excel_file):
        print(f"❌ Excel file not found: {excel_file}")
        return False
    
    # Migrate Excel to database
    if db_reader.migrate_from_excel(excel_file, "CP1_Test_Config"):
        print("✅ Migration successful")
        
        # Verify migration by loading the config
        if db_reader.load_config_by_name("CP1_Test_Config"):
            print(f"✅ Verification successful:")
            print(f"   Test Program: {db_reader.get_test_program_name()}")
            print(f"   Device Name: {db_reader.get_device_name()}")
            print(f"   Test Flow: {db_reader.get_test_flow()}")
            print(f"   Bin Mappings: {len(db_reader.get_all_bin_mappings())}")
            return True
        else:
            print("❌ Verification failed")
            return False
    else:
        print("❌ Migration failed")
        return False


def test_auto_config_selection():
    """Test automatic configuration selection"""
    print("=" * 60)
    print("Testing Automatic Configuration Selection...")
    
    db_reader = DatabaseConfigReader("test_config.db")
    
    # Test auto-selection with filters
    filters = {
        'device_name': 'ICNA3509WAA-P1-P',
        'test_flow': 'CP1',
        'vendor_name': 'CHANGHOU'
    }
    
    print(f"Searching with filters: {filters}")
    
    if db_reader.load_config_auto(filters):
        print("✅ Auto-selection successful")
        print(f"   Selected Config: {db_reader.get_test_program_name()}")
        print(f"   Device: {db_reader.get_device_name()}")
        print(f"   Test Flow: {db_reader.get_test_flow()}")
        print(f"   Vendor: {db_reader.get_vendor_name()}")
        return True
    else:
        print("❌ Auto-selection failed")
        return False


def test_tmb_with_database():
    """Test TMB generation with database configuration"""
    print("=" * 60)
    print("Testing TMB Generation with Database Configuration...")
    
    # Initialize TMB processor with database support
    tmb_processor = TMBProcessor(use_database=True, db_path="test_config.db")
    
    # Load configuration from database
    auto_filters = {
        'device_name': 'ICNA3509WAA-P1-P',
        'test_flow': 'CP1'
    }
    
    if not tmb_processor.load_config_from_database(auto_filters=auto_filters):
        print("❌ Failed to load database configuration")
        return False
    
    # Test with sample MAP file
    map_file = "test/back/019.3AD416-19-F4"
    if not os.path.exists(map_file):
        print(f"❌ MAP file not found: {map_file}")
        return False
    
    # Process MAP file to TMB
    tmb_output = tmb_processor.process_map_to_tmb(map_file)
    
    if tmb_output and os.path.exists(tmb_output):
        filename_only = os.path.basename(tmb_output)
        print(f"✅ TMB file generated with database config: {filename_only}")
        
        # Verify TMB content
        with open(tmb_output, 'r', encoding='utf-8') as f:
            content = f.read()
        
        if len(content) > 100:
            print("✅ TMB file has valid content")
            
            # Check for database-loaded configuration data
            if "ICNA3509WAA-P1-P" in content:
                print("✅ Database configuration data found in TMB")
            else:
                print("⚠️  Database configuration data not found in TMB")
            
            return True
        else:
            print("❌ TMB file content seems invalid")
            return False
    else:
        print("❌ Failed to generate TMB file")
        return False


def test_config_management():
    """Test configuration management operations"""
    print("=" * 60)
    print("Testing Configuration Management...")
    
    manager = ConfigDatabaseManager("test_config.db")
    
    # List available configurations
    configs = manager.list_configs_detailed()
    print(f"Available configurations: {len(configs)}")
    
    for config in configs:
        print(f"  - {config['config_name']}: {config['device_name']} "
              f"({config['bin_count']} bins, used {config['usage_count']} times)")
    
    if configs:
        # Test export/import
        test_config = configs[0]
        export_file = "test_config_export.json"
        
        print(f"\nTesting export of '{test_config['config_name']}'...")
        if manager.export_config_to_json(test_config['id'], export_file):
            print("✅ Export successful")
            
            # Test import
            print("Testing import...")
            new_config_id = manager.import_config_from_json(export_file)
            if new_config_id > 0:
                print(f"✅ Import successful, new config ID: {new_config_id}")
                
                # Clean up
                os.remove(export_file)
                return True
            else:
                print("❌ Import failed")
                return False
        else:
            print("❌ Export failed")
            return False
    else:
        print("⚠️  No configurations available for testing")
        return True


def test_usage_statistics():
    """Test usage statistics"""
    print("=" * 60)
    print("Testing Usage Statistics...")
    
    manager = ConfigDatabaseManager("test_config.db")
    
    stats = manager.get_usage_statistics()
    
    print("Most Used Configurations:")
    for item in stats['most_used'][:5]:
        print(f"  {item['config_name']}: {item['usage_count']} times")
    
    print("\nRecent Usage:")
    for item in stats['recent_usage'][:5]:
        print(f"  {item['config_name']} - {item['used_at']} ({item['context']})")
    
    return True


def test_performance_comparison():
    """Compare performance between Excel and Database loading"""
    print("=" * 60)
    print("Testing Performance Comparison...")
    
    import time
    
    # Test Excel loading time
    from config_reader import ConfigReader
    excel_reader = ConfigReader()
    
    start_time = time.time()
    excel_success = excel_reader.read_config_file("test/CP1_program_bin.xlsx")
    excel_time = time.time() - start_time
    
    if excel_success:
        print(f"✅ Excel loading time: {excel_time:.4f} seconds")
    else:
        print("❌ Excel loading failed")
        return False
    
    # Test database loading time
    db_reader = DatabaseConfigReader("test_config.db")
    
    start_time = time.time()
    db_success = db_reader.load_config_by_name("CP1_Test_Config")
    db_time = time.time() - start_time
    
    if db_success:
        print(f"✅ Database loading time: {db_time:.4f} seconds")
        
        # Compare results
        speedup = excel_time / db_time if db_time > 0 else float('inf')
        print(f"Database is {speedup:.2f}x faster than Excel")
        
        return True
    else:
        print("❌ Database loading failed")
        return False


def main():
    """Main test function"""
    print("Testing Database Integration for Configuration Management")
    print("=" * 60)
    
    # Change to script directory
    script_dir = os.path.dirname(os.path.abspath(__file__))
    parent_dir = os.path.dirname(script_dir)
    os.chdir(parent_dir)
    
    print(f"Working directory: {os.getcwd()}")
    
    # Run tests
    tests = [
        ("Database Migration", test_database_migration),
        ("Auto Config Selection", test_auto_config_selection),
        ("TMB with Database", test_tmb_with_database),
        ("Config Management", test_config_management),
        ("Usage Statistics", test_usage_statistics),
        ("Performance Comparison", test_performance_comparison),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n🧪 Running test: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
            if result:
                print(f"✅ {test_name} PASSED")
            else:
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            print(f"❌ {test_name} ERROR: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 60)
    print("DATABASE INTEGRATION TEST SUMMARY")
    print("=" * 60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All database integration tests passed!")
        print("\nDatabase configuration system is ready for use:")
        print("  - Migrate Excel files with: python config_database_manager.py migrate")
        print("  - Use TMBProcessor with use_database=True")
        print("  - Auto-select configs with filters")
        print("  - Manage configs with config_database_manager.py")
    else:
        print("⚠️  Some tests failed. Please check the implementation.")


if __name__ == "__main__":
    main()
