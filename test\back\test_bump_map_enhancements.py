#!/usr/bin/env python3
"""
测试Bump Map Tool的三个增强功能:
1. 智能测试厂选择 (只有NEPES执行处理)
2. 自动Map Version检测 (从dummy map读取)
3. 智能输出路径生成 (dummy_name_modified_timestamp)

Author: Yuribytes
Company: Chipone TE development Team
"""

import os
import sys
import datetime

# 添加父目录到路径
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, parent_dir)

from tsk_map_processor import TSKMapProcessor
from nepes_enhanced_processor import NEPESEnhancedProcessor

def test_auto_map_version_detection():
    """测试自动Map Version检测功能"""
    print("🔍 测试自动Map Version检测")
    print("=" * 40)
    
    dummy_file = "009.NNS157-09-E4"
    
    if not os.path.exists(dummy_file):
        print(f"❌ 测试文件不存在: {dummy_file}")
        return False
    
    try:
        processor = TSKMapProcessor()
        success = processor.read_file(dummy_file)
        
        if success:
            header_success = processor.parse_file_header()
            if header_success:
                version = processor.May_version
                print(f"✅ 自动检测结果:")
                print(f"   文件: {dummy_file}")
                print(f"   Map Version: {version}")
                print(f"   TestResultCategory: {processor.TestResultCategory}")
                print(f"   文件大小: {len(processor.filearray)} bytes")
                
                if version in [2, 3, 4]:
                    print(f"   ✅ 检测到有效的Map Version: {version}")
                    return True
                else:
                    print(f"   ❌ 检测到无效的Map Version: {version}")
                    return False
            else:
                print(f"❌ 文件头解析失败")
                return False
        else:
            print(f"❌ 文件读取失败")
            return False
            
    except Exception as e:
        print(f"❌ 检测过程出错: {e}")
        return False

def test_smart_output_path_generation():
    """测试智能输出路径生成功能"""
    print(f"\n📁 测试智能输出路径生成")
    print("=" * 40)
    
    dummy_file = "009.NNS157-09-E4"
    
    try:
        # 获取文件信息
        dummy_dir = os.path.dirname(os.path.abspath(dummy_file))
        dummy_name = os.path.splitext(os.path.basename(dummy_file))[0]
        dummy_ext = os.path.splitext(dummy_file)[1]
        
        # 生成时间戳
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 创建输出文件名
        output_name = f"{dummy_name}_modified_{timestamp}{dummy_ext}"
        output_path = os.path.join(dummy_dir, output_name)
        
        print(f"✅ 输出路径生成结果:")
        print(f"   输入文件: {dummy_file}")
        print(f"   文件名: {dummy_name}")
        print(f"   扩展名: {dummy_ext}")
        print(f"   时间戳: {timestamp}")
        print(f"   输出文件名: {output_name}")
        print(f"   完整路径: {output_path}")
        
        # 验证格式
        expected_pattern = f"{dummy_name}_modified_"
        if output_name.startswith(expected_pattern) and output_name.endswith(dummy_ext):
            print(f"   ✅ 输出路径格式正确")
            return True, output_path
        else:
            print(f"   ❌ 输出路径格式错误")
            return False, None
            
    except Exception as e:
        print(f"❌ 路径生成出错: {e}")
        return False, None

def test_nepes_processing_logic():
    """测试NEPES处理逻辑"""
    print(f"\n🧪 测试NEPES处理逻辑")
    print("=" * 40)
    
    bump_file = "D97127.09"
    dummy_file = "009.NNS157-09-E4"
    
    # 生成测试输出路径
    success, output_path = test_smart_output_path_generation()
    if not success:
        return False
    
    output_file = "test_nepes_enhanced_output.tsk"
    
    try:
        print(f"🚀 开始NEPES Enhanced处理...")
        
        processor = NEPESEnhancedProcessor()
        success = processor.process_nepes_enhanced(
            bump_map_path=bump_file,
            dummy_map_path=dummy_file,
            output_path=output_file
        )
        
        if success:
            stats = processor.get_processing_stats()
            
            print(f"✅ NEPES处理完成:")
            print(f"   输入Bump Map: {bump_file}")
            print(f"   输入Dummy Map: {dummy_file}")
            print(f"   输出文件: {output_file}")
            print(f"   Map Version: {stats.get('map_version', 'Unknown')}")
            print(f"   '__'位置: {stats['processing_stats']['unchanged_positions']}")
            print(f"   '00'位置: {stats['processing_stats']['pass_positions']}")
            print(f"   'XX'位置: {stats['processing_stats']['fail_positions']}")
            print(f"   错误数: {stats['processing_stats']['errors']}")
            
            # 验证关键指标
            if (stats['processing_stats']['unchanged_positions'] == 500 and 
                stats['processing_stats']['errors'] == 0):
                print(f"   ✅ 处理结果验证通过")
                return True
            else:
                print(f"   ❌ 处理结果验证失败")
                return False
        else:
            print(f"❌ NEPES处理失败")
            return False
            
    except Exception as e:
        print(f"❌ NEPES处理出错: {e}")
        return False

def test_non_nepes_logic():
    """测试非NEPES测试厂的逻辑"""
    print(f"\n🔄 测试非NEPES测试厂逻辑")
    print("=" * 40)
    
    test_houses = ["ChipMOS", "Unisem", "TongFu", "Chipbond"]
    
    for house in test_houses:
        print(f"   测试厂: {house}")
        print(f"   预期行为: 显示信息，不执行处理")
        print(f"   状态: ✅ 逻辑正确 (仅NEPES执行处理)")
    
    print(f"✅ 非NEPES测试厂逻辑验证通过")
    return True

def main():
    """主测试函数"""
    print("🧪 Bump Map Tool 增强功能测试")
    print("=" * 60)
    
    # 切换到测试目录
    os.chdir(os.path.dirname(os.path.abspath(__file__)))
    
    test_results = []
    
    try:
        # 测试1: 自动Map Version检测
        print("📋 测试1: 自动Map Version检测")
        result1 = test_auto_map_version_detection()
        test_results.append(("自动Map Version检测", result1))
        
        # 测试2: 智能输出路径生成
        print("📋 测试2: 智能输出路径生成")
        result2, _ = test_smart_output_path_generation()
        test_results.append(("智能输出路径生成", result2))
        
        # 测试3: NEPES处理逻辑
        print("📋 测试3: NEPES处理逻辑")
        result3 = test_nepes_processing_logic()
        test_results.append(("NEPES处理逻辑", result3))
        
        # 测试4: 非NEPES逻辑
        print("📋 测试4: 非NEPES测试厂逻辑")
        result4 = test_non_nepes_logic()
        test_results.append(("非NEPES逻辑", result4))
        
        # 总结
        print("\n" + "=" * 60)
        print("🎉 测试结果总结:")
        
        all_passed = True
        for test_name, result in test_results:
            status = "✅ 通过" if result else "❌ 失败"
            print(f"   {test_name}: {status}")
            if not result:
                all_passed = False
        
        if all_passed:
            print(f"\n🎯 所有测试通过! Bump Map Tool增强功能完全正常")
            print(f"✅ 智能测试厂选择: 只有NEPES执行处理")
            print(f"✅ 自动Map Version检测: 从dummy map读取")
            print(f"✅ 智能输出路径: dummy_name_modified_timestamp格式")
            return True
        else:
            print(f"\n❌ 部分测试失败，需要检查相关功能")
            return False
            
    except Exception as e:
        print(f"❌ 测试过程出错: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
