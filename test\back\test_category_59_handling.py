#!/usr/bin/env python3
"""
Test Category 59 Handling - Verify category 59 dies are marked as red (fail)
"""

import sys
import os
sys.path.append('..')
from tsk_map_processor import TSKMapProcessor


def test_category_59_logic():
    """
    Test the category 59 special handling logic
    """
    print("Testing Category 59 Special Handling Logic")
    print("=" * 50)
    
    processor = TSKMapProcessor()
    
    # Initialize test data
    processor.rowsize = 3
    processor.columnsize = 3
    processor.RefdieX = 0
    processor.RefdieY = 0
    processor.map_data = [[[0, 0] for _ in range(3)] for _ in range(3)]
    
    # Test cases: (x, y, category, is_test_die, is_pass_die, expected_color)
    test_cases = [
        (0, 0, 10, True, True, 4),   # Normal pass die -> green (4)
        (1, 0, 20, True, False, 3),  # Normal fail die -> red (3)
        (2, 0, 59, True, True, 3),   # Category 59 pass die -> red (3) - SPECIAL CASE
        (0, 1, 59, True, False, 3),  # Category 59 fail die -> red (3)
        (1, 1, 30, True, True, 4),   # Another normal pass die -> green (4)
    ]
    
    print("Test cases:")
    print("Format: (x, y, category, is_test_die, is_pass_die) -> expected_color")
    
    for i, (x, y, category, is_test_die, is_pass_die, expected_color) in enumerate(test_cases):
        print(f"\nTest {i+1}: ({x}, {y}, cat={category}, test={is_test_die}, pass={is_pass_die})")
        
        # Simulate the processing logic
        if is_test_die:
            map_row = y - processor.RefdieY
            map_col = x - processor.RefdieX
            
            if 0 <= map_row < processor.rowsize and 0 <= map_col < processor.columnsize:
                processor.map_data[map_row][map_col][0] = category
                
                # Apply the new logic
                if category == 59:
                    processor.map_data[map_row][map_col][1] = 3  # Force fail color for category 59
                elif is_pass_die:
                    processor.map_data[map_row][map_col][1] = 4  # Pass color (green)
                else:
                    processor.map_data[map_row][map_col][1] = 3  # Fail color (red)
                
                actual_color = processor.map_data[map_row][map_col][1]
                color_name = "Green (Pass)" if actual_color == 4 else "Red (Fail)"
                expected_name = "Green (Pass)" if expected_color == 4 else "Red (Fail)"
                
                if actual_color == expected_color:
                    print(f"  ✅ Result: {color_name} (Expected: {expected_name})")
                else:
                    print(f"  ❌ Result: {color_name} (Expected: {expected_name})")
                
                # Special note for category 59
                if category == 59:
                    print(f"  📝 Category 59 special rule: Always red, regardless of pass/fail status")
    
    # Display final map
    print(f"\nFinal map data (category, color):")
    print("Colors: 3=Red(Fail), 4=Green(Pass), 0=Empty")
    for i in range(processor.rowsize):
        row_display = []
        for j in range(processor.columnsize):
            cat = processor.map_data[i][j][0]
            color = processor.map_data[i][j][1]
            if color == 0:
                row_display.append("  Empty  ")
            else:
                color_char = "R" if color == 3 else "G"
                row_display.append(f"({cat:2d},{color_char})")
        print(f"  Row {i}: {' '.join(row_display)}")


def test_with_real_file(filepath):
    """
    Test category 59 handling with real TSK file
    """
    print(f"\nTesting Category 59 with Real File: {os.path.basename(filepath)}")
    print("=" * 60)
    
    if not os.path.exists(filepath):
        print(f"❌ File not found: {filepath}")
        return False
    
    processor = TSKMapProcessor()
    
    # Read and parse file
    if not processor.read_file(filepath):
        print("❌ Failed to read file")
        return False
    
    if not processor.parse_file_header():
        print("❌ Failed to parse header")
        return False
    
    print(f"✅ File parsed: {processor.columnsize}x{processor.rowsize}, Version {processor.May_version}")
    
    # Process die data
    if not processor.process_die_data():
        print("❌ Failed to process die data")
        return False
    
    # Search for category 59 dies
    category_59_count = 0
    category_59_dies = []
    
    for i in range(processor.rowsize):
        for j in range(processor.columnsize):
            category = processor.map_data[i][j][0]
            color = processor.map_data[i][j][1]
            
            if category == 59:
                category_59_count += 1
                color_name = "Red" if color == 3 else "Green" if color == 4 else "None"
                category_59_dies.append((i, j, category, color, color_name))
    
    print(f"\nCategory 59 Analysis:")
    print(f"  Found {category_59_count} dies with category 59")
    
    if category_59_count > 0:
        print(f"  Category 59 dies details:")
        for i, (row, col, cat, color, color_name) in enumerate(category_59_dies[:10]):  # Show first 10
            print(f"    Die ({row+1},{col+1}): Category {cat}, Color {color} ({color_name})")
            if color != 3:
                print(f"      ⚠️  WARNING: Category 59 die should be Red (3), but is {color}")
        
        if len(category_59_dies) > 10:
            print(f"    ... and {len(category_59_dies) - 10} more")
        
        # Check if all category 59 dies are red
        all_red = all(color == 3 for _, _, _, color, _ in category_59_dies)
        if all_red:
            print(f"  ✅ All category 59 dies are correctly marked as Red (Fail)")
        else:
            red_count = sum(1 for _, _, _, color, _ in category_59_dies if color == 3)
            print(f"  ❌ Only {red_count}/{category_59_count} category 59 dies are Red")
    else:
        print(f"  📝 No category 59 dies found in this file")
    
    # Show overall statistics
    total_dies = 0
    red_dies = 0
    green_dies = 0
    
    for i in range(processor.rowsize):
        for j in range(processor.columnsize):
            color = processor.map_data[i][j][1]
            if color != 0:
                total_dies += 1
                if color == 3:
                    red_dies += 1
                elif color == 4:
                    green_dies += 1
    
    print(f"\nOverall Statistics:")
    print(f"  Total tested dies: {total_dies}")
    print(f"  Red (Fail) dies: {red_dies}")
    print(f"  Green (Pass) dies: {green_dies}")
    print(f"  Category 59 dies: {category_59_count} (all should be Red)")
    
    return True


def main():
    """
    Main test function
    """
    print("Category 59 Handling Test")
    print("Verifying category 59 dies are always marked as Red (Fail)")
    print("=" * 70)
    
    # Test 1: Logic test
    test_category_59_logic()
    
    # Test 2: Real file test
    if len(sys.argv) >= 2:
        filepath = sys.argv[1]
        test_with_real_file(filepath)
    else:
        print(f"\nSkipping real file test (no file provided)")
        print(f"Usage: python test_category_59_handling.py <tsk_file_path>")
    
    print("\n" + "=" * 70)
    print("Category 59 Test Completed!")
    print("Key points:")
    print("- Category 59 dies are ALWAYS marked as Red (Fail)")
    print("- This overrides the normal pass/fail status")
    print("- Other categories follow normal pass/fail logic")


if __name__ == "__main__":
    main()
