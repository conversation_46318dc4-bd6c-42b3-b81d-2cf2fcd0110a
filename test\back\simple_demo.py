#!/usr/bin/env python3
"""
Simple demo script for new features - runs in test directory
Avoids Unicode issues and generates all files in test folder
"""

import os
import sys
import tempfile

# Add parent directory to path to import modules
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from full_map_processor import FullMapProcessor


def demo_output_folder_selection():
    """Demonstrate output folder selection functionality"""
    print("Demo: Output Folder Selection")
    print("-" * 40)
    
    # Create a demo output folder in current directory (test)
    demo_folder = "demo_output"
    os.makedirs(demo_folder, exist_ok=True)
    
    print(f"Created demo output folder: {demo_folder}")
    
    # Test file
    test_file = "3AC468-01-C5.map"
    if not os.path.exists(test_file):
        print(f"Test file not found: {test_file}")
        return False
    
    try:
        # Create Full Map Processor with output folder
        processor = FullMapProcessor()
        processor.set_rotation_angle(90)  # Test with rotation
        processor.set_filter_empty(True)
        processor.set_output_folder(demo_folder)  # Set custom output folder
        
        print(f"Processing file: {test_file}")
        print(f"Rotation angle: 90 degrees (R90)")
        print(f"Output folder: {demo_folder}")
        
        # Process file
        result = processor.process_multiple_files([test_file])
        
        if result:
            print(f"Success! File created: {result}")
            
            # Verify file location
            if result.startswith(demo_folder):
                print("File created in specified output folder")
                
                # Show file info
                if os.path.exists(result):
                    file_size = os.path.getsize(result)
                    print(f"File size: {file_size:,} bytes ({file_size/1024:.1f} KB)")
                    
                    # Check filename contains rotation info
                    if "_R90_" in os.path.basename(result):
                        print("Filename contains rotation angle (R90)")
                    
                    return True
            else:
                print(f"File not in expected folder: {result}")
        else:
            print("Failed to process file")
            
    except Exception as e:
        print(f"Error: {e}")
        return False
    
    return False


def demo_bin_summary_sheet():
    """Demonstrate Bin_Summary sheet functionality"""
    print("\nDemo: Bin_Summary Sheet")
    print("-" * 40)
    
    test_file = "3AC468-01-C5.map"
    if not os.path.exists(test_file):
        print(f"Test file not found: {test_file}")
        return False
    
    try:
        # Create Full Map Processor
        processor = FullMapProcessor()
        processor.set_rotation_angle(0)
        processor.set_filter_empty(True)
        processor.set_output_folder(".")  # Current directory (test)
        
        print(f"Processing file: {test_file}")
        print("Creating Bin_Summary sheet...")
        
        # Process file
        result = processor.process_multiple_files([test_file])
        
        if result and os.path.exists(result):
            print(f"Excel file created: {result}")
            
            # Examine the Excel file
            from openpyxl import load_workbook
            wb = load_workbook(result)
            
            print(f"Total sheets: {len(wb.sheetnames)}")
            print(f"Sheet names: {wb.sheetnames}")
            
            # Check Bin_Summary sheet
            if "Bin_Summary" in wb.sheetnames:
                if wb.sheetnames[0] == "Bin_Summary":
                    print("Bin_Summary is the first sheet (as required)")
                else:
                    print(f"Bin_Summary exists but not first. Position: {wb.sheetnames.index('Bin_Summary') + 1}")
                
                # Examine Bin_Summary content
                ws = wb["Bin_Summary"]
                
                print("\nBin_Summary Sheet Structure:")
                print("   Row 5: Headers")
                
                # Show headers
                headers = []
                for col in range(1, 11):  # Show first 10 columns
                    header = ws.cell(row=5, column=col).value
                    if header:
                        headers.append(str(header))
                
                print(f"   Headers (first 10): {', '.join(headers)}")
                
                # Show data rows
                print("   Row 6+: Data")
                for row in range(6, 10):  # Check first few data rows
                    sheet_name = ws.cell(row=row, column=1).value
                    yield_val = ws.cell(row=row, column=2).value
                    
                    if sheet_name:
                        if isinstance(yield_val, (int, float)):
                            yield_percent = f"{yield_val:.2%}" if yield_val <= 1 else f"{yield_val:.2f}%"
                        else:
                            yield_percent = str(yield_val)
                        
                        print(f"   Row {row}: {sheet_name} | Yield: {yield_percent}")
                        
                        # Show some bin data
                        bin_data = []
                        for col in range(3, 8):  # Show first 5 bin columns
                            bin_val = ws.cell(row=row, column=col).value
                            bin_data.append(str(bin_val) if bin_val is not None else "0")
                        
                        print(f"           Bins C00-C04: {', '.join(bin_data)}")
                    elif ws.cell(row=row, column=1).value == "Average":
                        print(f"   Row {row}: Average row found")
                        avg_yield = ws.cell(row=row, column=2).value
                        if isinstance(avg_yield, (int, float)):
                            avg_percent = f"{avg_yield:.2%}" if avg_yield <= 1 else f"{avg_yield:.2f}%"
                            print(f"           Average Yield: {avg_percent}")
                        break
                
                wb.close()
                return True
            else:
                print("Bin_Summary sheet not found")
                wb.close()
                return False
                
        else:
            print("Failed to create Excel file")
            return False
            
    except Exception as e:
        print(f"Error: {e}")
        return False


def main():
    """Run all demos"""
    print("New Features Demo")
    print("=" * 60)
    print("Demonstrating:")
    print("1. Output folder selection")
    print("2. Bin_Summary sheet functionality")
    print("=" * 60)
    
    # Change to test directory
    test_dir = os.path.dirname(os.path.abspath(__file__))
    os.chdir(test_dir)
    print(f"Working directory: {os.getcwd()}")
    
    # Run demos
    demo1 = demo_output_folder_selection()
    demo2 = demo_bin_summary_sheet()
    
    # Summary
    print("\n" + "=" * 60)
    print("Demo Results:")
    print(f"   Output Folder Selection: {'PASS' if demo1 else 'FAIL'}")
    print(f"   Bin_Summary Sheet: {'PASS' if demo2 else 'FAIL'}")
    
    all_success = demo1 and demo2
    print(f"\nOverall: {'ALL DEMOS SUCCESSFUL' if all_success else 'SOME DEMOS FAILED'}")
    
    if all_success:
        print("\nAll new features are working correctly!")
        print("Check the demo_output folder for generated files")
    
    return all_success


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
