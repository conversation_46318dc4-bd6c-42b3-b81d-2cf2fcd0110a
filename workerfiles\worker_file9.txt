任务流程：
针对Bump map To Tsk map Tool描述:
1，之前设计的test_nepes代码有一些问题，请按照下面描述修正代码（需要复用之前tsk_map_processor.py的一部分代码，处理逻辑也类似，只是我们需要生成修改二进制的map文件）请按照流程来操作最后生成最终map：
   -----先加载选择的dummy map：针对dummy map（针对这个文件加载二进制）
      ---复用tsk_map_processor.py的parse_file_header函数处理逻辑，需要变量：总的测试数量columnsize*rowsize，map version，TestResultStartPos，TestResultCategory。
	  ---检索每颗测试的x，y位置，每颗x，y坐标的芯片的category bin结果对应4个bytes输出（输出开始位置为TestResultCategory获取的地址，数据内容为tsk_map_processor.py的process_die_data函数下的onedieresult_category获取每颗芯片的二进制内容，
	     二进制内容修改的位置会根据map version来做动态调整）
	     map version=2或者3 修改从第1个bit到第16bit，一共16bit
		 map version=4 修改从第23个bit到第32bit，一共10bit
   -----再加载选择的bump map：针对bump map（提取bump map中RowData后的内容,内容和dummy map的测试数量是对应的。总的测试数量columnsize*rowsize）
      ---根据RowData后的内容，设计x，y来与dummy map的x，y顺序一一对应。
      ---bump map检索的关键内容：
         “--”代表测试数据为空
         “00”代表测试数据为pass（bump map的pass）
         “XX”除00以外的任何数据，代表测试数据为fail（bump map中的fail）
   -----现在我们根据上面的dummy map和bump map的描述，开始设计生成output tsk map部分(下面的描述是mapversion 4的方式)：
      ---根据bump map设计的检索内容，如果bump map中对应x，y位置，读出“--”，dummy map中相应x，y位置的二进制修改为（00000000000000000000000000000000）（二进制确认可以复用tsk_map_processor.py的process_die_data）
	  ---根据bump map设计的检索内容，如果bump map中对应x，y位置，读出“00”，dummy map中相应x，y位置的二进制修改为（00000000000000000000000000111111）（二进制确认可以复用tsk_map_processor.py的process_die_data）
	  ---根据bump map设计的检索内容，如果bump map中对应x，y位置，读出“XX”除00和--的其他内容，dummy map中相应x，y位置的二进制修改为（00000000000000000000000000111011）（二进制确认可以复用tsk_map_processor.py的process_die_data）
	  
现在举个例子，你可以根据例子来理解上面的描述：
    1， test内容下的dummy map为:009.NNS157-09-E4,解析的columnsize*rowsize=305*8=2440。TestResultCategory = 15049。
	2， test内容下的bump map为D97127.09,解析的columnsize*rowsize=305*8=2440，X=0,y=0位置为"--"这样dummy map中的第15049 byte位置，需要修改4bytes(00000000000000000000000000000000)。
	    x=79,y=0解析出"00",这样dummy map中的第15365 byte位置，需要修改为4bytes(00000000000000000000000000111111)
		x=149,y=2解析出“0C”，这样dummy map中的第18085 byte位置，需要修改为4bytes(00000000000000000000000000111011)
	3,  依次遍历整个columnsize*rowsize修改，完成全部dummy map新的生成，最中生成tsk map二进制文件用于后续测试。这样将bump map中的fail，能够加载进dummy map，通过load 最终的map，可以继续下一步流程。

上述功能开发注意事项：
     --- 其他两个功能不要破坏，代码功能不要破坏
     --- GUI尽量统一，不要占用太多空间
     --- 复用代码部分，尽量做到代码统一

要求：
1，测试脚本和文件请放到test文件夹
2，生成的md说明文档，请放到temp文件夹
3，程序架构统一，框架统一，代码尽量简洁

测试文件说明：
1，可以使用test文件夹下的D97127.09作为bump map的load
2，可以使用test文件夹下的009.NNS157-09-E4作为dummy map的文件