#!/usr/bin/env python3
"""
完整的Bump Map Tool集成测试
测试从main.py启动到Bump Map Tool加载的完整流程

Author: Yuribytes
Company: Chipone TE development Team
"""

import os
import sys
import tkinter as tk
import traceback

# 添加父目录到路径
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, parent_dir)

def test_main_py_imports():
    """测试main.py的导入"""
    print("🧪 测试main.py的导入")
    print("=" * 50)
    
    try:
        # 测试main.py中的导入
        from tool_selector import ToolSelector
        print("✅ tool_selector导入成功")
        
        from main_application import TSKMapApplication
        print("✅ main_application导入成功")
        
        return True
        
    except Exception as e:
        print(f"❌ main.py导入测试失败: {e}")
        traceback.print_exc()
        return False

def test_tool_selector_bump_map():
    """测试tool_selector中的Bump Map选项"""
    print(f"\n🧪 测试tool_selector中的Bump Map选项")
    print("=" * 50)
    
    try:
        from tool_selector import ToolSelector
        
        # 创建测试环境
        root = tk.Tk()
        root.withdraw()
        
        selector = ToolSelector()
        
        # 检查select_bump_map_tool方法
        if hasattr(selector, 'select_bump_map_tool'):
            print("✅ select_bump_map_tool方法存在")
        else:
            print("❌ select_bump_map_tool方法不存在")
            return False
        
        # 测试方法调用
        selector.select_bump_map_tool()
        if selector.selected_tool == "bump_map":
            print("✅ select_bump_map_tool方法工作正常")
        else:
            print(f"❌ select_bump_map_tool方法返回错误: {selector.selected_tool}")
            return False
        
        # 清理
        root.destroy()
        
        return True
        
    except Exception as e:
        print(f"❌ tool_selector测试失败: {e}")
        traceback.print_exc()
        return False

def test_main_application_bump_map():
    """测试main_application中的Bump Map Tool加载"""
    print(f"\n🧪 测试main_application中的Bump Map Tool加载")
    print("=" * 50)
    
    try:
        from main_application import TSKMapApplication
        
        # 创建测试环境
        app = TSKMapApplication()
        app.root.withdraw()
        
        # 检查show_bump_map_tool方法
        if hasattr(app, 'show_bump_map_tool'):
            print("✅ show_bump_map_tool方法存在")
        else:
            print("❌ show_bump_map_tool方法不存在")
            return False
        
        # 测试方法调用
        print("正在测试show_bump_map_tool方法调用...")
        app.show_bump_map_tool()
        
        # 检查bump_map_tool是否创建
        if app.bump_map_tool:
            print("✅ bump_map_tool实例创建成功")
        else:
            print("❌ bump_map_tool实例创建失败")
            return False
        
        # 检查窗口大小
        geometry = app.root.geometry()
        if "1000x950" in geometry:
            print(f"✅ 窗口大小正确: {geometry}")
        else:
            print(f"❌ 窗口大小错误: {geometry}")
            return False
        
        # 清理
        app.root.destroy()
        
        return True
        
    except Exception as e:
        print(f"❌ main_application测试失败: {e}")
        traceback.print_exc()
        return False

def test_bump_map_enhanced_frame_import():
    """测试bump_map_enhanced_frame导入和初始化"""
    print(f"\n🧪 测试bump_map_enhanced_frame导入和初始化")
    print("=" * 50)
    
    try:
        from bump_map_enhanced_frame import BumpMapEnhancedFrame
        print("✅ BumpMapEnhancedFrame导入成功")
        
        # 创建测试环境
        root = tk.Tk()
        root.withdraw()
        
        class MockController:
            def __init__(self):
                self.root = root
        
        controller = MockController()
        
        # 创建实例
        enhanced_frame = BumpMapEnhancedFrame(root, controller)
        print("✅ BumpMapEnhancedFrame实例创建成功")
        
        # 检查关键方法
        key_methods = ['show', 'hide', 'init_variables', 'create_widgets']
        for method in key_methods:
            if hasattr(enhanced_frame, method):
                print(f"✅ {method}方法存在")
            else:
                print(f"❌ {method}方法不存在")
                return False
        
        # 检查关键属性
        key_attributes = ['bump_map_files', 'dummy_map_file_path', 'canvas', 'scrollbar']
        for attr in key_attributes:
            if hasattr(enhanced_frame, attr):
                print(f"✅ {attr}属性存在")
            else:
                print(f"❌ {attr}属性不存在")
                return False
        
        # 清理
        root.destroy()
        
        return True
        
    except Exception as e:
        print(f"❌ bump_map_enhanced_frame测试失败: {e}")
        traceback.print_exc()
        return False

def test_complete_integration():
    """测试完整集成流程"""
    print(f"\n🧪 测试完整集成流程")
    print("=" * 50)
    
    try:
        # 步骤1: 创建main_application
        from main_application import TSKMapApplication
        app = TSKMapApplication()
        app.root.withdraw()
        print("✅ 步骤1: main_application创建成功")
        
        # 步骤2: 模拟tool_selector选择bump_map
        app.current_tool = "selector"
        print("✅ 步骤2: 设置当前工具为selector")
        
        # 步骤3: 调用show_bump_map_tool
        app.show_bump_map_tool()
        print("✅ 步骤3: show_bump_map_tool调用成功")
        
        # 步骤4: 验证bump_map_tool创建
        if app.bump_map_tool:
            print("✅ 步骤4: bump_map_tool实例存在")
        else:
            print("❌ 步骤4: bump_map_tool实例不存在")
            return False
        
        # 步骤5: 验证current_tool设置
        if app.current_tool == "bump_map":
            print("✅ 步骤5: current_tool正确设置为bump_map")
        else:
            print(f"❌ 步骤5: current_tool设置错误: {app.current_tool}")
            return False
        
        # 步骤6: 测试show方法
        app.bump_map_tool.show()
        print("✅ 步骤6: bump_map_tool.show()调用成功")
        
        # 步骤7: 测试hide方法
        app.bump_map_tool.hide()
        print("✅ 步骤7: bump_map_tool.hide()调用成功")
        
        # 清理
        app.root.destroy()
        
        return True
        
    except Exception as e:
        print(f"❌ 完整集成测试失败: {e}")
        traceback.print_exc()
        return False

def test_file_existence():
    """测试关键文件存在性"""
    print(f"\n🧪 测试关键文件存在性")
    print("=" * 50)
    
    key_files = [
        "main.py",
        "main_application.py", 
        "tool_selector.py",
        "bump_map_enhanced_frame.py"
    ]
    
    all_exist = True
    for file in key_files:
        if os.path.exists(file):
            print(f"✅ {file}存在")
        else:
            print(f"❌ {file}不存在")
            all_exist = False
    
    return all_exist

def main():
    """主测试函数"""
    print("🧪 Bump Map Tool完整集成测试")
    print("=" * 70)
    
    test_results = []
    
    try:
        # 测试1: 文件存在性
        result1 = test_file_existence()
        test_results.append(("关键文件存在性", result1))
        
        # 测试2: main.py导入
        result2 = test_main_py_imports()
        test_results.append(("main.py导入", result2))
        
        # 测试3: tool_selector Bump Map选项
        result3 = test_tool_selector_bump_map()
        test_results.append(("tool_selector Bump Map选项", result3))
        
        # 测试4: bump_map_enhanced_frame导入
        result4 = test_bump_map_enhanced_frame_import()
        test_results.append(("bump_map_enhanced_frame导入", result4))
        
        # 测试5: main_application Bump Map加载
        result5 = test_main_application_bump_map()
        test_results.append(("main_application Bump Map加载", result5))
        
        # 测试6: 完整集成流程
        result6 = test_complete_integration()
        test_results.append(("完整集成流程", result6))
        
        # 总结
        print("\n" + "=" * 70)
        print("🎉 Bump Map Tool完整集成测试结果总结:")
        
        passed = 0
        for test_name, result in test_results:
            status = "✅ 通过" if result else "❌ 失败"
            print(f"   {test_name}: {status}")
            if result:
                passed += 1
        
        print(f"\n📊 总体结果: {passed}/{len(test_results)} 测试通过")
        
        if passed == len(test_results):
            print("\n🎯 🎉 所有集成测试成功！")
            print("✅ Bump Map Tool可以正常从主界面加载")
            print("✅ 所有组件和方法正常工作")
            print("✅ 集成流程完整无误")
            print("\n🚀 现在可以通过main.py正常启动和使用Bump Map Tool！")
        else:
            print("⚠️  部分集成测试失败，存在问题需要修正")
            print("\n🔧 建议检查:")
            for test_name, result in test_results:
                if not result:
                    print(f"   - {test_name}")
        
        return passed == len(test_results)
        
    except Exception as e:
        print(f"❌ 集成测试过程出错: {e}")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
