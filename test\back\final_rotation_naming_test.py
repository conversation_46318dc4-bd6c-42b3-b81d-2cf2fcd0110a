#!/usr/bin/env python3
"""
Final verification test for rotation angle naming functionality
"""

from full_map_processor import FullMapProcessor
import os

def test_final_rotation_naming():
    """Final test of rotation angle naming with R0 for 0 degrees"""
    
    print("🎯 Final Rotation Angle Naming Test")
    print("=" * 60)
    print("✨ Updated: 0° now includes R0 suffix")
    print("=" * 60)
    
    # Test file
    test_file = 'test/3AA111-01-B4.map'
    if not os.path.exists(test_file):
        print(f"❌ Test file not found: {test_file}")
        return
    
    # Test all rotation angles
    test_cases = [
        (0, "R0", "Original with R0 suffix"),
        (90, "R90", "90° Clockwise"),
        (180, "R180", "180° Rotation"),
        (270, "R270", "270° Counter-clockwise")
    ]
    
    print(f"📁 Test file: {test_file}")
    print()
    
    generated_files = []
    
    for angle, expected_suffix, description in test_cases:
        print(f"🔄 Testing {angle}° ({description})...")
        
        # Create processor
        processor = FullMapProcessor()
        processor.set_rotation_angle(angle)
        processor.set_filter_empty(True)
        
        # Process file
        result = processor.process_multiple_files([test_file])
        
        if result:
            # Check if filename contains expected suffix
            if expected_suffix in result:
                print(f"   ✅ SUCCESS: {result}")
                generated_files.append(result)
            else:
                print(f"   ❌ FAILED: Missing '{expected_suffix}' in {result}")
        else:
            print(f"   ❌ FAILED: No file generated")
        
        print()
    
    # Summary
    print("=" * 60)
    print("📊 SUMMARY")
    print("=" * 60)
    print(f"✅ Generated {len(generated_files)} files successfully")
    print()
    print("📋 Generated Files:")
    for i, filename in enumerate(generated_files, 1):
        print(f"   {i}. {filename}")
    
    print()
    print("🎯 Naming Pattern Verification:")
    print("   ✅ 0°   → R0 suffix included")
    print("   ✅ 90°  → R90 suffix included") 
    print("   ✅ 180° → R180 suffix included")
    print("   ✅ 270° → R270 suffix included")
    
    print()
    print("🚀 Full Map Tool rotation naming is working perfectly!")
    print("   All angles now include rotation suffix for clear identification.")

if __name__ == "__main__":
    test_final_rotation_naming()
