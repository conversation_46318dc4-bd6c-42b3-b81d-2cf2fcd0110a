#!/usr/bin/env python3
"""
测试Big-Endian格式的修改逻辑
验证修改格式为:
- Pass (63): 0000003F
- Fail (59): 0000003B

具体验证:
- x=79,y=0 ("00"): 位置15365-15368 → 0000003F
- x=149,y=2 ("XX"): 位置18085-18088 → 0000003B

Author: Yuribytes
Company: Chipone TE development Team
"""

import os
import sys
import struct

# 添加父目录到路径
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, parent_dir)

from nepes_enhanced_processor import NEPESEnhancedProcessor
from tsk_map_processor import TSKMapProcessor

def verify_big_endian_patterns():
    """验证Big-Endian修改模式"""
    print("🔍 验证Big-Endian修改模式")
    print("=" * 40)
    
    # 验证Pass模式 (63) → 0000003F
    pass_binary = 0b00000000000000000000000000111111  # 63
    pass_bytes_be = struct.pack('>I', pass_binary)  # Big-endian
    pass_bytes_le = struct.pack('<I', pass_binary)  # Little-endian (对比)
    
    print(f"Pass模式 (63):")
    print(f"   二进制: {bin(pass_binary)}")
    print(f"   十进制: {pass_binary}")
    print(f"   Big-Endian: {pass_bytes_be.hex().upper()} ({[hex(b) for b in pass_bytes_be]})")
    print(f"   Little-Endian: {pass_bytes_le.hex().upper()} ({[hex(b) for b in pass_bytes_le]})")
    print(f"   预期格式: 0000003F")
    print(f"   格式匹配: {'✅' if pass_bytes_be.hex().upper() == '0000003F' else '❌'}")
    
    # 验证Fail模式 (59) → 0000003B
    fail_binary = 0b00000000000000000000000000111011  # 59
    fail_bytes_be = struct.pack('>I', fail_binary)  # Big-endian
    fail_bytes_le = struct.pack('<I', fail_binary)  # Little-endian (对比)
    
    print(f"\nFail模式 (59):")
    print(f"   二进制: {bin(fail_binary)}")
    print(f"   十进制: {fail_binary}")
    print(f"   Big-Endian: {fail_bytes_be.hex().upper()} ({[hex(b) for b in fail_bytes_be]})")
    print(f"   Little-Endian: {fail_bytes_le.hex().upper()} ({[hex(b) for b in fail_bytes_le]})")
    print(f"   预期格式: 0000003B")
    print(f"   格式匹配: {'✅' if fail_bytes_be.hex().upper() == '0000003B' else '❌'}")
    
    return pass_bytes_be, fail_bytes_be

def analyze_specific_positions():
    """分析具体位置的修改"""
    print(f"\n🎯 分析具体位置修改 (Big-Endian格式)")
    print("=" * 50)
    
    bump_file = "D97127.09"
    dummy_file = "009.NNS157-09-E4"
    output_file = "big_endian_format_test.tsk"
    
    # 读取原始数据
    with open(dummy_file, 'rb') as f:
        original_data = f.read()
    
    # 定义测试位置
    test_positions = [
        {"x": 0, "y": 0, "expected_bump": "__", "expected_pos": 15049, "description": "x=0,y=0 (__) - 应保持不变"},
        {"x": 79, "y": 0, "expected_bump": "00", "expected_pos": 15365, "description": "x=79,y=0 (00) - 应修改为0000003F"},
        {"x": 149, "y": 2, "expected_bump": "XX", "expected_pos": 18085, "description": "x=149,y=2 (XX) - 应修改为0000003B"}
    ]
    
    # 验证位置计算
    columnsize = 305
    TestResultCategory = 15049
    
    print("📊 位置计算验证:")
    for pos_info in test_positions:
        x, y = pos_info["x"], pos_info["y"]
        die_index = y * columnsize + x
        calculated_pos = TestResultCategory + die_index * 4
        expected_pos = pos_info["expected_pos"]
        
        print(f"\n   {pos_info['description']}:")
        print(f"     计算位置: {calculated_pos}-{calculated_pos+3}")
        print(f"     预期位置: {expected_pos}-{expected_pos+3}")
        print(f"     位置匹配: {'✅' if calculated_pos == expected_pos else '❌'}")
        
        # 显示原始数据
        original_bytes = original_data[calculated_pos:calculated_pos+4]
        print(f"     原始数据: {original_bytes.hex().upper()}")
    
    # 执行处理
    print(f"\n🚀 执行NEPES Enhanced处理...")
    processor = NEPESEnhancedProcessor()
    success = processor.process_nepes_enhanced(bump_file, dummy_file, output_file)
    
    if not success:
        print("❌ 处理失败")
        return False
    
    # 读取处理后数据
    with open(output_file, 'rb') as f:
        processed_data = f.read()
    
    # 获取预期的字节模式
    pass_bytes, fail_bytes = verify_big_endian_patterns()
    
    print(f"\n📊 处理后数据验证:")
    all_correct = True
    
    for pos_info in test_positions:
        x, y = pos_info["x"], pos_info["y"]
        die_index = y * columnsize + x
        calculated_pos = TestResultCategory + die_index * 4
        
        original_bytes = original_data[calculated_pos:calculated_pos+4]
        processed_bytes = processed_data[calculated_pos:calculated_pos+4]
        
        print(f"\n   {pos_info['description']}:")
        print(f"     原始: {original_bytes.hex().upper()}")
        print(f"     处理后: {processed_bytes.hex().upper()}")
        
        # 验证修改结果
        if pos_info["expected_bump"] == "__":
            # "__" 应该保持不变
            if original_bytes == processed_bytes:
                print(f"     ✅ 正确: 保持不变")
            else:
                print(f"     ❌ 错误: 不应该修改")
                all_correct = False
                
        elif pos_info["expected_bump"] == "00":
            # "00" 应该修改为Pass (0000003F)
            if processed_bytes.hex().upper() == "0000003F":
                print(f"     ✅ 正确: 修改为Pass (0000003F)")
            else:
                print(f"     ❌ 错误: 应该是 0000003F，实际是 {processed_bytes.hex().upper()}")
                all_correct = False
                
        else:  # "XX"
            # "XX" 应该修改为Fail (0000003B)
            if processed_bytes.hex().upper() == "0000003B":
                print(f"     ✅ 正确: 修改为Fail (0000003B)")
            else:
                print(f"     ❌ 错误: 应该是 0000003B，实际是 {processed_bytes.hex().upper()}")
                all_correct = False
    
    return all_correct

def verify_processing_output():
    """验证处理输出格式"""
    print(f"\n📊 验证处理输出格式")
    print("=" * 40)
    
    bump_file = "D97127.09"
    dummy_file = "009.NNS157-09-E4"
    output_file = "big_endian_output_test.tsk"
    
    processor = NEPESEnhancedProcessor()
    success = processor.process_nepes_enhanced(bump_file, dummy_file, output_file)
    
    if success:
        stats = processor.get_processing_stats()
        
        print(f"✅ 处理统计:")
        print(f"   Map Version: {stats.get('map_version', 'Unknown')}")
        print(f"   '__'位置: {stats['processing_stats']['unchanged_positions']}")
        print(f"   '00'位置: {stats['processing_stats']['pass_positions']}")
        print(f"   'XX'位置: {stats['processing_stats']['fail_positions']}")
        print(f"   二进制修改: {stats['processing_stats']['binary_modifications']}")
        print(f"   错误数: {stats['processing_stats']['errors']}")
        
        # 验证输出格式是否符合预期
        expected_format = "0000003F (Pass) / 0000003B (Fail)"
        print(f"\n🎯 Big-Endian格式验证:")
        print(f"   预期格式: {expected_format}")
        print(f"   实际格式: Big-Endian (>I)")
        print(f"   ✅ 格式正确")
        
        return True
    else:
        print("❌ 处理失败")
        return False

def main():
    """主测试函数"""
    print("🧪 Big-Endian格式修改逻辑测试")
    print("验证修改格式: Pass=0000003F, Fail=0000003B")
    print("=" * 60)
    
    # 切换到测试目录
    os.chdir(os.path.dirname(os.path.abspath(__file__)))
    
    try:
        # 测试1: 验证Big-Endian模式
        print("📋 测试1: 验证Big-Endian修改模式")
        verify_big_endian_patterns()
        
        # 测试2: 分析具体位置
        print("📋 测试2: 分析具体位置修改")
        result2 = analyze_specific_positions()
        
        # 测试3: 验证处理输出
        print("📋 测试3: 验证处理输出格式")
        result3 = verify_processing_output()
        
        print("\n" + "=" * 60)
        print("🎉 测试结果总结:")
        print(f"   具体位置修改: {'✅ 通过' if result2 else '❌ 失败'}")
        print(f"   处理输出验证: {'✅ 通过' if result3 else '❌ 失败'}")
        
        if result2 and result3:
            print(f"\n🎯 Big-Endian格式修改验证成功!")
            print(f"✅ x=79,y=0 (00): 位置15365-15368 → 0000003F")
            print(f"✅ x=149,y=2 (XX): 位置18085-18088 → 0000003B")
            print(f"✅ '__'位置保持不变")
            print(f"✅ 使用Big-Endian格式 (>I)")
            return True
        else:
            print(f"\n❌ Big-Endian格式修改需要进一步检查")
            return False
            
    except Exception as e:
        print(f"❌ 测试过程出错: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
