# TSK MAP Tool 简易使用说明

## 🚀 **快速开始**

### **1. 启动程序**
双击 `TSK_MAP_Tool.exe` 启动程序

### **2. 选择工具**
- **Full Map Tool** - 处理多个 MAP 文件
- **AB Map Tool** - 对比两个 MAP 文件  
- **Bump Map Tool** - 处理 Bump Map 数据

---

## 📊 **Full Map Tool 使用**

### **基本步骤**：
1. **添加文件**：点击 "Add MAP Files..." 选择 MAP 文件
2. **设置选项**：勾选需要的处理选项
3. **开始处理**：点击 "Process All Files"
4. **查看结果**：在输出文件夹中查看生成的 Excel 文件

### **常用选项**：
- ☑ **Filter Empty Areas** - 过滤空白区域
- ☑ **Sort Bins by Quantity** - 按数量排序
- **Rotation Angle** - 选择旋转角度 (0°/90°/180°/270°)

### **输出文件**：
- Excel 报告：`FullMap_Xfiles_日期时间.xlsx`
- TMB 文件：`文件名_日期时间.tmb`

---

## 🔍 **AB Map Tool 使用**

### **基本步骤**：
1. **选择文件 A**：点击 "Browse..." 选择第一个 MAP 文件
2. **选择文件 B**：点击 "Browse..." 选择第二个 MAP 文件
3. **开始对比**：点击 "Process AB Comparison"
4. **查看结果**：在输出文件夹中查看对比报告

### **输出文件**：
- 对比报告：`AB_Comparison_日期时间.xlsx`

---

## 🎨 **Bump Map Tool 使用**

### **基本步骤**：
1. **选择处理器**：选择合适的处理器类型
2. **配置参数**：根据需要调整处理参数
3. **选择文件**：选择要处理的 Bump Map 文件
4. **开始处理**：点击处理按钮
5. **查看结果**：检查处理结果

---

## 📄 **配置文件 (可选)**

### **Excel 配置文件格式**：
```
    A           B           D         F           H         J
2   程序名称    (空)        设备名称   供应商名称   测试流程   晶圆尺寸
5   (空)       (空)        测试机     探针卡号     测试流程   晶圆尺寸
6   1          Pass
7   2          Fail
8   3          其他名称
...
```

### **重要单元格**：
- **A2**: 测试程序名称 (必需)
- **J5**: 晶圆尺寸 (如: "8inch")
- **A6:B∞**: Bin 编号和名称映射 (必需)

---

## ⚡ **快速操作指南**

### **处理单个文件夹的所有 MAP 文件**：
1. 启动 Full Map Tool
2. 点击 "Add Folder..."
3. 选择包含 MAP 文件的文件夹
4. 点击 "Process All Files"

### **对比两个 MAP 文件**：
1. 启动 AB Map Tool
2. 分别选择文件 A 和文件 B
3. 点击 "Process AB Comparison"

### **生成 TMB 文件**：
1. 在 Full Map Tool 中添加 MAP 文件
2. 点击 "Process TMB Files"

---

## 🔧 **常见问题解决**

### **问题：程序无响应**
**解决**：点击 "Clear Memory" 清理内存

### **问题：文件无法打开**
**解决**：检查文件格式是否为支持的 MAP/TSK 格式

### **问题：输出文件找不到**
**解决**：检查输出文件夹设置，默认保存在第一个文件所在目录

### **问题：配置文件读取失败**
**解决**：检查 Excel 文件格式，确保 A2 和 A6:B∞ 有正确内容

---

## 📁 **文件类型说明**

### **输入文件**：
- **.map** - MAP 格式文件
- **.tsk** - TSK 格式文件  
- **.xlsx** - Excel 配置文件

### **输出文件**：
- **.xlsx** - Excel 分析报告
- **.tmb** - TMB 格式文件

---

## 💡 **使用技巧**

### **提高处理速度**：
- 定期使用 "Clear Memory" 清理内存
- 处理大量文件时分批进行
- 关闭不必要的其他程序

### **优化输出结果**：
- 使用配置文件提供 Bin 名称映射
- 根据需要选择合适的旋转角度
- 启用 "Filter Empty Areas" 减少文件大小

### **文件管理**：
- 将相关的 MAP 文件放在同一文件夹
- 使用有意义的文件名
- 定期备份重要的输出文件

---

## 🎯 **典型使用场景**

### **场景 1：批量处理测试数据**
```
目标：处理一批测试完成的 MAP 文件
步骤：Full Map Tool → Add Folder → Process All Files
结果：获得综合分析报告
```

### **场景 2：对比测试结果**
```
目标：对比两次测试的差异
步骤：AB Map Tool → 选择两个文件 → Process AB Comparison  
结果：获得详细对比报告
```

### **场景 3：生成 TMB 文件**
```
目标：将 MAP 文件转换为 TMB 格式
步骤：Full Map Tool → 添加文件 → Process TMB Files
结果：获得 TMB 格式文件
```

---

## 📞 **获取帮助**

### **程序内帮助**：
- 查看状态栏提示信息
- 注意弹窗中的详细说明

### **文件示例**：
- 参考 test 文件夹中的示例文件
- 查看示例配置文件格式

### **技术支持**：
- 开发团队：Chipone TE Development Team
- 版本：2025.08.12

---

## 📋 **检查清单**

### **使用前检查**：
- [ ] 确认文件格式正确 (MAP/TSK)
- [ ] 准备配置文件 (可选)
- [ ] 确保有足够的磁盘空间

### **处理后检查**：
- [ ] 确认输出文件生成成功
- [ ] 检查处理结果的准确性
- [ ] 备份重要的输出文件

---

## 🔄 **版本信息**

**当前版本**：TSK MAP Tool v2025.08.12  
**文档版本**：简易版 v1.0  
**最后更新**：2025-08-12

### **最新改进**：
- ✅ 动态弹窗大小调整
- ✅ 修复 Wafer Size 读取
- ✅ 优化用户界面
- ✅ 增强错误提示

---

**💡 提示**：如需更详细的说明，请参考《TSK MAP Tool 详尽使用说明》
