#!/usr/bin/env python3
"""
最终验证NEPES处理器与TSK处理器逻辑一致性

Author: Yuribytes
Company: Chipone TE development Team
"""

import os
import sys

# 添加父目录到路径
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, parent_dir)

from nepes_enhanced_processor import NEPESEnhancedProcessor
from tsk_map_processor import TSKMapProcessor

def verify_tsk_nepes_consistency():
    """验证TSK和NEPES处理器的一致性"""
    print("🧪 最终验证: TSK vs NEPES处理器一致性")
    print("=" * 60)
    
    bump_file = "D97127.09"
    dummy_file = "009.NNS157-09-E4"
    output_file = "final_verification.tsk"
    
    # 1. 分析TSK处理器的读取逻辑
    print("📊 Step 1: 分析TSK处理器读取逻辑")
    
    tsk_processor = TSKMapProcessor()
    tsk_processor.read_file(dummy_file)
    tsk_processor.parse_file_header()
    
    # 关键位置分析
    test_positions = [
        {"x": 79, "y": 0, "die_index": 79},
        {"x": 80, "y": 0, "die_index": 80},  # 相邻位置
        {"x": 149, "y": 2, "die_index": 2*305+149}
    ]
    
    print("   TSK处理器读取位置:")
    for pos in test_positions:
        x, y, die_index = pos["x"], pos["y"], pos["die_index"]
        category_start_pos = tsk_processor.TestResultCategory + 4 * die_index
        
        # TSK实际读取范围 (get_binary会-1)
        actual_read_start = category_start_pos - 1
        actual_read_end = category_start_pos + 3
        
        print(f"     x={x},y={y}: 计算位置{category_start_pos}, 实际读取[{actual_read_start}:{actual_read_end}]")
    
    # 2. 执行NEPES处理
    print(f"\n📊 Step 2: 执行NEPES处理")
    
    processor = NEPESEnhancedProcessor()
    success = processor.process_nepes_enhanced(bump_file, dummy_file, output_file)
    
    if not success:
        print("❌ NEPES处理失败")
        return False
    
    # 3. 验证结果
    print(f"\n📊 Step 3: 验证处理结果")
    
    with open(output_file, 'rb') as f:
        processed_data = f.read()
    
    print("   NEPES处理器写入验证:")
    
    all_correct = True
    
    for pos in test_positions:
        x, y, die_index = pos["x"], pos["y"], pos["die_index"]
        
        # NEPES写入位置 (现在是-1)
        nepes_write_pos = tsk_processor.TestResultCategory + 4 * die_index - 1
        
        # TSK读取位置 (get_binary会-1)
        tsk_read_pos = tsk_processor.TestResultCategory + 4 * die_index - 1
        
        # 检查是否一致
        positions_match = nepes_write_pos == tsk_read_pos
        
        # 读取实际数据
        actual_data = processed_data[nepes_write_pos:nepes_write_pos+4]
        
        print(f"     x={x},y={y}:")
        print(f"       NEPES写入位置: {nepes_write_pos}")
        print(f"       TSK读取位置: {tsk_read_pos}")
        print(f"       位置匹配: {'✅' if positions_match else '❌'}")
        print(f"       实际数据: {actual_data.hex().upper()}")
        
        if not positions_match:
            all_correct = False
    
    # 4. 验证关键位置的数据
    print(f"\n📊 Step 4: 验证关键位置数据")
    
    # x=79,y=0 (应该是Pass: 0000003F)
    pos_79_data = processed_data[15364:15368]
    expected_79 = "0000003F"
    actual_79 = pos_79_data.hex().upper()
    
    print(f"   x=79,y=0 (位置15364-15367):")
    print(f"     预期: {expected_79}")
    print(f"     实际: {actual_79}")
    print(f"     正确: {'✅' if actual_79 == expected_79 else '❌'}")
    
    # x=149,y=2 (应该是Fail: 0000003B)
    pos_149_data = processed_data[18084:18088]
    expected_149 = "0000003B"
    actual_149 = pos_149_data.hex().upper()
    
    print(f"   x=149,y=2 (位置18084-18087):")
    print(f"     预期: {expected_149}")
    print(f"     实际: {actual_149}")
    print(f"     正确: {'✅' if actual_149 == expected_149 else '❌'}")
    
    # 5. 最终结论
    data_correct = actual_79 == expected_79 and actual_149 == expected_149
    
    print(f"\n🎯 最终验证结果:")
    print(f"   位置逻辑一致: {'✅' if all_correct else '❌'}")
    print(f"   数据写入正确: {'✅' if data_correct else '❌'}")
    
    if all_correct and data_correct:
        print(f"\n🎉 验证成功!")
        print(f"✅ NEPES处理器与TSK处理器逻辑完全一致")
        print(f"✅ 1字节偏移问题已完全解决")
        print(f"✅ 现在与Full Map Tool逻辑统一")
        print(f"✅ x=79,y=0正确写入TSK实际读取位置15364")
        print(f"✅ x=149,y=2正确写入TSK实际读取位置18084")
        return True
    else:
        print(f"\n❌ 验证失败，仍需调试")
        return False

def main():
    """主验证函数"""
    print("🧪 NEPES处理器最终验证")
    print("=" * 70)
    
    # 切换到测试目录
    os.chdir(os.path.dirname(os.path.abspath(__file__)))
    
    try:
        # 执行最终验证
        result = verify_tsk_nepes_consistency()
        
        print("\n" + "=" * 70)
        print("🎉 验证完成!")
        
        if result:
            print("🎯 **恭喜！NEPES处理器现在与Full Map Tool完全一致！**")
        else:
            print("❌ 仍需进一步调试")
            
        return result
            
    except Exception as e:
        print(f"❌ 验证过程出错: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
