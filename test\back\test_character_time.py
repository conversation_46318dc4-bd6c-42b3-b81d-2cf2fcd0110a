#!/usr/bin/env python3
"""
Test Character Time - Verify character format time extraction
"""

import sys
import os
sys.path.append('..')
from tsk_map_processor import TSKMapProcessor
from excel_output import create_excel_output


def test_character_time_extraction(filepath):
    """
    Test character format time extraction
    """
    print(f"Testing Character Time Extraction: {os.path.basename(filepath)}")
    print("=" * 60)
    
    if not os.path.exists(filepath):
        print(f"❌ File not found: {filepath}")
        return False
    
    processor = TSKMapProcessor()
    
    # Read and parse file
    if not processor.read_file(filepath):
        print("❌ Failed to read file")
        return False
    
    if not processor.parse_file_header():
        print("❌ Failed to parse header")
        return False
    
    print(f"✅ File parsed successfully")
    
    # Display extracted information
    print(f"\nExtracted Device Information:")
    print(f"  Device Name: '{processor.devicename}'")
    print(f"  Lot ID: '{processor.lotid}'")
    print(f"  Wafer Slot ID: '{processor.waferslotid}'")
    print(f"  Start Time: '{processor.waferstarttime}'")
    print(f"  End Time: '{processor.waferendtime}'")
    
    # Detailed analysis of time data
    print(f"\nDetailed Time Data Analysis:")
    
    # Start Time (位置149-160)
    try:
        start_binary = processor.get_binary(processor.filearray, 149, 160)
        print(f"  Start Time (149-160):")
        print(f"    Binary length: {len(start_binary)} bits")
        print(f"    Binary: {start_binary}")
        
        # Parse each 2-byte component
        print(f"    Component analysis:")
        for i in range(0, min(96, len(start_binary)), 16):
            if i + 16 <= len(start_binary):
                component_binary = start_binary[i:i+16]
                component_value = processor.binary2val(component_binary)
                component_char = processor.binary_to_character_string(component_binary)
                print(f"      Bytes {i//8}-{i//8+1}: Binary={component_binary}, Value={component_value}, Char='{component_char}'")
        
        print(f"    Final result: '{processor.waferstarttime}'")
        
    except Exception as e:
        print(f"  Start Time Error: {e}")
    
    # End Time (位置161-172)
    try:
        end_binary = processor.get_binary(processor.filearray, 161, 172)
        print(f"  End Time (161-172):")
        print(f"    Binary length: {len(end_binary)} bits")
        print(f"    Binary: {end_binary}")
        
        # Parse each 2-byte component
        print(f"    Component analysis:")
        for i in range(0, min(96, len(end_binary)), 16):
            if i + 16 <= len(end_binary):
                component_binary = end_binary[i:i+16]
                component_value = processor.binary2val(component_binary)
                component_char = processor.binary_to_character_string(component_binary)
                print(f"      Bytes {i//8}-{i//8+1}: Binary={component_binary}, Value={component_value}, Char='{component_char}'")
        
        print(f"    Final result: '{processor.waferendtime}'")
        
    except Exception as e:
        print(f"  End Time Error: {e}")
    
    return True


def test_excel_output_with_character_time(filepath):
    """
    Test Excel output with character format time
    """
    print(f"\nTesting Excel Output with Character Time")
    print("=" * 50)
    
    processor = TSKMapProcessor()
    
    if not processor.read_file(filepath):
        return False
    
    if not processor.parse_file_header():
        return False
    
    if not processor.process_die_data():
        return False
    
    # Create Excel output
    output_filename = "test_character_time_output.xlsx"
    
    try:
        if create_excel_output(processor, "TestSheet", 0, output_filename, filepath):
            print(f"✅ Excel output created: {output_filename}")
            
            if os.path.exists(output_filename):
                file_size = os.path.getsize(output_filename)
                print(f"  File size: {file_size:,} bytes")
                
                stats = processor.get_test_statistics()
                print(f"\nDevice Info in Excel:")
                print(f"  B2: Device Name = '{processor.devicename}'")
                print(f"  B4: Lot ID = '{processor.lotid}'")
                print(f"  B5: Wafer ID = '{processor.waferslotid}'")
                print(f"  B6: Start Time = '{processor.waferstarttime}'")
                print(f"  B7: End Time = '{processor.waferendtime}'")
                print(f"  B8: Total Tested = {stats['total_tested']}")
                print(f"  B9: Pass Count = {stats['pass_count']}")
                print(f"  B10: Yield = {stats['yield_percentage']:.2f}%")
                
                # Check if time fields have content
                if processor.waferstarttime:
                    print(f"  ✅ Start Time extracted successfully")
                else:
                    print(f"  ⚠️  Start Time is empty")
                
                if processor.waferendtime:
                    print(f"  ✅ End Time extracted successfully")
                else:
                    print(f"  ⚠️  End Time is empty")
                
                return True
            else:
                print("❌ Output file not found")
                return False
        else:
            print("❌ Failed to create Excel output")
            return False
            
    except Exception as e:
        print(f"❌ Error creating Excel output: {e}")
        return False


def main():
    """
    Main test function
    """
    if len(sys.argv) < 2:
        print("Character Time Test")
        print("Usage: python test_character_time.py <tsk_file_path>")
        print("Example: python test_character_time.py ../3AA111-01-B4")
        return
    
    filepath = sys.argv[1]
    
    print("TSK/MAP Character Time Test")
    print("Testing character format time extraction for B6 and B7")
    print("=" * 70)
    
    # Test 1: Character time extraction
    if not test_character_time_extraction(filepath):
        print("❌ Character time extraction test failed")
        return
    
    # Test 2: Excel output with character time
    if not test_excel_output_with_character_time(filepath):
        print("❌ Excel output test failed")
        return
    
    print("\n" + "=" * 70)
    print("🎉 Character Time Test Completed!")
    print("\nKey Changes:")
    print("✅ Updated read positions (149-160, 161-172)")
    print("✅ Character format time parsing")
    print("✅ Fallback to character string display")
    print("✅ Excel time format when possible")
    
    print(f"\nTime Parsing Logic:")
    print(f"1. Extract 6 components (2 bytes each)")
    print(f"2. Try to interpret as characters first")
    print(f"3. Convert to numbers if characters not readable")
    print(f"4. Format as Excel time if valid date/time")
    print(f"5. Otherwise display as character string")


if __name__ == "__main__":
    main()
