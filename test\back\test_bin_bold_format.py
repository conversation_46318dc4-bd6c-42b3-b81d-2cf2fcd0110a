#!/usr/bin/env python3
"""
Test Bin Bold Format - Verify bin names in A14~AXX are displayed in bold
"""

import sys
import os

# Add parent directory to path
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
sys.path.insert(0, parent_dir)

from tsk_map_processor import TSKMapProcessor
from config_reader import ConfigReader
from excel_output import create_excel_output


def test_bin_bold_formatting_with_config(tsk_filepath):
    """Test bin bold formatting with configuration file"""
    print("Testing Bin Bold Formatting with Config")
    print("=" * 50)
    
    config_path = os.path.join(parent_dir, "3509_CP1_program_bin.xlsx")
    
    if not os.path.exists(tsk_filepath):
        print(f"❌ TSK file not found: {tsk_filepath}")
        return False
    
    if not os.path.exists(config_path):
        print(f"❌ Config file not found: {config_path}")
        return False
    
    try:
        # Load TSK file
        processor = TSKMapProcessor()
        
        if not processor.read_file(tsk_filepath):
            print("❌ Failed to read TSK file")
            return False
        
        if not processor.parse_file_header():
            print("❌ Failed to parse TSK header")
            return False
        
        if not processor.process_die_data():
            print("❌ Failed to process die data")
            return False
        
        # Load config file
        config_reader = ConfigReader()
        if not config_reader.read_config_file(config_path):
            print("❌ Failed to load config file")
            return False
        
        print(f"✅ Both files loaded successfully")
        
        # Get bin statistics
        bin_stats = processor.get_bin_statistics()
        
        print(f"\nBin Statistics (will be bold in Excel):")
        print(f"{'Rank':<4} {'Bin Format':<30} {'Quantity':<8} {'Yield%':<8}")
        print("-" * 50)
        
        for i, bin_data in enumerate(bin_stats[:10], 1):  # Show top 10
            bin_number = int(bin_data['bin_name'].replace('bin', ''))
            formatted_name = config_reader.get_formatted_bin_name(bin_number)
            quantity = bin_data['quantity']
            yield_pct = bin_data['yield_percentage']
            
            print(f"{i:<4} {formatted_name:<30} {quantity:<8} {yield_pct:<8.2f}")
        
        # Create Excel output with bold bin names
        output_filename = os.path.join(current_dir, "test_bin_bold_with_config.xlsx")
        
        if create_excel_output(processor, "TestSheet", 0, output_filename, tsk_filepath, config_reader):
            print(f"\n✅ Excel output created: {os.path.basename(output_filename)}")
            
            if os.path.exists(output_filename):
                file_size = os.path.getsize(output_filename)
                print(f"  File size: {file_size:,} bytes")
                print(f"  Location: test/ folder")
                
                print(f"\nExpected Excel Formatting:")
                print(f"  A13: 'Category' (header, bold)")
                print(f"  A14~A{13+len(bin_stats)}: Bin names (BOLD)")
                
                for i, bin_data in enumerate(bin_stats[:5], 14):
                    bin_number = int(bin_data['bin_name'].replace('bin', ''))
                    formatted_name = config_reader.get_formatted_bin_name(bin_number)
                    print(f"    A{i}: '{formatted_name}' (BOLD)")
                
                return True
            else:
                print("❌ Output file not found")
                return False
        else:
            print("❌ Failed to create Excel output")
            return False
            
    except Exception as e:
        print(f"❌ Error in bold formatting test: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_bin_bold_formatting_without_config(tsk_filepath):
    """Test bin bold formatting without configuration file"""
    print(f"\nTesting Bin Bold Formatting without Config")
    print("=" * 50)
    
    if not os.path.exists(tsk_filepath):
        print(f"❌ TSK file not found: {tsk_filepath}")
        return False
    
    try:
        # Load TSK file
        processor = TSKMapProcessor()
        
        if not processor.read_file(tsk_filepath):
            print("❌ Failed to read TSK file")
            return False
        
        if not processor.parse_file_header():
            print("❌ Failed to parse TSK header")
            return False
        
        if not processor.process_die_data():
            print("❌ Failed to process die data")
            return False
        
        print(f"✅ TSK file loaded successfully")
        
        # Get bin statistics
        bin_stats = processor.get_bin_statistics()
        
        print(f"\nBin Statistics (numbers only, will be bold):")
        print(f"{'Rank':<4} {'Bin Format':<15} {'Quantity':<8} {'Yield%':<8}")
        print("-" * 35)
        
        for i, bin_data in enumerate(bin_stats[:5], 1):  # Show top 5
            bin_number = int(bin_data['bin_name'].replace('bin', ''))
            format_without_config = str(bin_number)  # Just the number
            quantity = bin_data['quantity']
            yield_pct = bin_data['yield_percentage']
            
            print(f"{i:<4} {format_without_config:<15} {quantity:<8} {yield_pct:<8.2f}")
        
        # Create Excel output without config
        output_filename = os.path.join(current_dir, "test_bin_bold_no_config.xlsx")
        
        if create_excel_output(processor, "TestSheet", 0, output_filename, tsk_filepath, None):
            print(f"\n✅ Excel output created: {os.path.basename(output_filename)}")
            print(f"  Location: test/ folder")
            
            print(f"\nExpected Excel Formatting (No Config):")
            for i, bin_data in enumerate(bin_stats[:5], 14):
                bin_number = int(bin_data['bin_name'].replace('bin', ''))
                print(f"    A{i}: '{bin_number}' (BOLD)")
            
            return True
        else:
            print("❌ Failed to create Excel output")
            return False
            
    except Exception as e:
        print(f"❌ Error in no-config bold test: {e}")
        return False


def verify_excel_bold_formatting():
    """Verify that the Excel formatting includes bold fonts"""
    print(f"\nVerifying Excel Bold Formatting Implementation")
    print("=" * 50)
    
    try:
        from openpyxl import Workbook
        from openpyxl.styles import Font
        
        # Create a test workbook to verify font settings
        wb = Workbook()
        ws = wb.active
        
        # Test the font that should be used for bin names
        from excel_output import ExcelOutputHandler
        handler = ExcelOutputHandler()
        
        # Check if header_font is bold
        header_font = handler.header_font
        print(f"Header font settings:")
        print(f"  Bold: {header_font.bold}")
        print(f"  Name: {header_font.name}")
        print(f"  Size: {header_font.size}")
        
        if header_font.bold:
            print(f"✅ Header font is bold - bin names will be bold")
        else:
            print(f"⚠️  Header font is not bold - bin names may not be bold")
        
        # Test cell formatting
        test_cell = ws['A1']
        test_cell.value = "Test Bold Bin"
        test_cell.font = header_font
        
        print(f"✅ Font formatting test completed")
        
        # Clean up
        wb.close()
        
        return True
        
    except Exception as e:
        print(f"❌ Error verifying Excel formatting: {e}")
        return False


def main():
    """Main test function"""
    if len(sys.argv) < 2:
        print("Bin Bold Format Test")
        print("Usage: python test_bin_bold_format.py <tsk_file_path>")
        print("Example: python test_bin_bold_format.py ../3AA111-01-B4")
        return
    
    tsk_filepath = sys.argv[1]
    # Convert relative path to absolute path
    if not os.path.isabs(tsk_filepath):
        tsk_filepath = os.path.join(parent_dir, tsk_filepath)
    
    print("TSK/MAP Bin Bold Format Test")
    print("Testing bold formatting for bin names in A14~AXX")
    print("=" * 70)
    
    # Test 1: Verify Excel formatting implementation
    if not verify_excel_bold_formatting():
        print("❌ Excel formatting verification failed")
        return
    
    # Test 2: Bold formatting with config
    if not test_bin_bold_formatting_with_config(tsk_filepath):
        print("❌ Bold formatting with config test failed")
        return
    
    # Test 3: Bold formatting without config
    if not test_bin_bold_formatting_without_config(tsk_filepath):
        print("❌ Bold formatting without config test failed")
        return
    
    print("\n" + "=" * 70)
    print("🎉 Bin Bold Format Test Completed!")
    print("\nFormatting Changes:")
    print("✅ A14~AXX bin names now use bold font")
    print("✅ Same font as headers (bold, consistent styling)")
    print("✅ Works with and without configuration files")
    print("✅ Test files saved in test/ folder only")
    
    print(f"\nExcel Formatting Details:")
    print(f"• A13: 'Category' header (bold)")
    print(f"• A14+: Bin names (BOLD)")
    print(f"  - With config: '64(TEST OPEN SHORT TEST)' (bold)")
    print(f"  - Without config: '64' (bold)")
    print(f"• B14+: Quantities (normal font)")
    print(f"• C14+: Yield percentages (normal font)")
    
    print(f"\nTest Files Created:")
    print(f"• test/test_bin_bold_with_config.xlsx")
    print(f"• test/test_bin_bold_no_config.xlsx")
    print(f"• All test files contained in test/ folder")


if __name__ == "__main__":
    main()
