#!/usr/bin/env python3
"""
Bump Map Advanced Tool - Enhanced Version
实现worker_file15.txt中要求的所有增强功能

增强功能:
1. Test House选择增加Clear功能
2. Bump Map多选功能
3. 输出路径自动化
4. UI优化

Author: Yuribytes
Company: Chipone TE development Team
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import os
from datetime import datetime

class BumpMapEnhancedFrame:
    def __init__(self, parent, app_controller):
        self.parent = parent
        self.app_controller = app_controller

        # 创建主框架（移除滚动框架，与其他工具保持一致）
        self.main_frame = ttk.Frame(self.parent, padding="10")

        # 配置网格权重
        self.parent.columnconfigure(0, weight=1)
        self.parent.rowconfigure(0, weight=1)
        self.main_frame.columnconfigure(0, weight=1)

        # 调用后初始化
        self.__post_init__()

    def show(self):
        """显示工具界面"""
        self.main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

    def hide(self):
        """隐藏工具界面"""
        self.main_frame.grid_remove()

    # 初始化变量 (增强版)
    def __post_init__(self):
        """后初始化方法"""
        # 处理器和状态 (先初始化，确保属性存在)
        self.bump_processor = None
        self.processing_count = 0

        # 初始化变量 (增强版)
        self.init_variables()

        # 创建GUI组件
        self.create_widgets()
    
    def init_variables(self):
        """初始化所有变量 (增强版)"""
        # 文件路径 (支持多个Bump Map)
        self.bump_map_files = []  # 存储多个bump map文件路径
        self.bump_map_display = tk.StringVar(value="No bump map files selected")
        self.dummy_map_file_path = tk.StringVar()
        self.output_directory_path = tk.StringVar()  # 输出目录路径
        
        # 测试厂选择
        self.selected_test_house = tk.StringVar(value="")
        self.selected_test_house_display = tk.StringVar(value="No test house selected")
        
        # Map版本检测
        self.detected_map_version = tk.IntVar(value=0)
        self.map_version_info = tk.StringVar(value="Map Version: Not detected")
        
        # 动态配置 (按要求修正默认值)
        self.pass_value = tk.IntVar(value=0)  # 默认Pass=0
        self.fail_value = tk.IntVar(value=59)  # 默认Fail=59
        
        # 状态
        self.status_var = tk.StringVar(value="Ready - Select bump and dummy map files")
        
        # 测试厂数据 (优先级排序)
        self.init_test_houses()
    
    def init_test_houses(self):
        """初始化测试厂数据 (按优先级排序)"""
        # 优先级测试厂 (按用户要求排序)
        priority_test_houses = [
            ("Chipmore", "Chipmore Technologies"),
            ("Unisem", "Unisem Group"),
            ("TongFu", "TongFu Microelectronics"),
            ("ChipMOS", "ChipMOS Technologies"),
            ("NEPES", "NEPES Corporation"),
            ("Chipbond", "Chipbond Technology Corporation")
        ]
        
        # 其他测试厂 (按字母排序)
        other_test_houses = [
            ("Amkor", "Amkor Technology"),
            ("ASE", "ASE Semiconductor"),
            ("ChipPAC", "ChipPAC Inc."),
            ("FlipChip", "FlipChip International"),
            ("Hana", "Hana Micron"),
            ("JCET", "JCET Group"),
            ("King Yuan", "King Yuan Electronics"),
            ("Lingsen", "Lingsen Precision Industries"),
            ("OSAT", "OSAT Solutions"),
            ("PTI", "PTI Technologies"),
            ("SPIL", "Siliconware Precision Industries"),
            ("STATS", "STATS ChipPAC"),
            ("Tianshui", "Tianshui Huatian"),
            ("UTAC", "UTAC Holdings")
        ]
        
        # 合并测试厂列表
        self.test_houses = {}
        for test_id, test_name in priority_test_houses + other_test_houses:
            self.test_houses[test_id] = test_name
    
    def create_widgets(self):
        """创建所有GUI组件"""
        # 标题
        title_label = ttk.Label(self.main_frame, text="🔧 Bump Map Advanced Tool (Enhanced)",
                               font=("Arial", 14, "bold"))
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 10))

        # 文件选择区域
        self.create_file_selection_section()

        # 测试厂选择区域
        self.create_test_house_section()

        # Map版本显示区域
        self.create_map_version_section()

        # 动态配置区域
        self.create_configuration_section()

        # 状态栏
        self.create_status_bar()

        # 控制按钮
        self.create_control_buttons()
    
    def create_file_selection_section(self):
        """创建文件选择区域 (增强版)"""
        file_frame = ttk.LabelFrame(self.main_frame, text="📁 File Selection (Enhanced)", padding="8")
        file_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 5))
        file_frame.columnconfigure(1, weight=1)
        
        # Bump map files (多选)
        ttk.Label(file_frame, text="Bump Map Files:").grid(row=0, column=0, sticky=tk.W, pady=(0, 5))
        
        # Bump map显示区域
        bump_display_frame = ttk.Frame(file_frame)
        bump_display_frame.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(10, 5), pady=(0, 5))
        bump_display_frame.columnconfigure(0, weight=1)
        
        # 显示选中的bump map文件
        bump_display_label = ttk.Label(bump_display_frame, textvariable=self.bump_map_display,
                                      font=("Arial", 9), foreground="blue",
                                      wraplength=400)
        bump_display_label.grid(row=0, column=0, sticky=(tk.W, tk.E))
        
        # Bump map按钮组
        bump_button_frame = ttk.Frame(file_frame)
        bump_button_frame.grid(row=0, column=2, pady=(0, 5))
        
        ttk.Button(bump_button_frame, text="Add Files", 
                  command=self.browse_bump_map_files).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(bump_button_frame, text="Clear", 
                  command=self.clear_bump_map_files).pack(side=tk.LEFT)
        
        # Dummy map file
        ttk.Label(file_frame, text="Dummy Map File:").grid(row=1, column=0, sticky=tk.W, pady=(0, 5))
        dummy_entry = ttk.Entry(file_frame, textvariable=self.dummy_map_file_path, width=50)
        dummy_entry.grid(row=1, column=1, sticky=(tk.W, tk.E), padx=(10, 5), pady=(0, 5))
        ttk.Button(file_frame, text="Browse", 
                  command=self.browse_dummy_map_file).grid(row=1, column=2, pady=(0, 5))
        
        # Output directory (自动命名)
        ttk.Label(file_frame, text="Output Directory:").grid(row=2, column=0, sticky=tk.W)
        output_entry = ttk.Entry(file_frame, textvariable=self.output_directory_path, width=50)
        output_entry.grid(row=2, column=1, sticky=(tk.W, tk.E), padx=(10, 5))
        ttk.Button(file_frame, text="Browse", 
                  command=self.browse_output_directory).grid(row=2, column=2)
        
        # 输出说明
        output_note = ttk.Label(file_frame, 
                               text="Files will be auto-named: BumpMapName_Timestamp_DummyMapName.ext",
                               font=("Arial", 8), foreground="gray")
        output_note.grid(row=3, column=0, columnspan=3, sticky=tk.W, pady=(5, 0))
    
    def create_test_house_section(self):
        """创建测试厂选择区域 (增强版 - 添加Clear功能)"""
        test_house_frame = ttk.LabelFrame(self.main_frame, text="🏭 Test House Selection (Enhanced)", padding="8")
        test_house_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 5))
        test_house_frame.columnconfigure(0, weight=1)
        
        # 创建水平布局
        horizontal_frame = ttk.Frame(test_house_frame)
        horizontal_frame.grid(row=0, column=0, sticky=(tk.W, tk.E))
        horizontal_frame.columnconfigure(0, weight=3)
        horizontal_frame.columnconfigure(1, weight=1)
        
        # 左侧：测试厂选择区域
        left_frame = ttk.Frame(horizontal_frame)
        left_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 10))
        
        self.create_test_house_selection(left_frame)
        
        # 右侧：选择显示和Clear按钮
        right_frame = ttk.Frame(horizontal_frame)
        right_frame.grid(row=0, column=1, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        self.create_selection_display(right_frame)
    
    def create_test_house_selection(self, parent_frame):
        """创建测试厂选择区域"""
        # 创建滚动区域
        canvas_frame = ttk.Frame(parent_frame)
        canvas_frame.pack(fill=tk.BOTH, expand=True)
        
        canvas = tk.Canvas(canvas_frame, height=100, width=450)  # 进一步减少高度
        scrollbar = ttk.Scrollbar(canvas_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        # 添加测试厂单选按钮 - 双栏布局
        row = 0
        col = 0
        for test_house_id, test_house_name in self.test_houses.items():
            radio_button = ttk.Radiobutton(scrollable_frame,
                                          text=test_house_name,
                                          variable=self.selected_test_house,
                                          value=test_house_id)
            radio_button.grid(row=row, column=col, sticky=tk.W, padx=5, pady=2)
            
            col += 1
            if col > 1:  # 两栏布局
                col = 0
                row += 1
        
        # 确保scrollable_frame有足够的列宽度
        scrollable_frame.columnconfigure(0, weight=1, minsize=200)
        scrollable_frame.columnconfigure(1, weight=1, minsize=200)
        
        # 绑定鼠标滚轮
        def bind_mousewheel_recursive(widget):
            def _on_mousewheel(event):
                canvas.yview_scroll(int(-1*(event.delta/120)), "units")
            
            widget.bind("<MouseWheel>", _on_mousewheel)
            for child in widget.winfo_children():
                bind_mousewheel_recursive(child)
        
        bind_mousewheel_recursive(canvas_frame)
        
        # 绑定选择变化事件
        self.selected_test_house.trace('w', self.on_test_house_selection_changed)
    
    def create_selection_display(self, parent_frame):
        """创建右侧选择显示区域 (增强版 - 添加Clear功能)"""
        display_frame = ttk.LabelFrame(parent_frame, text="Selected", padding="3")
        display_frame.pack(fill=tk.BOTH, expand=True)

        # 显示选中的测试厂
        self.selection_label = ttk.Label(display_frame,
                                        textvariable=self.selected_test_house_display,
                                        font=("Arial", 9, "bold"),
                                        foreground="blue",
                                        wraplength=150,
                                        justify=tk.CENTER)
        self.selection_label.pack(pady=(3, 3))

        # 添加Clear按钮 (新功能)
        clear_button = ttk.Button(display_frame, text="🗑️ Clear",
                                 command=self.clear_test_house_selection,
                                 width=10)
        clear_button.pack(pady=(0, 3))

        # 添加说明文字
        info_label = ttk.Label(display_frame,
                              text="Format for alignment",
                              font=("Arial", 7),
                              foreground="gray",
                              justify=tk.CENTER)
        info_label.pack()
    
    def create_map_version_section(self):
        """创建Map版本显示区域"""
        version_frame = ttk.LabelFrame(self.main_frame, text="🔍 Map Version (Auto-Detected)", padding="8")
        version_frame.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 5))

        version_label = ttk.Label(version_frame, textvariable=self.map_version_info,
                                 font=("Arial", 10, "bold"), foreground="green")
        version_label.pack()

    def create_configuration_section(self):
        """创建动态配置区域"""
        config_frame = ttk.LabelFrame(self.main_frame, text="⚙️ Dynamic Configuration", padding="8")
        config_frame.grid(row=4, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 5))
        config_frame.columnconfigure(1, weight=1)
        config_frame.columnconfigure(3, weight=1)
        
        # Pass值配置
        ttk.Label(config_frame, text="Pass Value (0-255):").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        pass_spinbox = ttk.Spinbox(config_frame, from_=0, to=255, textvariable=self.pass_value, width=10)
        pass_spinbox.grid(row=0, column=1, sticky=tk.W, padx=(0, 20))
        
        # Fail值配置
        ttk.Label(config_frame, text="Fail Value (0-255):").grid(row=0, column=2, sticky=tk.W, padx=(0, 10))
        fail_spinbox = ttk.Spinbox(config_frame, from_=0, to=255, textvariable=self.fail_value, width=10)
        fail_spinbox.grid(row=0, column=3, sticky=tk.W)
        
        # 格式预览 (按原始设计)
        ttk.Label(config_frame, text="Format Preview:").grid(row=1, column=0, sticky=tk.W, pady=(10, 0))
        self.format_preview_label = ttk.Label(config_frame, text="Pass: 00000000, Fail: 0000003B",
                                             font=("Courier", 9), foreground="blue")
        self.format_preview_label.grid(row=1, column=1, columnspan=3, sticky=tk.W, pady=(10, 0))
        
        # 绑定值变化事件
        self.pass_value.trace('w', self.update_format_preview)
        self.fail_value.trace('w', self.update_format_preview)
        self.detected_map_version.trace('w', self.update_format_preview)
    
    def create_status_bar(self):
        """创建状态栏"""
        status_frame = ttk.Frame(self.main_frame)
        status_frame.grid(row=5, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 5))

        ttk.Label(status_frame, text="Status:").pack(side=tk.LEFT)
        status_label = ttk.Label(status_frame, textvariable=self.status_var,
                                font=("Arial", 9), foreground="blue")
        status_label.pack(side=tk.LEFT, padx=(10, 0))

    def create_control_buttons(self):
        """创建控制按钮 (复用原始设计)"""
        button_frame = ttk.Frame(self.main_frame)
        button_frame.grid(row=6, column=0, columnspan=2, pady=(5, 5))

        # 按钮布局：Process Maps | Clear Memory | Back | Exit (完全复用原始设计)
        ttk.Button(button_frame, text="Process Maps",
                  command=self.process_maps, style="Accent.TButton").pack(side=tk.LEFT, padx=(0, 15))
        ttk.Button(button_frame, text="Clear Memory",
                  command=self.clear_memory).pack(side=tk.LEFT, padx=(0, 15))
        ttk.Button(button_frame, text="← Back",
                  command=self.return_to_selector).pack(side=tk.LEFT, padx=(0, 15))
        ttk.Button(button_frame, text="Exit",
                  command=self.exit_application).pack(side=tk.LEFT)

    # ==================== 文件操作方法 ====================

    def browse_bump_map_files(self):
        """浏览选择多个Bump Map文件 (新功能)"""
        filenames = filedialog.askopenfilenames(
            title="Select Bump Map Files (Multiple Selection)",
            filetypes=[
                ("All supported files", "*.*"),
                ("MAP files", "*.map"),
                ("TSK files", "*.tsk"),
                ("Text files", "*.txt")
            ]
        )
        if filenames:
            # 添加到现有列表中
            for filename in filenames:
                if filename not in self.bump_map_files:
                    self.bump_map_files.append(filename)

            self.update_bump_map_display()
            self.status_var.set(f"Added {len(filenames)} bump map file(s). Total: {len(self.bump_map_files)}")

    def clear_bump_map_files(self):
        """清除所有选择的Bump Map文件 (新功能)"""
        self.bump_map_files.clear()
        self.update_bump_map_display()
        self.status_var.set("Bump map files cleared")

    def update_bump_map_display(self):
        """更新Bump Map文件显示 (新功能)"""
        if not self.bump_map_files:
            self.bump_map_display.set("No bump map files selected")
        elif len(self.bump_map_files) == 1:
            filename = os.path.basename(self.bump_map_files[0])
            self.bump_map_display.set(f"1 file: {filename}")
        else:
            filenames = [os.path.basename(f) for f in self.bump_map_files[:3]]
            if len(self.bump_map_files) > 3:
                display_text = f"{len(self.bump_map_files)} files: {', '.join(filenames)}..."
            else:
                display_text = f"{len(self.bump_map_files)} files: {', '.join(filenames)}"
            self.bump_map_display.set(display_text)

    def browse_dummy_map_file(self):
        """浏览选择Dummy Map文件"""
        filename = filedialog.askopenfilename(
            title="Select Dummy Map File",
            filetypes=[
                ("All supported files", "*.*"),  # All files优先
                ("TSK files", "*.tsk"),
                ("MAP files", "*.map"),
                ("Text files", "*.txt")
            ]
        )
        if filename:
            self.dummy_map_file_path.set(filename)

            # 自动检测Map版本
            self.auto_detect_map_version(filename)

            # 自动设置输出目录
            self.auto_generate_output_directory(filename)

            self.status_var.set(f"Dummy map file selected: {os.path.basename(filename)}")

    def browse_output_directory(self):
        """浏览选择输出目录 (新功能)"""
        directory = filedialog.askdirectory(
            title="Select Output Directory"
        )
        if directory:
            self.output_directory_path.set(directory)
            self.status_var.set(f"Output directory set: {os.path.basename(directory)}")

    def auto_detect_map_version(self, dummy_file_path):
        """自动检测Map版本"""
        try:
            from tsk_map_processor import TSKMapProcessor

            processor = TSKMapProcessor()
            success = processor.read_file(dummy_file_path)

            if success:
                header_success = processor.parse_file_header()
                if header_success:
                    version = processor.May_version
                    self.detected_map_version.set(version)

                    # 只显示合理的map version (按要求修正)
                    version_text = f"Map Version: {version}"
                    self.map_version_info.set(version_text)
                    print(f"✅ Auto-detected Map Version: {version}")
                    return version
                else:
                    self.map_version_info.set("Map Version: Detection failed (header parse error)")
            else:
                self.map_version_info.set("Map Version: Detection failed (file read error)")

        except Exception as e:
            self.map_version_info.set(f"Map Version: Detection failed ({str(e)})")
            print(f"❌ Map version detection error: {e}")

        return None

    def auto_generate_output_directory(self, dummy_file_path):
        """自动生成输出目录"""
        try:
            # 使用dummy文件的目录作为默认输出目录
            output_dir = os.path.dirname(dummy_file_path)
            self.output_directory_path.set(output_dir)
            print(f"✅ Auto-set output directory: {output_dir}")
        except Exception as e:
            print(f"❌ Output directory generation error: {e}")

    def generate_output_filename(self, bump_file_path, dummy_file_path):
        """生成输出文件名 (新功能) - 修改为lot-01格式，不包含dummy map名字、时间戳和文件扩展名"""
        try:
            # 获取bump文件的basename（不使用splitext，因为.09等不是真正的扩展名）
            bump_basename = os.path.basename(bump_file_path)

            # 将点号替换为连字符 (例如: D97127.09 -> D97127-09)
            output_base = bump_basename.replace('.', '-')

            # 创建输出文件名: 只使用bump map名字，格式为lot-01（不包含扩展名）
            output_filename = output_base

            return output_filename

        except Exception as e:
            print(f"❌ Output filename generation error: {e}")
            return f"output"

    # ==================== 测试厂操作方法 ====================

    def on_test_house_selection_changed(self, *args):
        """测试厂选择变化时的回调函数"""
        selected_id = self.selected_test_house.get()
        if selected_id and selected_id in self.test_houses:
            selected_name = self.test_houses[selected_id]
            self.selected_test_house_display.set(f"✓ {selected_name}")
            self.status_var.set(f"Test house selected: {selected_name}")
        else:
            self.selected_test_house_display.set("No test house selected")
            self.status_var.set("Ready - Select bump and dummy map files")

    def clear_test_house_selection(self):
        """清除测试厂选择 (新功能)"""
        self.selected_test_house.set("")
        self.selected_test_house_display.set("No test house selected")
        self.status_var.set("Test house selection cleared - Please select again")
        print("✅ Test house selection cleared")

    # ==================== 配置和预览方法 ====================

    def update_format_preview(self, *args):
        """更新格式预览 (按要求的格式显示)"""
        try:
            pass_val = self.pass_value.get()
            fail_val = self.fail_value.get()
            map_version = self.detected_map_version.get()

            # 按照要求的格式显示
            if map_version in [2, 3]:
                # mapversion=2/3 Special，003F0000 格式 (高位在前)
                # 例如63 -> 003F0000 (不是0000003F)
                pass_hex = f"{pass_val:04X}0000"  # 高位在前，低位补0000
                fail_hex = f"{fail_val:04X}0000"  # 高位在前，低位补0000
                preview_text = f"Pass: {pass_hex}, Fail: {fail_hex} (Map v{map_version} Special)"
            elif map_version == 4:
                # mapversion=4, big-endian, 0000003F 格式 (低位在后)
                pass_hex = f"{pass_val:08X}"
                fail_hex = f"{fail_val:08X}"
                preview_text = f"Pass: {pass_hex}, Fail: {fail_hex} (Map v{map_version} big-endian)"
            else:
                # 默认格式
                pass_hex = f"{pass_val:08X}"
                fail_hex = f"{fail_val:08X}"
                preview_text = f"Pass: {pass_hex}, Fail: {fail_hex}"

            self.format_preview_label.config(text=preview_text)
        except:
            self.format_preview_label.config(text="Invalid values")

    # ==================== 处理方法 ====================

    def process_maps(self):
        """处理多个Bump Map文件 (增强版本)"""
        try:
            # 验证输入
            if not self.bump_map_files:
                self.show_custom_result_popup("Error", "Please select at least one bump map file", "error")
                return

            if not self.dummy_map_file_path.get():
                self.show_custom_result_popup("Error", "Please select a dummy map file", "error")
                return

            if not self.output_directory_path.get():
                self.show_custom_result_popup("Error", "Please select an output directory", "error")
                return

            # 验证文件存在
            for bump_file in self.bump_map_files:
                if not os.path.exists(bump_file):
                    self.show_custom_result_popup("Error", f"Bump map file does not exist: {os.path.basename(bump_file)}", "error")
                    return

            if not os.path.exists(self.dummy_map_file_path.get()):
                self.show_custom_result_popup("Error", "Dummy map file does not exist", "error")
                return

            if not os.path.exists(self.output_directory_path.get()):
                self.show_custom_result_popup("Error", "Output directory does not exist", "error")
                return

            # 验证测试厂选择
            if not self.selected_test_house.get():
                self.show_custom_result_popup("Error", "Please select a test house", "error")
                return

            # 获取配置值
            selected_test_house = self.selected_test_house.get()
            pass_val = self.pass_value.get()
            fail_val = self.fail_value.get()
            map_version = self.detected_map_version.get()

            # 处理逻辑
            if selected_test_house == "NEPES":
                self.process_nepes_batch()
            else:
                self.show_non_nepes_info(selected_test_house, map_version, pass_val, fail_val)

        except Exception as e:
            self.show_custom_result_popup("Error", f"Processing error: {str(e)}", "error")
            print(f"❌ Processing error: {e}")

    def process_nepes_batch(self):
        """批量处理NEPES文件 (新功能)"""
        try:
            from nepes_advanced_processor import NEPESAdvancedProcessor

            self.status_var.set(f"Processing {len(self.bump_map_files)} bump map(s) with NEPES Advanced Processor...")

            # 创建处理器
            self.bump_processor = NEPESAdvancedProcessor()

            # 处理每个bump map文件
            processed_files = []
            failed_files = []

            for i, bump_file in enumerate(self.bump_map_files):
                try:
                    # 生成输出文件名
                    output_filename = self.generate_output_filename(bump_file, self.dummy_map_file_path.get())
                    output_path = os.path.join(self.output_directory_path.get(), output_filename)

                    # 更新状态
                    self.status_var.set(f"Processing {i+1}/{len(self.bump_map_files)}: {os.path.basename(bump_file)}")
                    self.parent.update()

                    # 处理文件
                    success = self.bump_processor.process_advanced_nepes(
                        bump_file,
                        self.dummy_map_file_path.get(),
                        output_path,
                        self.pass_value.get(),
                        self.fail_value.get()
                    )

                    if success:
                        processed_files.append((bump_file, output_path))
                        print(f"✅ Processed: {os.path.basename(bump_file)} → {output_filename}")
                    else:
                        failed_files.append(bump_file)
                        print(f"❌ Failed: {os.path.basename(bump_file)}")

                except Exception as e:
                    failed_files.append(bump_file)
                    print(f"❌ Error processing {os.path.basename(bump_file)}: {e}")

            # 显示结果
            self.show_batch_results(processed_files, failed_files)

            # 更新处理计数
            self.processing_count += len(processed_files)

        except ImportError:
            self.show_custom_result_popup("Error", "NEPES Advanced Processor not available", "error")
        except Exception as e:
            self.show_custom_result_popup("Error", f"Batch processing error: {str(e)}", "error")

    def show_non_nepes_info(self, selected_test_house, map_version, pass_val, fail_val):
        """显示非NEPES测试厂信息"""
        selected_house_name = self.test_houses.get(selected_test_house, selected_test_house)
        info_msg = (
            f"Selected Configuration:\n"
            f"• Test House: {selected_house_name}\n"
            f"• Map Version: {map_version} (Auto-detected)\n"
            f"• Pass Value: {pass_val}\n"
            f"• Fail Value: {fail_val}\n"
            f"• Files to Process: {len(self.bump_map_files)}\n\n"
            f"ℹ️ Processing Logic:\n"
            f"Advanced processing is currently implemented for NEPES Corporation only.\n\n"
            f"For {selected_house_name}:\n"
            f"• Processing logic is not yet implemented\n"
            f"• Will be added in future updates\n"
            f"• Current focus is on NEPES format optimization\n\n"
            f"Please select 'NEPES Corporation' to use the advanced processing features."
        )

        self.show_custom_result_popup("Processing Info", info_msg, "info")
        self.status_var.set(f"Ready - {selected_house_name} selected (processing not implemented)")

    def show_batch_results(self, processed_files, failed_files):
        """显示批处理结果 (新功能)"""
        if processed_files and not failed_files:
            # 全部成功
            success_msg = self.create_batch_success_message(processed_files)
            self.show_custom_result_popup("Batch Processing Complete", success_msg, "success")
            self.status_var.set(f"Batch processing completed - {len(processed_files)} files processed")
        elif processed_files and failed_files:
            # 部分成功
            partial_msg = self.create_partial_success_message(processed_files, failed_files)
            self.show_custom_result_popup("Partial Success", partial_msg, "warning")
            self.status_var.set(f"Partial success - {len(processed_files)}/{len(self.bump_map_files)} files processed")
        else:
            # 全部失败
            self.show_custom_result_popup("Processing Failed", f"Failed to process all {len(self.bump_map_files)} files. Check console for details.", "error")
            self.status_var.set("Batch processing failed - Check inputs and try again")

    def create_batch_success_message(self, processed_files):
        """创建批处理成功消息 (新功能)"""
        try:
            file_list = []
            for bump_file, output_path in processed_files[:5]:  # 显示前5个文件
                file_list.append(f"• {os.path.basename(bump_file)} → {os.path.basename(output_path)}")

            if len(processed_files) > 5:
                file_list.append(f"• ... and {len(processed_files) - 5} more files")

            success_msg = (
                f"🎉 Batch Processing Completed Successfully!\n\n"
                f"📊 Summary:\n"
                f"• Files Processed: {len(processed_files)}\n"
                f"• Map Version: {self.detected_map_version.get()}\n"
                f"• Pass Value: {self.pass_value.get()}\n"
                f"• Fail Value: {self.fail_value.get()}\n\n"
                f"📁 Output Files:\n" + "\n".join(file_list) + f"\n\n"
                f"📍 Output Directory: {os.path.basename(self.output_directory_path.get())}"
            )

            return success_msg

        except Exception as e:
            return f"✅ Batch processing completed!\n{len(processed_files)} files processed successfully."

    def create_partial_success_message(self, processed_files, failed_files):
        """创建部分成功消息 (新功能)"""
        success_list = [f"✅ {os.path.basename(f[0])}" for f in processed_files[:3]]
        if len(processed_files) > 3:
            success_list.append(f"✅ ... and {len(processed_files) - 3} more")

        failed_list = [f"❌ {os.path.basename(f)}" for f in failed_files[:3]]
        if len(failed_files) > 3:
            failed_list.append(f"❌ ... and {len(failed_files) - 3} more")

        partial_msg = (
            f"⚠️ Partial Processing Results\n\n"
            f"✅ Successfully Processed ({len(processed_files)}):\n" + "\n".join(success_list) + "\n\n"
            f"❌ Failed ({len(failed_files)}):\n" + "\n".join(failed_list) + "\n\n"
            f"Check console for detailed error messages."
        )

        return partial_msg

    def show_custom_result_popup(self, title: str, message: str, popup_type: str = "info"):
        """显示自定义的居中结果弹窗 - 优化确认按钮显示"""
        try:
            # Create popup window
            popup = tk.Toplevel(self.app_controller.root)
            popup.title(title)
            popup.resizable(False, False)

            # Center popup on parent window
            popup.transient(self.app_controller.root)
            popup.grab_set()

            # 优化大小计算 - 确保确认按钮可见
            lines = message.count('\n') + 1
            max_line_length = max(len(line) for line in message.split('\n')) if message.strip() else 20

            # 更保守的宽度计算，确保内容完整显示
            estimated_width = min(max(450, max_line_length * 7 + 80), 650)
            # 增加高度余量，确保按钮区域有足够空间
            estimated_height = min(max(250, lines * 22 + 150), 550)

            popup.geometry(f"{estimated_width}x{estimated_height}")

            # Center popup
            popup.update_idletasks()  # Ensure geometry is calculated
            x = self.app_controller.root.winfo_x() + (self.app_controller.root.winfo_width() // 2) - (estimated_width // 2)
            y = self.app_controller.root.winfo_y() + (self.app_controller.root.winfo_height() // 2) - (estimated_height // 2)
            popup.geometry(f"{estimated_width}x{estimated_height}+{x}+{y}")

            # Create content frame with better padding
            main_frame = ttk.Frame(popup, padding="15")
            main_frame.pack(fill=tk.BOTH, expand=True)

            # Icon based on type
            icon_text = "🎉" if popup_type == "success" else "⚠️" if popup_type == "warning" else "❌"

            # Title with icon
            title_frame = ttk.Frame(main_frame)
            title_frame.pack(fill=tk.X, pady=(0, 15))

            ttk.Label(title_frame, text=f"{icon_text} {title}",
                     font=("Arial", 12, "bold")).pack()

            # Message content with scrollbar if needed
            if lines > 12:  # 降低滚动条阈值，避免内容过长
                text_frame = ttk.Frame(main_frame)
                text_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 15))

                text_widget = tk.Text(text_frame, wrap=tk.WORD, height=12, width=55, font=("Arial", 9))
                scrollbar = ttk.Scrollbar(text_frame, orient=tk.VERTICAL, command=text_widget.yview)
                text_widget.configure(yscrollcommand=scrollbar.set)

                text_widget.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
                scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

                text_widget.insert(tk.END, message)
                text_widget.configure(state=tk.DISABLED)
            else:
                # Simple label for shorter messages
                message_label = ttk.Label(main_frame, text=message,
                                        justify=tk.LEFT, wraplength=estimated_width-50,
                                        font=("Arial", 9))
                message_label.pack(fill=tk.BOTH, expand=True, pady=(0, 15))

            # 确认按钮区域 - 固定在底部
            button_frame = ttk.Frame(main_frame)
            button_frame.pack(fill=tk.X, side=tk.BOTTOM)

            # 增大确认按钮，确保易于点击
            ok_button = ttk.Button(button_frame, text="确定",
                                  command=popup.destroy, width=12)
            ok_button.pack(side=tk.RIGHT, pady=(10, 0))

            # Allow manual close with Escape key
            popup.bind('<Escape>', lambda e: popup.destroy())
            popup.bind('<Return>', lambda e: popup.destroy())
            popup.focus_set()

        except Exception as e:
            print(f"Warning: Error showing custom result popup: {e}")
            # Fallback to standard messagebox
            if popup_type == "success":
                messagebox.showinfo(title, message)
            elif popup_type == "warning":
                messagebox.showwarning(title, message)
            else:
                messagebox.showerror(title, message)

    # ==================== 内存管理方法 ====================

    def clear_memory(self):
        """Clear memory and reset state (完全复用原始设计)"""
        try:
            memory_freed = 0.0
            processor_count = 0

            # Clear bump processor (similar to Full Map Tool's processors)
            if hasattr(self, 'bump_processor') and self.bump_processor:
                if hasattr(self.bump_processor, 'get_memory_usage_mb'):
                    memory_freed += self.bump_processor.get_memory_usage_mb()
                if hasattr(self.bump_processor, 'clear_memory'):
                    self.bump_processor.clear_memory()
                self.bump_processor = None
                processor_count = 1

            # Reset counters
            if hasattr(self, 'processing_count'):
                self.processing_count = 0

            # Always provide feedback to user (consistent with Full Map Tool)
            if memory_freed > 0:
                print(f"Bump Map Enhanced Tool - Total memory cleared: ~{memory_freed:.1f} MB from {processor_count} processors")
                if hasattr(self, 'status_var'):
                    self.status_var.set(f"Memory cleared: {memory_freed:.1f} MB freed from {processor_count} processors")
            else:
                print("Bump Map Enhanced Tool - Memory cleared (no active processors)")
                if hasattr(self, 'status_var'):
                    self.status_var.set("Memory cleared - No active processors to free")

        except Exception as e:
            print(f"Warning: Error during Bump Map Enhanced memory cleanup: {e}")
            if hasattr(self, 'status_var'):
                self.status_var.set("Memory cleanup completed with warnings")
            else:
                print("Status variable not available for error message")

    # ==================== 退出和返回方法 ====================

    def return_to_selector(self):
        """Return to tool selector with automatic memory cleanup (完全复用原始功能)"""
        try:
            # Get memory info before clearing (for accurate reporting)
            memory_before_clear = 0.0
            processor_count = 1 if hasattr(self, 'bump_processor') and self.bump_processor else 0

            if hasattr(self, 'bump_processor') and self.bump_processor and hasattr(self.bump_processor, 'get_memory_usage_mb'):
                memory_before_clear = self.bump_processor.get_memory_usage_mb()

            # Clear memory automatically
            self.clear_memory()
            print(f"Bump Map Enhanced Tool - Memory cleared when returning to main menu: {memory_before_clear:.1f} MB freed")

            # Verify memory was actually cleared
            memory_after_clear = 0.0
            if hasattr(self, 'bump_processor') and self.bump_processor and hasattr(self.bump_processor, 'get_memory_usage_mb'):
                memory_after_clear = self.bump_processor.get_memory_usage_mb()

            # Calculate actual memory freed
            actual_memory_freed = memory_before_clear - memory_after_clear
            print(f"Bump Map Enhanced Tool - Actual memory freed: {actual_memory_freed:.1f} MB (before: {memory_before_clear:.1f} MB, after: {memory_after_clear:.1f} MB)")

            # Clear state when returning (with safe attribute access)
            if hasattr(self, 'bump_map_files'):
                self.bump_map_files.clear()
            if hasattr(self, 'bump_map_display'):
                self.bump_map_display.set("No bump map files selected")
            if hasattr(self, 'dummy_map_file_path'):
                self.dummy_map_file_path.set("")
            if hasattr(self, 'output_directory_path'):
                self.output_directory_path.set("")
            if hasattr(self, 'selected_test_house'):
                self.selected_test_house.set("")
            if hasattr(self, 'selected_test_house_display'):
                self.selected_test_house_display.set("No test house selected")
            if hasattr(self, 'detected_map_version'):
                self.detected_map_version.set(0)
            if hasattr(self, 'map_version_info'):
                self.map_version_info.set("Map Version: Not detected")
            if hasattr(self, 'status_var'):
                self.status_var.set("Ready - Select bump and dummy map files")

            # Show 1-second cleanup popup with actual freed memory
            self.show_return_cleanup_popup(actual_memory_freed, processor_count)

            # Return to selector after popup
            self.app_controller.root.after(1100, self.app_controller.show_tool_selector)

        except Exception as e:
            print(f"Warning: Error during Bump Map Enhanced return cleanup: {e}")
            # Still return to selector even if cleanup fails
            self.app_controller.show_tool_selector()

    def exit_application(self):
        """Exit application with cleanup (完全复用原始功能)"""
        try:
            # Get memory info before clearing (similar to Full Map Tool)
            memory_freed = 0.0
            processor_count = 0

            # Calculate memory usage from bump processor
            if hasattr(self, 'bump_processor') and self.bump_processor and hasattr(self.bump_processor, 'get_memory_usage_mb'):
                memory_freed += self.bump_processor.get_memory_usage_mb()
                processor_count = 1

            # Clear all memory before exit
            self.clear_memory()
            print("Bump Map Enhanced Tool - Application memory cleared before exit")

            # Always show cleanup popup when exiting (user feedback) - matching Full Map Tool
            self.show_memory_cleanup_popup(memory_freed, processor_count)
            # Wait a moment for popup to show before quitting
            self.app_controller.root.after(2100, self.app_controller.root.quit)

        except Exception as e:
            print(f"Warning: Error during Bump Map Enhanced exit cleanup: {e}")
            self.app_controller.root.quit()

    def show_return_cleanup_popup(self, memory_freed: float, processor_count: int):
        """Show 1-second cleanup popup when returning to main menu (完全复用原始功能)"""
        try:
            # Create popup window
            popup = tk.Toplevel(self.app_controller.root)
            popup.title("Memory Cleanup")
            popup.geometry("350x120")
            popup.resizable(False, False)

            # Center popup on parent window
            popup.transient(self.app_controller.root)
            popup.grab_set()

            # Center popup
            x = self.app_controller.root.winfo_x() + (self.app_controller.root.winfo_width() // 2) - 175
            y = self.app_controller.root.winfo_y() + (self.app_controller.root.winfo_height() // 2) - 60
            popup.geometry(f"350x120+{x}+{y}")

            # Create content
            main_frame = ttk.Frame(popup, padding="20")
            main_frame.pack(fill=tk.BOTH, expand=True)

            # Icon and message
            if memory_freed > 0:
                message = f"🧹 Returning to Main Menu\n\n{memory_freed:.1f} MB memory freed"
            elif processor_count > 0:
                message = f"🧹 Returning to Main Menu\n\nProcessors cleared"
            else:
                message = "🧹 Returning to Main Menu\n\nReady for next operation"

            ttk.Label(main_frame, text=message, justify=tk.CENTER,
                     font=("Arial", 10)).pack(expand=True)

            # Auto-close after 1 second
            popup.after(1000, popup.destroy)

            # Allow manual close with Escape key
            popup.bind('<Escape>', lambda e: popup.destroy())
            popup.focus_set()

        except Exception as e:
            print(f"Warning: Error showing Bump Map Enhanced return cleanup popup: {e}")

    def show_memory_cleanup_popup(self, memory_freed: float, processor_count: int):
        """Show memory cleanup popup (完全复用原始功能)"""
        try:
            # Create popup window
            popup = tk.Toplevel(self.app_controller.root)
            popup.title("Memory Cleanup")
            popup.geometry("350x120")
            popup.resizable(False, False)

            # Center popup on parent window
            popup.transient(self.app_controller.root)
            popup.grab_set()

            # Center popup
            x = self.app_controller.root.winfo_x() + (self.app_controller.root.winfo_width() // 2) - 175
            y = self.app_controller.root.winfo_y() + (self.app_controller.root.winfo_height() // 2) - 60
            popup.geometry(f"350x120+{x}+{y}")

            # Create content
            main_frame = ttk.Frame(popup, padding="20")
            main_frame.pack(fill=tk.BOTH, expand=True)

            # Icon and message
            if memory_freed > 0:
                message = f"✅ Memory Cleanup Complete\n\n{memory_freed:.1f} MB freed from {processor_count} processors"
            elif processor_count > 0:
                message = f"✅ Memory Cleanup Complete\n\nProcessors cleared (no memory to free)"
            else:
                message = "✅ Application Closing\n\nThank you for using Bump Map Enhanced Tool!"

            ttk.Label(main_frame, text=message, justify=tk.CENTER,
                     font=("Arial", 10)).pack(expand=True)

            # Auto-close after 2 seconds
            popup.after(2000, popup.destroy)

            # Allow manual close with Escape key
            popup.bind('<Escape>', lambda e: popup.destroy())
            popup.focus_set()

        except Exception as e:
            print(f"Warning: Error showing Bump Map Enhanced cleanup popup: {e}")


def main():
    """测试函数"""
    root = tk.Tk()
    root.title("Bump Map Enhanced Tool Test")
    root.geometry("1000x900")  # 增加高度避免按钮遮挡

    # Mock app controller for testing
    class MockController:
        def __init__(self, root):
            self.root = root
        def show_tool_selector(self):
            print("Would return to tool selector")
            return True

    controller = MockController(root)
    enhanced_tool = BumpMapEnhancedFrame(root, controller)

    root.mainloop()


if __name__ == "__main__":
    main()
