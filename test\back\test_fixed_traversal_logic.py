#!/usr/bin/env python3
"""
测试修复后的遍历逻辑
验证x=79,y=0现在是否正确写入位置15365

Author: Yuribytes
Company: Chipone TE development Team
"""

import os
import sys

# 添加父目录到路径
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, parent_dir)

from nepes_enhanced_processor import NEPESEnhancedProcessor

def test_fixed_traversal():
    """测试修复后的遍历逻辑"""
    print("🧪 测试修复后的遍历逻辑")
    print("验证x=79,y=0是否正确写入位置15365")
    print("=" * 60)
    
    bump_file = "D97127.09"
    dummy_file = "009.NNS157-09-E4"
    output_file = "fixed_traversal_test.tsk"
    
    # 读取原始文件
    with open(dummy_file, 'rb') as f:
        original_data = f.read()
    
    print(f"📊 关键位置原始数据:")
    print(f"   位置15365-15368: {original_data[15365:15369].hex().upper()}")
    print(f"   位置18085-18088: {original_data[18085:18089].hex().upper()}")
    
    # 执行NEPES处理
    print(f"\n🚀 执行NEPES Enhanced处理...")
    processor = NEPESEnhancedProcessor()
    success = processor.process_nepes_enhanced(bump_file, dummy_file, output_file)
    
    if not success:
        print("❌ 处理失败")
        return False
    
    # 读取处理后文件
    with open(output_file, 'rb') as f:
        processed_data = f.read()
    
    print(f"\n📊 处理后数据:")
    print(f"   位置15365-15368: {processed_data[15365:15369].hex().upper()}")
    print(f"   位置18085-18088: {processed_data[18085:18089].hex().upper()}")
    
    # 验证结果
    print(f"\n🎯 验证结果:")
    
    # x=79,y=0验证
    pos_79_0 = processed_data[15365:15369]
    expected_79_0 = "0000003F"
    actual_79_0 = pos_79_0.hex().upper()
    
    print(f"   x=79,y=0 (位置15365-15368):")
    print(f"     预期: {expected_79_0}")
    print(f"     实际: {actual_79_0}")
    print(f"     正确: {'✅' if actual_79_0 == expected_79_0 else '❌'}")
    
    # x=149,y=2验证
    pos_149_2 = processed_data[18085:18089]
    expected_149_2 = "0000003B"
    actual_149_2 = pos_149_2.hex().upper()
    
    print(f"   x=149,y=2 (位置18085-18088):")
    print(f"     预期: {expected_149_2}")
    print(f"     实际: {actual_149_2}")
    print(f"     正确: {'✅' if actual_149_2 == expected_149_2 else '❌'}")
    
    # 检查是否还有偏移问题
    print(f"\n🔍 检查位置偏移问题:")
    
    # 检查x=79,y=0周围位置
    for offset in range(-2, 6):
        pos = 15365 + offset
        if 0 <= pos < len(processed_data):
            original_byte = original_data[pos]
            processed_byte = processed_data[pos]
            
            marker = ""
            if 15365 <= pos <= 15368:
                marker = " ← 目标位置"
            
            if original_byte != processed_byte:
                print(f"     位置{pos}: {original_byte:02X} → {processed_byte:02X} (变化){marker}")
            else:
                print(f"     位置{pos}: {original_byte:02X} → {processed_byte:02X} (不变){marker}")
    
    # 总结
    success_79 = actual_79_0 == expected_79_0
    success_149 = actual_149_2 == expected_149_2
    
    if success_79 and success_149:
        print(f"\n🎉 遍历逻辑修复成功!")
        print(f"✅ x=79,y=0正确写入位置15365")
        print(f"✅ x=149,y=2正确写入位置18085")
        print(f"✅ 使用TSK处理器的1-based索引逻辑")
        return True
    else:
        print(f"\n❌ 遍历逻辑仍有问题")
        if not success_79:
            print(f"❌ x=79,y=0写入错误")
        if not success_149:
            print(f"❌ x=149,y=2写入错误")
        return False

def compare_with_tsk_logic():
    """对比TSK处理器逻辑"""
    print(f"\n🔍 对比TSK处理器逻辑")
    print("=" * 40)
    
    # 手动计算TSK处理器的位置
    columnsize = 305
    TestResultCategory = 15049
    
    test_positions = [
        {"x": 79, "y": 0, "description": "x=79,y=0"},
        {"x": 149, "y": 2, "description": "x=149,y=2"}
    ]
    
    print("📊 TSK处理器位置计算:")
    for pos_info in test_positions:
        x, y = pos_info["x"], pos_info["y"]
        
        # TSK处理器逻辑 (1-based)
        i = y + 1  # Convert to 1-based
        j = x + 1  # Convert to 1-based
        die_index = (i - 1) * columnsize + j - 1
        category_pos = TestResultCategory + 4 * die_index
        
        print(f"\n   {pos_info['description']}:")
        print(f"     1-based坐标: i={i}, j={j}")
        print(f"     die_index: ({i}-1) * {columnsize} + {j}-1 = {die_index}")
        print(f"     category_pos: {TestResultCategory} + 4 * {die_index} = {category_pos}")
        
        # 验证预期位置
        if pos_info["description"] == "x=79,y=0":
            expected = 15365
        else:  # x=149,y=2
            expected = 18085
            
        print(f"     预期位置: {expected}")
        print(f"     计算匹配: {'✅' if category_pos == expected else '❌'}")

def main():
    """主测试函数"""
    print("🧪 修复后遍历逻辑测试")
    print("=" * 70)
    
    # 切换到测试目录
    os.chdir(os.path.dirname(os.path.abspath(__file__)))
    
    try:
        # 测试1: 修复后的遍历逻辑
        result1 = test_fixed_traversal()
        
        # 测试2: 对比TSK逻辑
        compare_with_tsk_logic()
        
        print("\n" + "=" * 70)
        print("🎉 测试结果总结:")
        print(f"   遍历逻辑修复: {'✅ 成功' if result1 else '❌ 失败'}")
        
        if result1:
            print(f"\n🎯 修复完成!")
            print(f"✅ 完全复用TSK处理器的1-based索引逻辑")
            print(f"✅ x=79,y=0正确写入位置15365 (不再偏移)")
            print(f"✅ 解决了(304,7)越界问题的根本原因")
        else:
            print(f"\n❌ 仍需进一步调试")
            
        return result1
            
    except Exception as e:
        print(f"❌ 测试过程出错: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
