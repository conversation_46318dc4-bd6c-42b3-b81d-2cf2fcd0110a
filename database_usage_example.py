#!/usr/bin/env python3
"""
Database Configuration Usage Example
Demonstrates how to use the database-driven configuration system
"""

import os
from database_config_reader import DatabaseConfigReader
from config_database_manager import ConfigDatabaseManager
from tmb_processor import TMBProcessor


def example_1_migrate_excel_to_database():
    """示例1: 将Excel配置迁移到数据库"""
    print("=" * 60)
    print("示例1: Excel配置迁移到数据库")
    print("=" * 60)
    
    # 初始化数据库配置读取器
    db_reader = DatabaseConfigReader("production_config.db")
    
    # 迁移Excel文件到数据库
    excel_file = "test/CP1_program_bin.xlsx"
    config_name = "Production_CP1_Config"
    
    if os.path.exists(excel_file):
        if db_reader.migrate_from_excel(excel_file, config_name):
            print(f"✅ 成功迁移配置 '{config_name}' 到数据库")
        else:
            print("❌ 迁移失败")
    else:
        print(f"❌ Excel文件不存在: {excel_file}")


def example_2_auto_config_selection():
    """示例2: 自动配置选择"""
    print("=" * 60)
    print("示例2: 自动配置选择")
    print("=" * 60)
    
    # 初始化数据库配置读取器
    db_reader = DatabaseConfigReader("production_config.db")
    
    # 定义筛选条件
    filters = {
        'device_name': 'ICNA3509WAA-P1-P',  # 设备名称
        'test_flow': 'CP1',                  # 测试流程
        'vendor_name': 'CHANGHOU'            # 供应商名称
    }
    
    print(f"筛选条件: {filters}")
    
    # 自动选择配置
    if db_reader.load_config_auto(filters):
        print("✅ 自动选择配置成功")
        print(f"   测试程序: {db_reader.get_test_program_name()}")
        print(f"   设备名称: {db_reader.get_device_name()}")
        print(f"   测试流程: {db_reader.get_test_flow()}")
        print(f"   供应商: {db_reader.get_vendor_name()}")
        print(f"   Bin映射数量: {len(db_reader.get_all_bin_mappings())}")
    else:
        print("❌ 自动选择配置失败")


def example_3_tmb_generation_with_database():
    """示例3: 使用数据库配置生成TMB文件"""
    print("=" * 60)
    print("示例3: 使用数据库配置生成TMB文件")
    print("=" * 60)
    
    # 初始化支持数据库的TMB处理器
    tmb_processor = TMBProcessor(use_database=True, db_path="production_config.db")
    
    # 自动加载配置
    auto_filters = {
        'device_name': 'ICNA3509WAA-P1-P',
        'test_flow': 'CP1'
    }
    
    if tmb_processor.load_config_from_database(auto_filters=auto_filters):
        print("✅ 数据库配置加载成功")
        
        # 处理MAP文件生成TMB
        map_file = "test/back/019.3AD416-19-F4"
        if os.path.exists(map_file):
            tmb_output = tmb_processor.process_map_to_tmb(map_file)
            if tmb_output:
                print(f"✅ TMB文件生成成功: {os.path.basename(tmb_output)}")
            else:
                print("❌ TMB文件生成失败")
        else:
            print(f"❌ MAP文件不存在: {map_file}")
    else:
        print("❌ 数据库配置加载失败")


def example_4_config_management():
    """示例4: 配置管理操作"""
    print("=" * 60)
    print("示例4: 配置管理操作")
    print("=" * 60)
    
    # 初始化配置管理器
    manager = ConfigDatabaseManager("production_config.db")
    
    # 列出所有配置
    print("当前可用配置:")
    configs = manager.list_configs_detailed()
    for config in configs:
        print(f"  - {config['config_name']}")
        print(f"    设备: {config['device_name']}")
        print(f"    测试流程: {config['test_flow']}")
        print(f"    Bin数量: {config['bin_count']}")
        print(f"    使用次数: {config['usage_count']}")
        print(f"    更新时间: {config['updated_at']}")
        print()
    
    # 导出配置到JSON
    if configs:
        config_to_export = configs[0]
        export_file = f"{config_to_export['config_name']}_backup.json"
        
        if manager.export_config_to_json(config_to_export['id'], export_file):
            print(f"✅ 配置导出成功: {export_file}")
            
            # 清理导出文件
            if os.path.exists(export_file):
                os.remove(export_file)
        else:
            print("❌ 配置导出失败")
    
    manager.close()


def example_5_usage_statistics():
    """示例5: 使用统计分析"""
    print("=" * 60)
    print("示例5: 使用统计分析")
    print("=" * 60)
    
    # 初始化配置管理器
    manager = ConfigDatabaseManager("production_config.db")
    
    # 获取使用统计
    stats = manager.get_usage_statistics()
    
    print("最常用的配置:")
    for item in stats['most_used'][:5]:
        print(f"  {item['config_name']}: {item['usage_count']} 次")
    
    print("\n最近使用记录:")
    for item in stats['recent_usage'][:10]:
        print(f"  {item['config_name']} - {item['used_at']} ({item['context']})")
    
    manager.close()


def example_6_performance_comparison():
    """示例6: 性能对比"""
    print("=" * 60)
    print("示例6: Excel vs 数据库性能对比")
    print("=" * 60)
    
    import time
    
    # Excel加载性能测试
    from config_reader import ConfigReader
    excel_reader = ConfigReader()
    
    start_time = time.time()
    excel_success = excel_reader.read_config_file("test/CP1_program_bin.xlsx")
    excel_time = time.time() - start_time
    
    if excel_success:
        print(f"Excel加载时间: {excel_time:.4f} 秒")
        print(f"Excel Bin映射数量: {len(excel_reader.get_all_bin_mappings())}")
    
    # 数据库加载性能测试
    db_reader = DatabaseConfigReader("production_config.db")
    
    start_time = time.time()
    db_success = db_reader.load_config_by_name("Production_CP1_Config")
    db_time = time.time() - start_time
    
    if db_success:
        print(f"数据库加载时间: {db_time:.4f} 秒")
        print(f"数据库 Bin映射数量: {len(db_reader.get_all_bin_mappings())}")
        
        if excel_time > 0 and db_time > 0:
            speedup = excel_time / db_time
            print(f"数据库比Excel快 {speedup:.2f} 倍")


def main():
    """主函数 - 运行所有示例"""
    print("数据库配置系统使用示例")
    print("=" * 60)
    
    examples = [
        ("Excel配置迁移", example_1_migrate_excel_to_database),
        ("自动配置选择", example_2_auto_config_selection),
        ("TMB生成集成", example_3_tmb_generation_with_database),
        ("配置管理操作", example_4_config_management),
        ("使用统计分析", example_5_usage_statistics),
        ("性能对比测试", example_6_performance_comparison),
    ]
    
    for name, example_func in examples:
        print(f"\n🚀 运行示例: {name}")
        try:
            example_func()
            print(f"✅ {name} 完成")
        except Exception as e:
            print(f"❌ {name} 出错: {e}")
    
    print("\n" + "=" * 60)
    print("所有示例运行完成!")
    print("=" * 60)
    
    print("\n📚 使用指南:")
    print("1. 迁移现有Excel配置到数据库")
    print("2. 使用自动筛选功能选择合适的配置")
    print("3. 在TMB处理器中启用数据库支持")
    print("4. 使用命令行工具管理配置")
    print("5. 监控配置使用统计")
    
    print("\n🔧 命令行工具:")
    print("python config_database_manager.py --db production_config.db list")
    print("python config_database_manager.py --db production_config.db stats")
    print("python config_database_manager.py --db production_config.db export 1 backup.json")


if __name__ == "__main__":
    main()
