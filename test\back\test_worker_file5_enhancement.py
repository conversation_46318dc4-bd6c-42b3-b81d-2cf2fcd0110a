#!/usr/bin/env python3
"""
Test script for worker_file5 enhancement
Tests the new configuration reading and Bin_Summary sheet enhancement features
"""

import os
import sys
import tempfile
from openpyxl import Workbook

# Add parent directory to path to import modules
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_config_reader_enhancement():
    """Test the enhanced ConfigReader with D5, F5, H5 cell reading"""
    print("🧪 Testing ConfigReader Enhancement...")
    print("=" * 60)
    
    try:
        from config_reader import ConfigReader
        
        # Create a test config file with all required cells
        config_path = os.path.join(tempfile.gettempdir(), "test_worker_file5_config.xlsx")
        
        print("📝 Creating test configuration file...")
        wb = Workbook()
        ws = wb.active
        
        # Set test data for existing cells
        ws['A2'] = "TestProgram_V2.1.5"
        ws['D2'] = "AdvancedChip_AC2024"
        ws['F2'] = "TechManufacturing_Corp"
        
        # Set test data for new cells (worker_file5 requirements)
        ws['D5'] = "Tester_Station_01"
        ws['F5'] = "ProbeCard_PC2024_Rev3"
        ws['H5'] = "TestFlow_Production_V1.2"
        
        # Add some bin mappings
        ws['A6'] = 0
        ws['B6'] = "PASS"
        ws['A7'] = 1
        ws['B7'] = "FAIL_CONTACT"
        ws['A8'] = 2
        ws['B8'] = "FAIL_LEAKAGE"
        ws['A9'] = 5
        ws['B9'] = "FAIL_SPEED"
        
        wb.save(config_path)
        wb.close()
        
        print(f"✅ Test config file created: {os.path.basename(config_path)}")
        
        # Test reading the configuration
        config_reader = ConfigReader()
        if config_reader.read_config_file(config_path):
            print("✅ Configuration file loaded successfully")
            
            print(f"\n📋 Configuration Data:")
            print(f"🔹 Test Program (A2): '{config_reader.get_test_program_name()}'")
            print(f"🔹 Device Name (D2): '{config_reader.get_device_name()}'")
            print(f"🔹 Vendor Name (F2): '{config_reader.get_vendor_name()}'")
            
            print(f"\n🆕 New Configuration Data (worker_file5):")
            print(f"🔹 Tester Name (D5): '{config_reader.get_tester_name()}'")
            print(f"🔹 Probe Card No. (F5): '{config_reader.get_probe_card_no()}'")
            print(f"🔹 Test Flow (H5): '{config_reader.get_test_flow()}'")
            
            print(f"\n🏷️  Bin Mappings:")
            for bin_num, bin_name in config_reader.get_all_bin_mappings().items():
                print(f"🔹 bin{bin_num}: {bin_name}")
            
            # Verify all new methods work
            assert config_reader.get_tester_name() == "Tester_Station_01"
            assert config_reader.get_probe_card_no() == "ProbeCard_PC2024_Rev3"
            assert config_reader.get_test_flow() == "TestFlow_Production_V1.2"
            
            print(f"\n✅ All new configuration methods working correctly!")
            return True
            
        else:
            print("❌ Failed to load configuration file")
            return False
            
    except Exception as e:
        print(f"❌ Error in ConfigReader test: {e}")
        return False

def test_bin_summary_enhancement():
    """Test the enhanced Bin_Summary sheet with additional headers and data"""
    print("\n🧪 Testing Bin_Summary Enhancement...")
    print("=" * 60)
    
    try:
        from full_map_processor import FullMapProcessor
        from config_reader import ConfigReader
        from openpyxl import Workbook
        
        # Create a mock processor with config
        processor = FullMapProcessor()
        
        # Create and set up config reader
        config_reader = ConfigReader()
        config_path = os.path.join(tempfile.gettempdir(), "test_worker_file5_config.xlsx")
        
        if config_reader.read_config_file(config_path):
            processor.set_config_reader(config_reader)
            print("✅ Configuration loaded into processor")
        else:
            print("⚠️  No configuration loaded, testing without config")
        
        # Create test workbook and worksheet
        workbook = Workbook()
        worksheet = workbook.active
        worksheet.title = "Bin_Summary"
        
        # Create mock processors data (simulating multiple MAP files)
        mock_processors = {
            "test_map_1.map": {
                'processor': type('MockProcessor', (), {
                    'get_test_statistics': lambda: {
                        'total_tested': 1000,
                        'pass_count': 950,
                        'yield_percentage': 95.0
                    },
                    'get_bin_statistics': lambda sort_by_quantity=True: [
                        {'bin_name': 'bin0', 'quantity': 950, 'yield_percentage': 95.0},
                        {'bin_name': 'bin1', 'quantity': 30, 'yield_percentage': 3.0},
                        {'bin_name': 'bin2', 'quantity': 20, 'yield_percentage': 2.0}
                    ]
                })(),
                'file_path': '/test/test_map_1.map'
            },
            "test_map_2.map": {
                'processor': type('MockProcessor', (), {
                    'get_test_statistics': lambda: {
                        'total_tested': 1200,
                        'pass_count': 1140,
                        'yield_percentage': 95.0
                    },
                    'get_bin_statistics': lambda sort_by_quantity=True: [
                        {'bin_name': 'bin0', 'quantity': 1140, 'yield_percentage': 95.0},
                        {'bin_name': 'bin1', 'quantity': 40, 'yield_percentage': 3.33},
                        {'bin_name': 'bin2', 'quantity': 20, 'yield_percentage': 1.67}
                    ]
                })(),
                'file_path': '/test/test_map_2.map'
            }
        }
        
        # Add mock sheets to workbook
        for filename in mock_processors.keys():
            sheet_name = os.path.splitext(filename)[0]
            workbook.create_sheet(title=sheet_name)
        
        print("🔄 Testing Bin_Summary sheet creation...")
        
        # Test the enhanced _create_bin_summary_sheet method
        processor._create_bin_summary_sheet(worksheet, mock_processors, workbook)
        
        print("\n📋 Verifying Enhanced Bin_Summary Sheet:")
        print("=" * 50)
        
        # Check additional headers in row 5 (consecutive columns starting from 132)
        additional_headers = [
            (132, "Tester"),
            (133, "Probe card no."),
            (134, "Test Program"),
            (135, "Test Flow")
        ]
        
        print("🔍 Checking additional headers in row 5:")
        for col_num, expected_header in additional_headers:
            actual_header = worksheet.cell(row=5, column=col_num).value
            print(f"  Column {col_num} (Expected: '{expected_header}'): '{actual_header}'")
            if actual_header == expected_header:
                print(f"  ✅ Header correct")
            else:
                print(f"  ❌ Header mismatch")
        
        # Check data in row 6 (first data row)
        print(f"\n🔍 Checking configuration data in row 6:")
        if processor.config_reader:
            config_data = [
                (132, processor.config_reader.get_tester_name(), "Tester Name"),
                (133, processor.config_reader.get_probe_card_no(), "Probe Card No."),
                (134, processor.config_reader.get_test_program_name(), "Test Program"),
                (135, processor.config_reader.get_test_flow(), "Test Flow")
            ]
            
            for col_num, expected_data, description in config_data:
                actual_data = worksheet.cell(row=6, column=col_num).value
                print(f"  Column {col_num} ({description}): '{actual_data}' (Expected: '{expected_data}')")
                if actual_data == expected_data:
                    print(f"  ✅ Data correct")
                else:
                    print(f"  ❌ Data mismatch")
        else:
            print("  ⚠️  No config reader available, data cells should be empty")
        
        # Save test output for inspection
        output_path = "test_worker_file5_output.xlsx"
        workbook.save(output_path)
        workbook.close()
        
        print(f"\n✅ Test output saved: {output_path}")
        print("✅ Bin_Summary enhancement test completed successfully!")
        
        return True
        
    except Exception as e:
        print(f"❌ Error in Bin_Summary enhancement test: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all tests for worker_file5 enhancement"""
    print("🚀 Worker File 5 Enhancement Test Suite")
    print("=" * 80)
    
    test_results = []
    
    # Test 1: ConfigReader enhancement
    result1 = test_config_reader_enhancement()
    test_results.append(("ConfigReader Enhancement", result1))
    
    # Test 2: Bin_Summary enhancement
    result2 = test_bin_summary_enhancement()
    test_results.append(("Bin_Summary Enhancement", result2))
    
    # Summary
    print("\n" + "=" * 80)
    print("📊 Test Results Summary:")
    print("=" * 80)
    
    all_passed = True
    for test_name, result in test_results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name:<30} {status}")
        if not result:
            all_passed = False
    
    print("=" * 80)
    if all_passed:
        print("🎉 All tests passed! Worker File 5 enhancement is working correctly.")
    else:
        print("⚠️  Some tests failed. Please check the implementation.")
    
    return all_passed

if __name__ == "__main__":
    main()
