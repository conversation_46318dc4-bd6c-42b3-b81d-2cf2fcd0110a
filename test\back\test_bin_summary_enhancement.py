#!/usr/bin/env python3
"""
Test script for the enhanced Bin_Summary sheet functionality
Tests the new header information and summary statistics
"""

import os
import sys
import tempfile
import shutil

# Add parent directory to path to import modules
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_config_reader_enhancement():
    """Test the enhanced ConfigReader with D2 and F2 cell reading"""
    print("🧪 Testing Enhanced ConfigReader")
    print("=" * 40)
    
    try:
        from config_reader import ConfigReader
        from openpyxl import Workbook
        
        # Create a test Excel file with the required data
        test_config_path = os.path.join(tempfile.gettempdir(), "test_config.xlsx")
        
        # Create test workbook
        wb = Workbook()
        ws = wb.active
        
        # Set test data
        ws['A2'] = "Test_Program_V1.0"  # Test program name
        ws['D2'] = "TestDevice_ABC123"  # Device name
        ws['F2'] = "TestVendor_XYZ"     # Vendor name
        
        # Add some bin mappings
        ws['A6'] = 0
        ws['B6'] = "Pass"
        ws['A7'] = 1
        ws['B7'] = "Fail_Test1"
        ws['A8'] = 2
        ws['B8'] = "Fail_Test2"
        
        wb.save(test_config_path)
        wb.close()
        
        # Test ConfigReader
        config_reader = ConfigReader()
        
        print("1️⃣ Testing config file reading...")
        if config_reader.read_config_file(test_config_path):
            print("   ✅ Config file loaded successfully")
        else:
            print("   ❌ Failed to load config file")
            return False
        
        # Test new methods
        print("2️⃣ Testing new field extraction...")
        
        device_name = config_reader.get_device_name()
        vendor_name = config_reader.get_vendor_name()
        test_program = config_reader.get_test_program_name()
        
        print(f"   Device Name (D2): '{device_name}'")
        print(f"   Vendor Name (F2): '{vendor_name}'")
        print(f"   Test Program (A2): '{test_program}'")
        
        # Verify values
        if device_name == "TestDevice_ABC123":
            print("   ✅ Device name correctly extracted")
        else:
            print(f"   ❌ Device name mismatch: expected 'TestDevice_ABC123', got '{device_name}'")
            return False
        
        if vendor_name == "TestVendor_XYZ":
            print("   ✅ Vendor name correctly extracted")
        else:
            print(f"   ❌ Vendor name mismatch: expected 'TestVendor_XYZ', got '{vendor_name}'")
            return False
        
        # Test bin mappings still work
        print("3️⃣ Testing bin mappings...")
        bin_mappings = config_reader.bin_name_mapping
        if len(bin_mappings) >= 3:
            print(f"   ✅ Bin mappings loaded: {len(bin_mappings)} bins")
            print(f"   bin0: {config_reader.get_bin_name(0)}")
            print(f"   bin1: {config_reader.get_bin_name(1)}")
        else:
            print("   ❌ Bin mappings not loaded correctly")
            return False
        
        # Cleanup
        os.remove(test_config_path)
        return True
        
    except Exception as e:
        print(f"❌ Error during ConfigReader testing: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_summary_header_creation():
    """Test the summary header creation functionality"""
    print("\n🧪 Testing Summary Header Creation")
    print("=" * 40)
    
    try:
        from full_map_processor import FullMapProcessor
        from config_reader import ConfigReader
        from openpyxl import Workbook
        
        # Create test processor
        processor = FullMapProcessor()
        
        # Create mock config reader
        config_reader = ConfigReader()
        config_reader.device_name = "MockDevice_123"
        config_reader.vendor_name = "MockVendor_ABC"
        processor.set_config_reader(config_reader)
        
        # Create test workbook and worksheet
        workbook = Workbook()
        worksheet = workbook.active
        
        # Create mock processors data
        mock_processors = {
            "TestFile1-W01.map": {
                "processor": MockTSKProcessor(1000, 950),  # 1000 tested, 950 pass
                "file_path": "TestFile1-W01.map"
            },
            "TestFile1-W02.map": {
                "processor": MockTSKProcessor(1200, 1100),  # 1200 tested, 1100 pass
                "file_path": "TestFile1-W02.map"
            }
        }
        
        print("1️⃣ Testing header creation...")
        processor._create_summary_header(worksheet, mock_processors)
        
        # Verify header content
        print("2️⃣ Verifying header content...")
        
        # Check row 1
        device_name_header = worksheet['A1'].value
        device_name_value = worksheet['C1'].value
        lot_no_header = worksheet['E1'].value
        lot_no_value = worksheet['F1'].value
        total_pcs_header = worksheet['H1'].value
        total_pcs_value = worksheet['I1'].value
        vendor_header = worksheet['J1'].value
        vendor_value = worksheet['K1'].value
        
        print(f"   Row 1: Device='{device_name_value}', Lot='{lot_no_value}', Maps={total_pcs_value}, Vendor='{vendor_value}'")
        
        # Check row 2
        total_tested_header = worksheet['A2'].value
        total_tested_value = worksheet['C2'].value
        pass_dice_header = worksheet['E2'].value
        pass_dice_value = worksheet['F2'].value
        yield_header = worksheet['H2'].value
        yield_value = worksheet['I2'].value
        fail_dice_header = worksheet['J2'].value
        fail_dice_value = worksheet['K2'].value
        
        print(f"   Row 2: Tested={total_tested_value}, Pass={pass_dice_value}, Yield='{yield_value}', Fail={fail_dice_value}")
        
        # Verify calculations
        expected_total = 1000 + 1200  # 2200
        expected_pass = 950 + 1100    # 2050
        expected_fail = expected_total - expected_pass  # 150
        expected_yield = (expected_pass / expected_total * 100)  # ~93.18%
        
        if total_tested_value == expected_total:
            print("   ✅ Total tested calculation correct")
        else:
            print(f"   ❌ Total tested mismatch: expected {expected_total}, got {total_tested_value}")
            return False
        
        if pass_dice_value == expected_pass:
            print("   ✅ Pass dice calculation correct")
        else:
            print(f"   ❌ Pass dice mismatch: expected {expected_pass}, got {pass_dice_value}")
            return False
        
        if fail_dice_value == expected_fail:
            print("   ✅ Fail dice calculation correct")
        else:
            print(f"   ❌ Fail dice mismatch: expected {expected_fail}, got {fail_dice_value}")
            return False
        
        # Check yield format (should be like "93.18%")
        if yield_value and "%" in str(yield_value):
            yield_num = float(str(yield_value).replace("%", ""))
            if abs(yield_num - expected_yield) < 0.01:  # Allow small floating point differences
                print("   ✅ Yield calculation and format correct")
            else:
                print(f"   ❌ Yield calculation mismatch: expected {expected_yield:.2f}%, got {yield_num}%")
                return False
        else:
            print(f"   ❌ Yield format incorrect: expected percentage, got '{yield_value}'")
            return False
        
        # Check lot number extraction
        if lot_no_value == "TestFile1":
            print("   ✅ Lot number extraction correct")
        else:
            print(f"   ❌ Lot number mismatch: expected 'TestFile1', got '{lot_no_value}'")
            return False
        
        workbook.close()
        return True
        
    except Exception as e:
        print(f"❌ Error during summary header testing: {e}")
        import traceback
        traceback.print_exc()
        return False


class MockTSKProcessor:
    """Mock TSKMapProcessor for testing"""
    def __init__(self, total_tested, pass_count):
        self.total_tested = total_tested
        self.pass_count = pass_count
    
    def get_test_statistics(self):
        return {
            'total_tested': self.total_tested,
            'pass_count': self.pass_count,
            'yield_percentage': (self.pass_count / self.total_tested * 100) if self.total_tested > 0 else 0.0
        }


def test_integration():
    """Test integration with existing functionality"""
    print("\n🧪 Testing Integration")
    print("=" * 40)
    
    try:
        from full_map_processor import FullMapProcessor
        
        # Test that the processor still has all required methods
        processor = FullMapProcessor()
        
        print("1️⃣ Testing method availability...")
        
        required_methods = [
            '_create_summary_header',
            '_create_bin_summary_sheet',
            'set_sort_by_quantity',
            'set_config_reader'
        ]
        
        for method_name in required_methods:
            if hasattr(processor, method_name):
                print(f"   ✅ Method {method_name} available")
            else:
                print(f"   ❌ Method {method_name} missing")
                return False
        
        print("2️⃣ Testing default values...")
        if hasattr(processor, 'sort_by_quantity') and processor.sort_by_quantity:
            print("   ✅ Default sort_by_quantity is True")
        else:
            print("   ❌ Default sort_by_quantity should be True")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error during integration testing: {e}")
        return False


def main():
    """Run all tests for the Bin_Summary enhancement"""
    print("🎯 Bin_Summary Enhancement Test Suite")
    print("=" * 60)
    
    # Run tests
    test1 = test_config_reader_enhancement()
    test2 = test_summary_header_creation()
    test3 = test_integration()
    
    # Summary
    print("\n" + "=" * 60)
    print("📋 TEST RESULTS")
    print("=" * 60)
    print(f"   ConfigReader Enhancement: {'PASS' if test1 else 'FAIL'}")
    print(f"   Summary Header Creation: {'PASS' if test2 else 'FAIL'}")
    print(f"   Integration: {'PASS' if test3 else 'FAIL'}")
    
    all_passed = test1 and test2 and test3
    print(f"\nOVERALL: {'ALL TESTS PASSED' if all_passed else 'SOME TESTS FAILED'}")
    
    if all_passed:
        print("\n🎉 SUCCESS! Bin_Summary enhancement is working correctly!")
        print("\n📋 New Features:")
        print("   ✅ Enhanced header with Device Name, Lot No, Total pcs, Vendor")
        print("   ✅ Summary statistics: Total Tested, Pass Dice, Yield%, Fail Dice")
        print("   ✅ Automatic calculation from all loaded MAP files")
        print("   ✅ Integration with config file (D2, F2 cells)")
        print("   ✅ Maintains existing bin statistics functionality")
    else:
        print("\n❌ Some tests failed. Please check the implementation.")
    
    return all_passed


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
