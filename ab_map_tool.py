#!/usr/bin/env python3
"""
AB MAP Tool - Enhanced GUI Application
Supports three processing modes:
1. Amap - Single A file processing
2. Bmap - Single B file processing  
3. AB Compare - Dual file comparison with both A and B files

Features:
- Dynamic file selection based on mode
- Timestamp-based comparison file naming
- Unified Excel output with multiple sheets for comparison
- Consistent program architecture with existing tools
"""

import sys
import os

# Add current directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from tsk_map_gui import ABMapGUI
import tkinter as tk
from tkinter import ttk


def main():
    """Main entry point for AB MAP Tool"""
    try:
        # Create main window
        root = tk.Tk()
        
        # Configure modern theme
        style = ttk.Style()
        style.theme_use('clam')
        
        # Initialize AB Map GUI
        app = ABMapGUI(root)
        
        # Start the application
        root.mainloop()
        
    except Exception as e:
        print(f"Error starting AB MAP Tool: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
