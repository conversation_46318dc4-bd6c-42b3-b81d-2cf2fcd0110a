#!/usr/bin/env python3
"""
测试Bump Map Enhanced Tool的完整功能
验证worker_file15.txt中要求的所有增强功能

Author: Yuribytes
Company: Chipone TE development Team
"""

import os
import sys
import tkinter as tk

# 添加父目录到路径
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, parent_dir)

def test_enhanced_tool_initialization():
    """测试增强工具初始化"""
    print("🧪 测试增强工具初始化")
    print("=" * 50)
    
    try:
        from bump_map_enhanced_frame import BumpMapEnhancedFrame
        
        # 创建测试环境
        root = tk.Tk()
        root.withdraw()
        
        class MockController:
            def __init__(self):
                self.root = root
        
        controller = MockController()
        enhanced_tool = BumpMapEnhancedFrame(root, controller)
        
        # 检查核心属性
        core_attrs = [
            'bump_map_files', 'bump_map_display', 'dummy_map_file_path',
            'output_directory_path', 'selected_test_house', 'selected_test_house_display',
            'detected_map_version', 'map_version_info', 'pass_value', 'fail_value',
            'status_var', 'test_houses'
        ]
        
        for attr in core_attrs:
            if hasattr(enhanced_tool, attr):
                print(f"✅ {attr}属性存在")
            else:
                print(f"❌ {attr}属性不存在")
                return False
        
        # 检查初始状态
        if len(enhanced_tool.bump_map_files) == 0:
            print("✅ 初始状态正确 - 无Bump Map文件")
        else:
            print("❌ 初始状态异常")
            return False
        
        if enhanced_tool.bump_map_display.get() == "No bump map files selected":
            print("✅ 初始显示正确")
        else:
            print("❌ 初始显示异常")
            return False
        
        # 清理
        root.destroy()
        
        return True
        
    except Exception as e:
        print(f"❌ 增强工具初始化测试失败: {e}")
        return False

def test_multiple_bump_map_functionality():
    """测试多个Bump Map功能"""
    print(f"\n🧪 测试多个Bump Map功能")
    print("=" * 50)
    
    try:
        from bump_map_enhanced_frame import BumpMapEnhancedFrame
        
        # 创建测试环境
        root = tk.Tk()
        root.withdraw()
        
        class MockController:
            def __init__(self):
                self.root = root
        
        controller = MockController()
        enhanced_tool = BumpMapEnhancedFrame(root, controller)
        
        # 检查多选相关方法
        multi_methods = [
            'browse_bump_map_files', 'clear_bump_map_files', 
            'update_bump_map_display'
        ]
        for method in multi_methods:
            if hasattr(enhanced_tool, method):
                print(f"✅ {method}方法存在")
            else:
                print(f"❌ {method}方法不存在")
                return False
        
        # 测试添加文件功能
        test_files = ["test1.map", "test2.map", "test3.map"]
        enhanced_tool.bump_map_files.extend(test_files)
        enhanced_tool.update_bump_map_display()
        
        # 验证显示更新
        display_text = enhanced_tool.bump_map_display.get()
        if "3 files" in display_text:
            print("✅ 多文件显示功能正常")
        else:
            print(f"❌ 多文件显示异常: {display_text}")
            return False
        
        # 测试Clear功能
        enhanced_tool.clear_bump_map_files()
        
        if len(enhanced_tool.bump_map_files) == 0:
            print("✅ Clear功能正常")
        else:
            print("❌ Clear功能异常")
            return False
        
        # 清理
        root.destroy()
        
        return True
        
    except Exception as e:
        print(f"❌ 多个Bump Map功能测试失败: {e}")
        return False

def test_test_house_clear_functionality():
    """测试Test House Clear功能"""
    print(f"\n🧪 测试Test House Clear功能")
    print("=" * 50)
    
    try:
        from bump_map_enhanced_frame import BumpMapEnhancedFrame
        
        # 创建测试环境
        root = tk.Tk()
        root.withdraw()
        
        class MockController:
            def __init__(self):
                self.root = root
        
        controller = MockController()
        enhanced_tool = BumpMapEnhancedFrame(root, controller)
        
        # 检查Clear功能方法
        if hasattr(enhanced_tool, 'clear_test_house_selection'):
            print("✅ clear_test_house_selection方法存在")
        else:
            print("❌ clear_test_house_selection方法不存在")
            return False
        
        # 测试选择功能
        enhanced_tool.selected_test_house.set("NEPES")
        enhanced_tool.on_test_house_selection_changed()
        
        # 验证选择成功
        if "NEPES Corporation" in enhanced_tool.selected_test_house_display.get():
            print("✅ 测试厂选择功能正常")
        else:
            print("❌ 测试厂选择功能异常")
            return False
        
        # 测试Clear功能
        enhanced_tool.clear_test_house_selection()
        
        # 验证Clear成功
        if enhanced_tool.selected_test_house.get() == "":
            print("✅ Clear功能正常 - 选择已清除")
        else:
            print("❌ Clear功能异常 - 选择未清除")
            return False
        
        # 清理
        root.destroy()
        
        return True
        
    except Exception as e:
        print(f"❌ Test House Clear功能测试失败: {e}")
        return False

def test_output_directory_functionality():
    """测试输出目录功能"""
    print(f"\n🧪 测试输出目录功能")
    print("=" * 50)
    
    try:
        from bump_map_enhanced_frame import BumpMapEnhancedFrame
        
        # 创建测试环境
        root = tk.Tk()
        root.withdraw()
        
        class MockController:
            def __init__(self):
                self.root = root
        
        controller = MockController()
        enhanced_tool = BumpMapEnhancedFrame(root, controller)
        
        # 检查输出目录相关方法
        output_methods = [
            'browse_output_directory', 'auto_generate_output_directory',
            'generate_output_filename'
        ]
        for method in output_methods:
            if hasattr(enhanced_tool, method):
                print(f"✅ {method}方法存在")
            else:
                print(f"❌ {method}方法不存在")
                return False
        
        # 测试文件名生成功能
        test_bump = "/path/to/bump_test.map"
        test_dummy = "/path/to/dummy_test.tsk"
        
        filename = enhanced_tool.generate_output_filename(test_bump, test_dummy)
        
        if filename and "bump_test" in filename and "dummy_test" in filename:
            print(f"✅ 文件名生成功能正常: {filename}")
        else:
            print(f"❌ 文件名生成异常: {filename}")
            return False
        
        # 清理
        root.destroy()
        
        return True
        
    except Exception as e:
        print(f"❌ 输出目录功能测试失败: {e}")
        return False

def test_batch_processing_methods():
    """测试批处理方法"""
    print(f"\n🧪 测试批处理方法")
    print("=" * 50)
    
    try:
        from bump_map_enhanced_frame import BumpMapEnhancedFrame
        
        # 创建测试环境
        root = tk.Tk()
        root.withdraw()
        
        class MockController:
            def __init__(self):
                self.root = root
        
        controller = MockController()
        enhanced_tool = BumpMapEnhancedFrame(root, controller)
        
        # 检查批处理相关方法
        batch_methods = [
            'process_nepes_batch', 'show_batch_results',
            'create_batch_success_message', 'create_partial_success_message'
        ]
        for method in batch_methods:
            if hasattr(enhanced_tool, method):
                print(f"✅ {method}方法存在")
            else:
                print(f"❌ {method}方法不存在")
                return False
        
        # 测试消息生成
        processed_files = [
            ("bump1.map", "output1.map"),
            ("bump2.map", "output2.map")
        ]
        
        success_msg = enhanced_tool.create_batch_success_message(processed_files)
        if "Batch Processing Completed Successfully" in success_msg:
            print("✅ 成功消息生成正常")
        else:
            print("❌ 成功消息生成异常")
            return False
        
        failed_files = ["bump3.map"]
        partial_msg = enhanced_tool.create_partial_success_message(processed_files, failed_files)
        if "Partial Processing Results" in partial_msg:
            print("✅ 部分成功消息生成正常")
        else:
            print("❌ 部分成功消息生成异常")
            return False
        
        # 清理
        root.destroy()
        
        return True
        
    except Exception as e:
        print(f"❌ 批处理方法测试失败: {e}")
        return False

def test_ui_components():
    """测试UI组件"""
    print(f"\n🧪 测试UI组件")
    print("=" * 50)
    
    try:
        from bump_map_enhanced_frame import BumpMapEnhancedFrame
        
        # 创建测试环境
        root = tk.Tk()
        root.withdraw()
        
        class MockController:
            def __init__(self):
                self.root = root
        
        controller = MockController()
        enhanced_tool = BumpMapEnhancedFrame(root, controller)
        
        # 检查UI创建方法
        ui_methods = [
            'create_file_selection_section', 'create_test_house_section',
            'create_map_version_section', 'create_configuration_section',
            'create_status_bar', 'create_control_buttons'
        ]
        for method in ui_methods:
            if hasattr(enhanced_tool, method):
                print(f"✅ {method}方法存在")
            else:
                print(f"❌ {method}方法不存在")
                return False
        
        # 检查格式预览功能
        if hasattr(enhanced_tool, 'update_format_preview'):
            print("✅ update_format_preview方法存在")
        else:
            print("❌ update_format_preview方法不存在")
            return False
        
        # 测试格式预览
        enhanced_tool.pass_value.set(255)
        enhanced_tool.fail_value.set(0)
        enhanced_tool.update_format_preview()
        
        print("✅ UI组件功能正常")
        
        # 清理
        root.destroy()
        
        return True
        
    except Exception as e:
        print(f"❌ UI组件测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🧪 Bump Map Enhanced Tool 完整功能测试")
    print("=" * 70)
    
    test_results = []
    
    try:
        # 测试1: 增强工具初始化
        result1 = test_enhanced_tool_initialization()
        test_results.append(("增强工具初始化", result1))
        
        # 测试2: 多个Bump Map功能
        result2 = test_multiple_bump_map_functionality()
        test_results.append(("多个Bump Map功能", result2))
        
        # 测试3: Test House Clear功能
        result3 = test_test_house_clear_functionality()
        test_results.append(("Test House Clear功能", result3))
        
        # 测试4: 输出目录功能
        result4 = test_output_directory_functionality()
        test_results.append(("输出目录功能", result4))
        
        # 测试5: 批处理方法
        result5 = test_batch_processing_methods()
        test_results.append(("批处理方法", result5))
        
        # 测试6: UI组件
        result6 = test_ui_components()
        test_results.append(("UI组件", result6))
        
        # 总结
        print("\n" + "=" * 70)
        print("🎉 增强工具完整功能测试结果总结:")
        
        passed = 0
        for test_name, result in test_results:
            status = "✅ 通过" if result else "❌ 失败"
            print(f"   {test_name}: {status}")
            if result:
                passed += 1
        
        print(f"\n📊 总体结果: {passed}/{len(test_results)} 测试通过")
        
        if passed == len(test_results):
            print("\n🎯 🎉 所有增强功能测试成功！")
            print("✅ worker_file15.txt要求的功能已完整实现:")
            print("   1. ✅ Test House选择增加Clear功能")
            print("   2. ✅ Bump Map多选功能")
            print("   3. ✅ 输出路径自动化")
            print("   4. ✅ UI优化和美观设计")
            print("   5. ✅ 批处理消息系统")
            print("   6. ✅ 内存管理和状态清理")
            print("\n🚀 Bump Map Enhanced Tool 完全实现！")
        else:
            print("⚠️  部分增强功能测试失败，需要进一步检查")
        
        return passed == len(test_results)
        
    except Exception as e:
        print(f"❌ 增强功能测试过程出错: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
