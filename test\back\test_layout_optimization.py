#!/usr/bin/env python3
"""
Test script for Bump Map Tool layout optimization
Tests the improved layout that ensures both columns of test house selection are visible
Author: Yuribytes
Company: Chipone TE development Team
"""

import sys
import os
import tkinter as tk
from tkinter import ttk

# Add parent directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    from bump_map_tool_frame import BumpMapToolFrame
except ImportError as e:
    print(f"Import error: {e}")
    print("Please ensure all required modules are available.")
    sys.exit(1)


class LayoutOptimizationTester:
    """Test class for layout optimization"""
    
    def __init__(self):
        self.root = None
        self.tool_frame = None
    
    def setup_gui(self):
        """Set up GUI for testing"""
        self.root = tk.Tk()
        self.root.title("Bump Map Tool - Layout Optimization Test")
        self.root.geometry("1000x800")
        
        # Mock app controller
        class MockController:
            def __init__(self, root):
                self.root = root
            def return_to_selector_with_confirmation(self, tool_name, has_unsaved):
                print(f"Mock: Return to selector from {tool_name}, unsaved: {has_unsaved}")
                return True
        
        controller = MockController(self.root)
        self.tool_frame = BumpMapToolFrame(self.root, controller)
        self.tool_frame.show()
    
    def test_layout_optimization(self):
        """Test the layout optimization"""
        print("🧪 Testing Layout Optimization")
        print("=" * 50)
        
        try:
            self.setup_gui()
            
            # Update GUI to ensure all widgets are rendered
            self.root.update_idletasks()
            
            # Test 1: Check if test houses are properly distributed
            print("📋 Test House Distribution:")
            test_houses = list(self.tool_frame.test_houses.items())
            total_houses = len(test_houses)
            print(f"Total test houses: {total_houses}")
            
            # Calculate expected rows for 2-column layout
            expected_rows = (total_houses + 1) // 2
            print(f"Expected rows for 2-column layout: {expected_rows}")
            
            # Test 2: Verify priority test houses
            priority_houses = list(test_houses)[:6]
            print(f"\n🎯 Priority Test Houses (First 6):")
            for i, (house_id, house_name) in enumerate(priority_houses, 1):
                print(f"  {i}. {house_id}: {house_name}")
            
            # Test 3: Test selection functionality
            print(f"\n🔄 Testing Selection Functionality:")
            
            # Test selecting first priority test house
            first_house_id = priority_houses[0][0]
            first_house_name = priority_houses[0][1]
            
            print(f"Selecting: {first_house_id}")
            self.tool_frame.selected_test_house.set(first_house_id)
            
            # Allow GUI to update
            self.root.update_idletasks()
            
            # Check display update
            display_text = self.tool_frame.selected_test_house_display.get()
            print(f"Display updated to: '{display_text}'")
            
            # Verify display contains the house name
            if first_house_name in display_text and "✓" in display_text:
                print("✅ Selection display working correctly")
            else:
                print("❌ Selection display not working properly")
            
            # Test 4: Test different selections
            print(f"\n🔄 Testing Multiple Selections:")
            test_selections = priority_houses[:3]  # Test first 3
            
            for house_id, house_name in test_selections:
                print(f"Testing selection: {house_id}")
                self.tool_frame.selected_test_house.set(house_id)
                self.root.update_idletasks()
                
                display_text = self.tool_frame.selected_test_house_display.get()
                if house_name in display_text:
                    print(f"  ✅ {house_id} selection working")
                else:
                    print(f"  ❌ {house_id} selection failed")
            
            print(f"\n📐 Layout Information:")
            print(f"Main window size: {self.root.winfo_width()}x{self.root.winfo_height()}")
            
            # Show layout for manual inspection
            print(f"\n📋 Layout Optimization Complete!")
            print("=" * 50)
            print("🔍 Manual Inspection Points:")
            print("1. Both columns of test house selection should be visible")
            print("2. Right side display should be compact but readable")
            print("3. No overlap between selection area and display area")
            print("4. Scrolling should work smoothly")
            print("5. Selection feedback should be immediate")
            print("\n👀 Please inspect the GUI layout manually...")
            print("Close the window when inspection is complete.")
            
            # Keep GUI open for manual inspection
            self.root.mainloop()
            
        except Exception as e:
            print(f"❌ Layout optimization test failed: {e}")
            import traceback
            traceback.print_exc()
        finally:
            if self.root:
                try:
                    self.root.destroy()
                except:
                    pass
    
    def run_automated_tests(self):
        """Run automated layout tests without GUI display"""
        print("🤖 Running Automated Layout Tests")
        print("=" * 40)
        
        try:
            # Create GUI without showing
            self.root = tk.Tk()
            self.root.withdraw()  # Hide window
            
            class MockController:
                def __init__(self, root):
                    self.root = root
                def return_to_selector_with_confirmation(self, tool_name, has_unsaved):
                    return True
            
            controller = MockController(self.root)
            self.tool_frame = BumpMapToolFrame(self.root, controller)
            
            # Test 1: Widget existence
            print("🧪 Testing widget existence...")
            assert hasattr(self.tool_frame, 'selected_test_house_display')
            assert hasattr(self.tool_frame, 'test_houses')
            print("✅ All required widgets exist")
            
            # Test 2: Test house count
            print("🧪 Testing test house count...")
            house_count = len(self.tool_frame.test_houses)
            assert house_count == 20, f"Expected 20 test houses, got {house_count}"
            print(f"✅ Correct number of test houses: {house_count}")
            
            # Test 3: Priority order
            print("🧪 Testing priority order...")
            house_list = list(self.tool_frame.test_houses.keys())
            priority_expected = ["ChipMOS", "Unisem", "TongFu", "NEPES", "Chipbond", "Powertech"]
            priority_actual = house_list[:6]
            
            for expected, actual in zip(priority_expected, priority_actual):
                assert expected == actual, f"Priority mismatch: expected {expected}, got {actual}"
            print("✅ Priority order is correct")
            
            # Test 4: Selection functionality
            print("🧪 Testing selection functionality...")
            test_id = "ChipMOS"
            self.tool_frame.selected_test_house.set(test_id)
            
            # Trigger the callback manually
            self.tool_frame.on_test_house_selection_changed()
            
            display_text = self.tool_frame.selected_test_house_display.get()
            assert "ChipMOS Technologies" in display_text
            assert "✓" in display_text
            print("✅ Selection functionality working")
            
            print("\n🎉 All automated tests passed!")
            
            self.root.destroy()
            return True
            
        except Exception as e:
            print(f"❌ Automated test failed: {e}")
            if self.root:
                self.root.destroy()
            return False


def main():
    """Main test function"""
    print("🚀 Bump Map Tool - Layout Optimization Test Suite")
    print("=" * 60)
    
    tester = LayoutOptimizationTester()
    
    # Run automated tests first
    automated_success = tester.run_automated_tests()
    
    if automated_success:
        print("\n" + "=" * 60)
        print("🖥️  Starting Manual Layout Inspection...")
        print("=" * 60)
        
        # Run manual inspection
        tester.test_layout_optimization()
    else:
        print("\n❌ Automated tests failed. Skipping manual inspection.")


if __name__ == "__main__":
    main()
