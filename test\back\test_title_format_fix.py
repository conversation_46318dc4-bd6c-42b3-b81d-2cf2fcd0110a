#!/usr/bin/env python3
"""
Test script for title format fix
Tests that only the first line (title) has spacing between three-digit numbers,
while data rows maintain consistent 3-character width formatting
"""

import os
import sys

# Add parent directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from tmb_processor import TMBProcessor
from config_reader import Config<PERSON><PERSON><PERSON>


def test_title_format_only():
    """Test that only title line has improved formatting"""
    print("=" * 60)
    print("Testing title format fix (only first line modified)...")
    
    # Setup
    tmb_processor = TMBProcessor()
    config_reader = ConfigReader()
    
    # Load config
    config_file = "test/CP1_program_bin.xlsx"
    if not config_reader.read_config_file(config_file):
        print("❌ Failed to load config file")
        return False
    
    tmb_processor.set_config_reader(config_reader)
    
    # Test with sample MAP file
    map_file = "test/019.3AD416-19-F4"
    if not os.path.exists(map_file):
        print(f"❌ MAP file not found: {map_file}")
        return False
    
    # Process MAP file to TMB
    tmb_output = tmb_processor.process_map_to_tmb(map_file)
    
    if tmb_output and os.path.exists(tmb_output):
        filename_only = os.path.basename(tmb_output)
        print(f"✅ TMB file generated: {filename_only}")
        
        # Read and analyze format
        with open(tmb_output, 'r', encoding='utf-8') as f:
            content = f.read()
        
        lines = content.split('\n')
        
        # Find the header line (first line)
        header_line = lines[0] if lines else ""
        separator_line = lines[1] if len(lines) > 1 else ""
        
        print(f"\nHeader line analysis:")
        print(f"Length: {len(header_line)}")
        print(f"Content: '{header_line[:120]}...'")
        
        # Check header line format - should have spacing for three-digit numbers
        header_success = True
        if " 100 101 102" in header_line:
            print("✅ Header line: Three-digit numbers have proper spacing")
        elif "100101102" in header_line:
            print("❌ Header line: Three-digit numbers are still stacked")
            header_success = False
        else:
            print("⚠️  Header line: Could not find three-digit pattern (may be OK)")
        
        # Check separator line format - should be consistent
        print(f"\nSeparator line analysis:")
        print(f"Length: {len(separator_line)}")
        print(f"Content: '{separator_line[:120]}...'")
        
        separator_success = True
        # Count separator patterns
        three_char_count = separator_line.count("--+")
        four_char_count = separator_line.count("---+")
        
        print(f"Three-char separators (--+): {three_char_count}")
        print(f"Four-char separators (---+): {four_char_count}")
        
        if four_char_count == 0:
            print("✅ Separator line: All separators use consistent 3-character format")
        else:
            print("❌ Separator line: Mixed separator formats detected")
            separator_success = False
        
        # Check data rows format - should be consistent 3-character width
        print(f"\nData rows analysis:")
        data_rows = [line for line in lines[2:10] if line.strip() and '|' in line]  # First few data rows
        
        data_success = True
        for i, row in enumerate(data_rows[:3]):  # Check first 3 data rows
            row_data = row.split('|')[1] if '|' in row else ""  # Get data part after |
            
            print(f"Row {i}: Length={len(row_data)}, Content='{row_data[:80]}...'")
            
            # Check for consistent spacing in data rows
            # Data rows should NOT have extra spacing for three-digit numbers
            # They should maintain the original 3-character format
            
            # Look for patterns that indicate proper 3-character formatting
            # Three-digit numbers should appear as "XXX" not " XXX"
            if "119" in row_data and " 119 " not in row_data:
                print(f"  ✅ Row {i}: Three-digit numbers use 3-character format")
            elif " 119 " in row_data:
                print(f"  ❌ Row {i}: Three-digit numbers have extra spacing")
                data_success = False
            else:
                print(f"  ⚠️  Row {i}: No three-digit numbers found for testing")
        
        # Overall result
        overall_success = header_success and separator_success and data_success
        
        if overall_success:
            print("\n✅ Title format fix successful:")
            print("   - Header line: Three-digit numbers have proper spacing")
            print("   - Separator line: Consistent 3-character format")
            print("   - Data rows: Maintain consistent 3-character format")
        else:
            print("\n❌ Title format fix has issues:")
            if not header_success:
                print("   - Header line formatting issue")
            if not separator_success:
                print("   - Separator line formatting issue")
            if not data_success:
                print("   - Data rows formatting issue")
        
        print(f"\nTMB file saved to: {tmb_output}")
        return overall_success
    else:
        print("❌ Failed to generate TMB file")
        return False


def main():
    """Main test function"""
    print("Testing title format fix (only first line modification)")
    print("=" * 60)
    
    # Change to script directory
    script_dir = os.path.dirname(os.path.abspath(__file__))
    parent_dir = os.path.dirname(script_dir)
    os.chdir(parent_dir)
    
    print(f"Working directory: {os.getcwd()}")
    
    # Run test
    try:
        result = test_title_format_only()
        if result:
            print("\n✅ Title format fix test completed successfully")
            print("Only the first line (title) has improved spacing for three-digit numbers")
            print("Data rows maintain consistent 3-character formatting")
        else:
            print("\n❌ Title format fix test failed")
    except Exception as e:
        print(f"\n❌ Test error: {e}")


if __name__ == "__main__":
    main()
