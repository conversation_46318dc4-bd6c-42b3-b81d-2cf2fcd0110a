#!/usr/bin/env python3
"""
Demo script for Worker File 5 Enhancement
Demonstrates the new Bin_Summary sheet enhancement features
"""

import os
import sys
import tempfile
from openpyxl import Workbook

# Add parent directory to path to import modules
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def create_demo_config_file():
    """Create a demo configuration file with all required data"""
    config_path = os.path.join(tempfile.gettempdir(), "demo_worker_file5_config.xlsx")
    
    print("📝 Creating demo configuration file...")
    wb = Workbook()
    ws = wb.active
    
    # Set demo data for all required cells
    ws['A2'] = "ProductionTest_V3.2.1"      # Test Program
    ws['D2'] = "SuperChip_SC2024_Pro"       # Device Name
    ws['F2'] = "EliteManufacturing_Ltd"     # Vendor Name
    
    # New cells for worker_file5
    ws['D5'] = "ATE_Station_Alpha_01"       # Tester Name
    ws['F5'] = "ProbeCard_Elite_PC2024_R5"  # Probe Card Number
    ws['H5'] = "ProductionFlow_Standard_V2" # Test Flow
    
    # Add comprehensive bin mappings
    bin_mappings = [
        (0, "PASS"),
        (1, "FAIL_CONTACT_OPEN"),
        (2, "FAIL_CONTACT_SHORT"),
        (3, "FAIL_LEAKAGE_HIGH"),
        (4, "FAIL_LEAKAGE_LOW"),
        (5, "FAIL_SPEED_SLOW"),
        (6, "FAIL_SPEED_FAST"),
        (7, "FAIL_VOLTAGE_HIGH"),
        (8, "FAIL_VOLTAGE_LOW"),
        (9, "FAIL_CURRENT_HIGH"),
        (10, "FAIL_CURRENT_LOW")
    ]
    
    for i, (bin_num, bin_name) in enumerate(bin_mappings, 6):
        ws[f'A{i}'] = bin_num
        ws[f'B{i}'] = bin_name
    
    wb.save(config_path)
    wb.close()
    
    print(f"✅ Demo config file created: {os.path.basename(config_path)}")
    return config_path

def demo_config_reader():
    """Demonstrate the enhanced ConfigReader functionality"""
    print("\n🔍 Demonstrating Enhanced ConfigReader...")
    print("=" * 60)
    
    try:
        from config_reader import ConfigReader
        
        # Create demo config file
        config_path = create_demo_config_file()
        
        # Load configuration
        config_reader = ConfigReader()
        if config_reader.read_config_file(config_path):
            print("✅ Configuration loaded successfully")
            
            print(f"\n📊 Original Configuration Data:")
            print(f"🔹 Test Program (A2): '{config_reader.get_test_program_name()}'")
            print(f"🔹 Device Name (D2): '{config_reader.get_device_name()}'")
            print(f"🔹 Vendor Name (F2): '{config_reader.get_vendor_name()}'")
            
            print(f"\n🆕 New Configuration Data (Worker File 5):")
            print(f"🔹 Tester Name (D5): '{config_reader.get_tester_name()}'")
            print(f"🔹 Probe Card No. (F5): '{config_reader.get_probe_card_no()}'")
            print(f"🔹 Test Flow (H5): '{config_reader.get_test_flow()}'")
            
            print(f"\n🏷️  Bin Mappings ({len(config_reader.get_all_bin_mappings())} total):")
            for bin_num, bin_name in list(config_reader.get_all_bin_mappings().items())[:5]:
                print(f"🔹 bin{bin_num}: {bin_name}")
            if len(config_reader.get_all_bin_mappings()) > 5:
                print(f"🔹 ... and {len(config_reader.get_all_bin_mappings()) - 5} more")
            
            return config_reader
        else:
            print("❌ Failed to load configuration")
            return None
            
    except Exception as e:
        print(f"❌ Error in ConfigReader demo: {e}")
        return None

def demo_bin_summary_enhancement(config_reader):
    """Demonstrate the enhanced Bin_Summary sheet functionality"""
    print("\n🔍 Demonstrating Enhanced Bin_Summary Sheet...")
    print("=" * 60)
    
    try:
        from full_map_processor import FullMapProcessor
        from openpyxl import Workbook
        
        # Create processor with config
        processor = FullMapProcessor()
        if config_reader:
            processor.set_config_reader(config_reader)
            print("✅ Configuration applied to processor")
        
        # Create demo workbook
        workbook = Workbook()
        worksheet = workbook.active
        worksheet.title = "Bin_Summary"
        
        # Create mock data for demonstration
        mock_processors = {
            "demo_wafer_01.map": {
                'processor': type('MockProcessor', (), {
                    'get_test_statistics': lambda: {
                        'total_tested': 2500,
                        'pass_count': 2375,
                        'yield_percentage': 95.0
                    },
                    'get_bin_statistics': lambda sort_by_quantity=True: [
                        {'bin_name': 'bin0', 'quantity': 2375, 'yield_percentage': 95.0},
                        {'bin_name': 'bin1', 'quantity': 75, 'yield_percentage': 3.0},
                        {'bin_name': 'bin2', 'quantity': 30, 'yield_percentage': 1.2},
                        {'bin_name': 'bin5', 'quantity': 20, 'yield_percentage': 0.8}
                    ]
                })(),
                'file_path': '/demo/demo_wafer_01.map'
            },
            "demo_wafer_02.map": {
                'processor': type('MockProcessor', (), {
                    'get_test_statistics': lambda: {
                        'total_tested': 2400,
                        'pass_count': 2280,
                        'yield_percentage': 95.0
                    },
                    'get_bin_statistics': lambda sort_by_quantity=True: [
                        {'bin_name': 'bin0', 'quantity': 2280, 'yield_percentage': 95.0},
                        {'bin_name': 'bin1', 'quantity': 72, 'yield_percentage': 3.0},
                        {'bin_name': 'bin2', 'quantity': 28, 'yield_percentage': 1.17},
                        {'bin_name': 'bin5', 'quantity': 20, 'yield_percentage': 0.83}
                    ]
                })(),
                'file_path': '/demo/demo_wafer_02.map'
            }
        }
        
        # Add mock sheets to workbook
        for filename in mock_processors.keys():
            sheet_name = os.path.splitext(filename)[0]
            workbook.create_sheet(title=sheet_name)
        
        print("🔄 Creating enhanced Bin_Summary sheet...")
        
        # Create the enhanced Bin_Summary sheet
        processor._create_bin_summary_sheet(worksheet, mock_processors, workbook)
        
        print("\n📋 Verifying Enhanced Features:")
        print("=" * 50)
        
        # Check new headers (consecutive columns starting from 132)
        new_headers = [
            (132, "Tester", "Column 132"),
            (133, "Probe card no.", "Column 133"),
            (134, "Test Program", "Column 134"),
            (135, "Test Flow", "Column 135")
        ]
        
        print("🔍 New Headers Added:")
        for col_num, expected_header, excel_ref in new_headers:
            actual_header = worksheet.cell(row=5, column=col_num).value
            status = "✅" if actual_header == expected_header else "❌"
            print(f"  {excel_ref}: '{actual_header}' {status}")
        
        # Check data in first row
        print(f"\n🔍 Configuration Data in Row 6:")
        if config_reader:
            config_data = [
                (132, config_reader.get_tester_name(), "Tester Name"),
                (133, config_reader.get_probe_card_no(), "Probe Card No."),
                (134, config_reader.get_test_program_name(), "Test Program"),
                (135, config_reader.get_test_flow(), "Test Flow")
            ]
            
            for col_num, expected_data, description in config_data:
                actual_data = worksheet.cell(row=6, column=col_num).value
                status = "✅" if actual_data == expected_data else "❌"
                print(f"  {description}: '{actual_data}' {status}")
        
        # Save demo output
        output_path = "demo_worker_file5_output.xlsx"
        workbook.save(output_path)
        workbook.close()
        
        print(f"\n✅ Demo output saved: {output_path}")
        print("✅ Enhanced Bin_Summary demonstration completed!")
        
        return True
        
    except Exception as e:
        print(f"❌ Error in Bin_Summary demo: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run the complete Worker File 5 enhancement demonstration"""
    print("🚀 Worker File 5 Enhancement - Feature Demonstration")
    print("=" * 80)
    print("This demo showcases the new Bin_Summary sheet enhancements:")
    print("• Enhanced ConfigReader with D5, F5, H5 cell reading")
    print("• New headers: Tester, Probe card no., Test Program, Test Flow")
    print("• Automatic data filling from configuration file")
    print("=" * 80)
    
    # Demo 1: Enhanced ConfigReader
    config_reader = demo_config_reader()
    
    # Demo 2: Enhanced Bin_Summary
    if config_reader:
        demo_bin_summary_enhancement(config_reader)
    else:
        print("\n⚠️  Skipping Bin_Summary demo due to ConfigReader issues")
    
    print("\n" + "=" * 80)
    print("🎉 Worker File 5 Enhancement Demonstration Complete!")
    print("=" * 80)
    print("📁 Files generated:")
    print("  • demo_worker_file5_output.xlsx - Enhanced Bin_Summary sheet")
    print("  • Configuration file in temp directory")
    print("\n💡 Next steps:")
    print("  • Open the generated Excel file to see the new features")
    print("  • Check columns EB, EC, ED, EF for the new data")
    print("  • Verify that original bin statistics are preserved")

if __name__ == "__main__":
    main()
