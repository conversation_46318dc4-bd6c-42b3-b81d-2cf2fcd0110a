#!/usr/bin/env python3
"""
NEPES Precise Processor Verification Test
Validates exact binary modifications according to worker_file9.txt examples

Author: Yuribytes
Company: Chipone TE development Team
"""

import os
import struct
from nepes_precise_processor import NEPESPreciseProcessor
from tsk_map_processor import TSKMapProcessor


class NEPESPreciseVerificationTester:
    """Verification tester for NEPES precise processor"""
    
    def __init__(self):
        self.processor = NEPESPreciseProcessor()
        self.test_results = {}
    
    def test_example_positions(self):
        """Test specific examples from worker_file9.txt"""
        print("🧪 Test 1: Example Position Verification")
        print("-" * 50)
        
        try:
            bump_file = "test/D97127.09"
            dummy_file = "test/009.NNS157-09-E4"
            output_file = "test/verification_output.tsk"
            
            # Process files
            success = self.processor.process_nepes_precise(bump_file, dummy_file, output_file)
            
            if success:
                # Read the output file for verification
                with open(output_file, 'rb') as f:
                    output_data = f.read()
                
                print(f"\n🔍 Verifying specific examples from worker_file9.txt:")
                
                # Example 1: x=0, y=0 position should be "__" → all zeros
                pos1 = self.processor.calculate_category_position(0, 0)
                bytes1 = output_data[pos1:pos1+4]
                expected1 = struct.pack('<I', 0b00000000000000000000000000000000)

                print(f"   Example 1 - x=0, y=0 (position {pos1}):")
                print(f"     Expected: {expected1.hex()} (all zeros for '__')")
                print(f"     Actual:   {bytes1.hex()}")
                
                # Example 2: x=79, y=0 position should be "00" → 63
                pos2 = self.processor.calculate_category_position(79, 0)
                bytes2 = output_data[pos2:pos2+4]
                expected2 = struct.pack('<I', 0b00000000000000000000000000111111)
                
                print(f"   Example 2 - x=79, y=0 (position {pos2}):")
                print(f"     Expected: {expected2.hex()} (63)")
                print(f"     Actual:   {bytes2.hex()}")
                
                # Example 3: x=149, y=2 position should be "0C" → 59
                pos3 = self.processor.calculate_category_position(149, 2)
                bytes3 = output_data[pos3:pos3+4]
                expected3 = struct.pack('<I', 0b00000000000000000000000000111011)
                
                print(f"   Example 3 - x=149, y=2 (position {pos3}):")
                print(f"     Expected: {expected3.hex()} (59)")
                print(f"     Actual:   {bytes3.hex()}")
                
                # Verify examples
                example1_correct = bytes1 == expected1
                example2_correct = bytes2 == expected2
                example3_correct = bytes3 == expected3
                
                print(f"\n📊 Example Verification Results:")
                print(f"   Example 1 (x=0, y=0): {'✅' if example1_correct else '❌'}")
                print(f"   Example 2 (x=79, y=0): {'✅' if example2_correct else '❌'}")
                print(f"   Example 3 (x=149, y=2): {'✅' if example3_correct else '❌'}")
                
                if example1_correct and example2_correct and example3_correct:
                    print(f"   ✅ All examples verified correctly!")
                    self.test_results["example_positions"] = True
                else:
                    print(f"   ❌ Some examples failed verification")
                    self.test_results["example_positions"] = False
            else:
                print(f"❌ Processing failed")
                self.test_results["example_positions"] = False
                
        except Exception as e:
            print(f"❌ Example position test failed: {e}")
            self.test_results["example_positions"] = False
        
        return self.test_results.get("example_positions", False)
    
    def test_binary_pattern_accuracy(self):
        """Test binary pattern accuracy for different bump values"""
        print("\n🧪 Test 2: Binary Pattern Accuracy")
        print("-" * 50)
        
        try:
            # Test binary pattern generation
            processor = NEPESPreciseProcessor()
            
            # Test patterns
            pattern_empty_dash = processor.get_binary_modification_pattern("--")
            pattern_empty_underscore = processor.get_binary_modification_pattern("__")
            pattern_pass = processor.get_binary_modification_pattern("00")
            pattern_fail = processor.get_binary_modification_pattern("0C")

            # Expected patterns
            expected_empty = struct.pack('<I', 0b00000000000000000000000000000000)  # 0
            expected_pass = struct.pack('<I', 0b00000000000000000000000000111111)   # 63
            expected_fail = struct.pack('<I', 0b00000000000000000000000000111011)   # 59

            print(f"   Binary Pattern Tests:")
            print(f"     Empty (--): Expected {expected_empty.hex()}, Got {pattern_empty_dash.hex()}")
            print(f"     Empty (__): Expected {expected_empty.hex()}, Got {pattern_empty_underscore.hex()}")
            print(f"     Pass (00):  Expected {expected_pass.hex()}, Got {pattern_pass.hex()}")
            print(f"     Fail (0C):  Expected {expected_fail.hex()}, Got {pattern_fail.hex()}")
            
            # Verify patterns
            empty_dash_correct = pattern_empty_dash == expected_empty
            empty_underscore_correct = pattern_empty_underscore == expected_empty
            pass_correct = pattern_pass == expected_pass
            fail_correct = pattern_fail == expected_fail

            print(f"\n   Pattern Verification:")
            print(f"     Empty (--) pattern: {'✅' if empty_dash_correct else '❌'}")
            print(f"     Empty (__) pattern: {'✅' if empty_underscore_correct else '❌'}")
            print(f"     Pass pattern:       {'✅' if pass_correct else '❌'}")
            print(f"     Fail pattern:       {'✅' if fail_correct else '❌'}")

            if empty_dash_correct and empty_underscore_correct and pass_correct and fail_correct:
                print(f"   ✅ All binary patterns correct!")
                self.test_results["binary_patterns"] = True
            else:
                print(f"   ❌ Some binary patterns incorrect")
                self.test_results["binary_patterns"] = False
                
        except Exception as e:
            print(f"❌ Binary pattern test failed: {e}")
            self.test_results["binary_patterns"] = False
        
        return self.test_results.get("binary_patterns", False)
    
    def test_position_calculation(self):
        """Test position calculation accuracy"""
        print("\n🧪 Test 3: Position Calculation Accuracy")
        print("-" * 50)
        
        try:
            # Load dummy map to get TestResultCategory
            dummy_file = "test/009.NNS157-09-E4"
            processor = NEPESPreciseProcessor()
            
            if processor.load_dummy_map(dummy_file):
                print(f"   TSK Parameters:")
                print(f"     TestResultCategory: {processor.TestResultCategory}")
                print(f"     Columnsize: {processor.columnsize}")
                print(f"     Rowsize: {processor.rowsize}")
                
                # Test specific position calculations from worker_file9.txt
                # Example: x=0, y=0 → position 15049
                pos1 = processor.calculate_category_position(0, 0)
                expected_pos1 = 15049
                
                # Example: x=79, y=0 → position 15049 + 79*4 = 15365
                pos2 = processor.calculate_category_position(79, 0)
                expected_pos2 = 15049 + 79 * 4
                
                # Example: x=149, y=2 → position 15049 + (2*305 + 149)*4 = 18085
                pos3 = processor.calculate_category_position(149, 2)
                expected_pos3 = 15049 + (2 * 305 + 149) * 4
                
                print(f"\n   Position Calculations:")
                print(f"     x=0, y=0:   Expected {expected_pos1}, Got {pos1}")
                print(f"     x=79, y=0:  Expected {expected_pos2}, Got {pos2}")
                print(f"     x=149, y=2: Expected {expected_pos3}, Got {pos3}")
                
                # Verify calculations
                pos1_correct = pos1 == expected_pos1
                pos2_correct = pos2 == expected_pos2
                pos3_correct = pos3 == expected_pos3
                
                print(f"\n   Calculation Verification:")
                print(f"     Position 1: {'✅' if pos1_correct else '❌'}")
                print(f"     Position 2: {'✅' if pos2_correct else '❌'}")
                print(f"     Position 3: {'✅' if pos3_correct else '❌'}")
                
                if pos1_correct and pos2_correct and pos3_correct:
                    print(f"   ✅ All position calculations correct!")
                    self.test_results["position_calculation"] = True
                else:
                    print(f"   ❌ Some position calculations incorrect")
                    self.test_results["position_calculation"] = False
            else:
                print(f"   ❌ Failed to load dummy map")
                self.test_results["position_calculation"] = False
                
        except Exception as e:
            print(f"❌ Position calculation test failed: {e}")
            self.test_results["position_calculation"] = False
        
        return self.test_results.get("position_calculation", False)
    
    def test_output_file_integrity(self):
        """Test output file integrity"""
        print("\n🧪 Test 4: Output File Integrity")
        print("-" * 50)
        
        try:
            dummy_file = "test/009.NNS157-09-E4"
            output_file = "test/verification_output.tsk"
            
            if os.path.exists(output_file):
                # Compare file sizes
                original_size = os.path.getsize(dummy_file)
                output_size = os.path.getsize(output_file)
                
                print(f"   File Size Comparison:")
                print(f"     Original: {original_size} bytes")
                print(f"     Output:   {output_size} bytes")
                
                size_match = original_size == output_size
                print(f"     Size match: {'✅' if size_match else '❌'}")
                
                # Test if output can be read by TSK processor
                tsk_reader = TSKMapProcessor()
                read_success = tsk_reader.read_file(output_file)
                
                if read_success:
                    header_success = tsk_reader.parse_file_header()
                    print(f"     TSK readability: {'✅' if header_success else '❌'}")
                    
                    if header_success:
                        print(f"     Output dimensions: {tsk_reader.rowsize} × {tsk_reader.columnsize}")
                        print(f"     Output TestResultCategory: {tsk_reader.TestResultCategory}")
                else:
                    print(f"     TSK readability: ❌")
                    header_success = False
                
                if size_match and header_success:
                    print(f"   ✅ Output file integrity verified!")
                    self.test_results["file_integrity"] = True
                else:
                    print(f"   ❌ Output file integrity issues")
                    self.test_results["file_integrity"] = False
            else:
                print(f"   ❌ Output file not found")
                self.test_results["file_integrity"] = False
                
        except Exception as e:
            print(f"❌ File integrity test failed: {e}")
            self.test_results["file_integrity"] = False
        
        return self.test_results.get("file_integrity", False)
    
    def run_all_verification_tests(self):
        """Run all verification tests"""
        print("🚀 NEPES Precise Processor - Verification Test Suite")
        print("=" * 70)
        
        # Run tests in sequence
        self.test_example_positions()
        self.test_binary_pattern_accuracy()
        self.test_position_calculation()
        self.test_output_file_integrity()
        
        # Summary
        print("\n" + "=" * 70)
        print("📋 Verification Test Results Summary")
        print("=" * 70)
        
        total_tests = len(self.test_results)
        passed_tests = sum(self.test_results.values())
        
        for test_name, result in self.test_results.items():
            status = "✅ PASS" if result else "❌ FAIL"
            print(f"{test_name:<25} {status}")
        
        print("-" * 70)
        print(f"Total Tests: {total_tests}")
        print(f"Passed: {passed_tests}")
        print(f"Failed: {total_tests - passed_tests}")
        print(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
        
        if passed_tests == total_tests:
            print("\n🎉 All verification tests PASSED!")
            print("✅ Binary modifications are exact and correct!")
            print("🔧 Worker_file9.txt specifications fully implemented!")
            print("📊 Ready for production use!")
        else:
            print(f"\n⚠️  {total_tests - passed_tests} verification test(s) failed.")
        
        return passed_tests == total_tests


def main():
    """Main verification test function"""
    print("🧪 Starting NEPES Precise Processor Verification")
    print("Testing exact compliance with worker_file9.txt specifications\n")
    
    tester = NEPESPreciseVerificationTester()
    success = tester.run_all_verification_tests()
    
    if success:
        print("\n🎯 NEPES Precise Processor verification completed successfully!")
        print("🔧 All binary operations verified as exact and correct!")
        print("📊 Worker_file9.txt specifications fully satisfied!")
    else:
        print("\n🔧 Some verification tests failed. Review implementation.")
    
    return success


if __name__ == "__main__":
    main()
