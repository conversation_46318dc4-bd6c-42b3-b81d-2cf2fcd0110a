#!/usr/bin/env python3
"""
Test script for main.py functionality
Verifies that main.py can be imported and all functions work correctly
"""

import sys
import os
import subprocess

# Add parent directory to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


def test_main_import():
    """Test that main.py can be imported without errors"""
    print("Testing main.py import...")
    
    try:
        import main
        print("✅ main.py imported successfully")
        return True
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Other error: {e}")
        return False


def test_main_help():
    """Test main.py --help command"""
    print("\nTesting main.py --help...")
    
    try:
        result = subprocess.run([sys.executable, "main.py", "--help"], 
                              capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            print("✅ --help command works")
            if "TSK/MAP File Processor" in result.stdout:
                print("✅ Help text contains expected content")
                return True
            else:
                print("❌ Help text missing expected content")
                return False
        else:
            print(f"❌ --help command failed with return code {result.returncode}")
            print(f"Error: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ --help command timed out")
        return False
    except Exception as e:
        print(f"❌ Error running --help: {e}")
        return False


def test_main_version():
    """Test main.py --version command"""
    print("\nTesting main.py --version...")
    
    try:
        result = subprocess.run([sys.executable, "main.py", "--version"], 
                              capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            print("✅ --version command works")
            if "TSK/MAP File Processor v1.0" in result.stdout:
                print("✅ Version text contains expected content")
                return True
            else:
                print("❌ Version text missing expected content")
                return False
        else:
            print(f"❌ --version command failed with return code {result.returncode}")
            print(f"Error: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ --version command timed out")
        return False
    except Exception as e:
        print(f"❌ Error running --version: {e}")
        return False


def test_main_dependencies():
    """Test dependency checking function"""
    print("\nTesting dependency checking...")
    
    try:
        import main
        
        # Test check_dependencies function
        if main.check_dependencies():
            print("✅ Dependencies check passed")
            return True
        else:
            print("❌ Dependencies check failed")
            return False
            
    except Exception as e:
        print(f"❌ Error testing dependencies: {e}")
        return False


def test_main_functions():
    """Test individual functions in main.py"""
    print("\nTesting main.py functions...")
    
    try:
        import main
        
        # Test print_usage function
        print("Testing print_usage()...")
        main.print_usage()
        print("✅ print_usage() works")
        
        # Test print_version function
        print("Testing print_version()...")
        main.print_version()
        print("✅ print_version() works")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing functions: {e}")
        return False


def test_gui_imports():
    """Test that GUI classes can be imported"""
    print("\nTesting GUI imports...")
    
    try:
        from tool_selector import ToolSelector
        print("✅ ToolSelector imported")
        
        from tsk_map_gui import ABMapGUI
        print("✅ ABMapGUI imported")
        
        from full_map_gui import FullMapGUI
        print("✅ FullMapGUI imported")
        
        return True
        
    except ImportError as e:
        print(f"❌ GUI import error: {e}")
        return False
    except Exception as e:
        print(f"❌ GUI import other error: {e}")
        return False


def main():
    """Run all tests"""
    print("Main.py Functionality Test")
    print("=" * 50)
    
    tests = [
        test_main_import,
        test_main_help,
        test_main_version,
        test_main_dependencies,
        test_main_functions,
        test_gui_imports
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ Test {test.__name__} failed with exception: {e}")
    
    print("\n" + "=" * 50)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All main.py functionality tests passed!")
        return True
    else:
        print("❌ Some tests failed")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
