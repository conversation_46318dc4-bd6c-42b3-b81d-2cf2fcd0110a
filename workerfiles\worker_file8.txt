任务流程：
完善Bump map To Tsk map Tool功能:
 1, 当前开发的是NEPES Corporation的bump to tsk代码，其他测试厂需要待有特定格式后完善，可以提前预留架构。
    NEPES选择的bump map描述：
      --原始bump map文件是一个snif文件格式文件（也可能没有后缀格式，可以不用管这部分），此map可以使用txt文本工具打开分析。这个文件检索关键字“RowData:”这个后面的内容是bump的所有bin别。与我们之前一直在操作的tsk map是对应的。
	  --原始bump map的这部分展示的是“__”和“00”和“XX”，针对这个部分，我们需要的是
	NEPES选择的原始dummy map文件描述：
      --dummy map格式和内容与我们一直执行的Full Map tool的解析规则是一致的。这个dummy map测试的数量是与刚load的bump map一致（但是dummy map是包含空和bin0）。我们需要按照之前的Full Map tool进行解析并修改dummy map最终生成需要的tsk map。
	  --修改规则:bump map中的“00”对应到dummy map中0的位置，全部转为63（按照之前了解的tsk map格式（二进制修改）），bump map除00位置（“--” 和其他非"00"的任何数值）对应到dummy map中的空，全部转为59（按照之前了解的tsk map格式（二进制修改））
 2, 请先尝试理解上面的规则，按照需求尝试修改代码。因为现在设计NEPES格式部分，需要单独生成文件来完成代码开发，架构设计完成后，单独完成NEPES代码部分，预留其他测试厂代码部分（可以暂时不开发）
 
上述功能开发注意事项：
     --- 其他两个功能不要破坏，代码功能不要破坏
     --- GUI尽量统一，不要占用太多空间
     --- 复用代码部分，尽量做到代码统一

要求：
1，测试脚本和文件请放到test文件夹
2，生成的md说明文档，请放到temp文件夹
3，程序架构统一，框架统一，代码尽量简洁

测试文件说明：
1，可以使用test文件夹下的D97127.09作为bump map的load
2，可以使用test文件夹下的009.NNS157-09-E4作为dummy map的文件