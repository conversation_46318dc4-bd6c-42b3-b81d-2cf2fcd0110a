#!/usr/bin/env python3
"""
Test Device Info - Verify device information extraction and Excel output
"""

import sys
import os
sys.path.append('..')
from tsk_map_processor import TSKMapProcessor
from excel_output import create_excel_output


def test_device_info_extraction(filepath):
    """
    Test device information extraction from TSK file
    """
    print(f"Testing Device Info Extraction: {os.path.basename(filepath)}")
    print("=" * 60)
    
    if not os.path.exists(filepath):
        print(f"❌ File not found: {filepath}")
        return False
    
    processor = TSKMapProcessor()
    
    # Read and parse file
    if not processor.read_file(filepath):
        print("❌ Failed to read file")
        return False
    
    if not processor.parse_file_header():
        print("❌ Failed to parse header")
        return False
    
    print(f"✅ File parsed successfully")
    
    # Display extracted device information
    print(f"\nExtracted Device Information:")
    print(f"  Device Name: '{processor.devicename}'")
    print(f"  Lot ID: '{processor.lotid}'")
    print(f"  Wafer Slot ID: '{processor.waferslotid}'")
    print(f"  Start Time: '{processor.waferstarttime}'")
    print(f"  End Time: '{processor.waferendtime}'")
    
    # Show raw binary data for debugging
    print(f"\nRaw Binary Data (for debugging):")
    try:
        device_binary = processor.get_binary(processor.filearray, 20, 35)
        print(f"  Device Name binary (20-35): {device_binary[:64]}...")
        
        lot_binary = processor.get_binary(processor.filearray, 82, 99)
        print(f"  Lot ID binary (82-99): {lot_binary[:64]}...")
        
        wafer_binary = processor.get_binary(processor.filearray, 102, 103)
        print(f"  Wafer ID binary (102-103): {wafer_binary}")
        
        start_time_binary = processor.get_binary(processor.filearray, 148, 159)
        print(f"  Start Time binary (148-159): {start_time_binary[:64]}...")
        
        end_time_binary = processor.get_binary(processor.filearray, 160, 171)
        print(f"  End Time binary (160-171): {end_time_binary[:64]}...")
        
    except Exception as e:
        print(f"  Error reading raw binary: {e}")
    
    return True


def test_statistics_calculation(filepath):
    """
    Test statistics calculation
    """
    print(f"\nTesting Statistics Calculation")
    print("=" * 40)
    
    processor = TSKMapProcessor()
    
    if not processor.read_file(filepath):
        return False
    
    if not processor.parse_file_header():
        return False
    
    if not processor.process_die_data():
        print("❌ Failed to process die data")
        return False
    
    # Get statistics
    stats = processor.get_test_statistics()
    
    print(f"Test Statistics:")
    print(f"  Total Tested: {stats['total_tested']}")
    print(f"  Pass Count: {stats['pass_count']}")
    print(f"  Fail Count: {stats['total_tested'] - stats['pass_count']}")
    print(f"  Yield: {stats['yield_percentage']:.2f}%")
    
    return True


def test_excel_output_with_device_info(filepath):
    """
    Test Excel output with device information
    """
    print(f"\nTesting Excel Output with Device Info")
    print("=" * 45)
    
    processor = TSKMapProcessor()
    
    if not processor.read_file(filepath):
        return False
    
    if not processor.parse_file_header():
        return False
    
    if not processor.process_die_data():
        return False
    
    # Create Excel output with device info
    output_filename = "test_device_info_output.xlsx"
    
    try:
        if create_excel_output(processor, "TestSheet", 0, output_filename, filepath):
            print(f"✅ Excel output created: {output_filename}")
            
            # Check file size
            if os.path.exists(output_filename):
                file_size = os.path.getsize(output_filename)
                print(f"  File size: {file_size:,} bytes")
                
                print(f"  Device info should be in columns A-D:")
                print(f"    A1-A12: Labels (Device Info, Device Name, etc.)")
                print(f"    B2: Device Name = '{processor.devicename}'")
                print(f"    B4: Lot ID = '{processor.lotid}'")
                print(f"    B5: Wafer ID = '{processor.waferslotid}'")
                print(f"    B6: Start Time = '{processor.waferstarttime}'")
                print(f"    B7: End Time = '{processor.waferendtime}'")
                
                stats = processor.get_test_statistics()
                print(f"    B8: Total Tested = {stats['total_tested']}")
                print(f"    B9: Pass Count = {stats['pass_count']}")
                print(f"    B10: Yield = {stats['yield_percentage']:.2f}%")
                
                print(f"  Map data should start from column E (5)")
                
                return True
            else:
                print("❌ Output file not found")
                return False
        else:
            print("❌ Failed to create Excel output")
            return False
            
    except Exception as e:
        print(f"❌ Error creating Excel output: {e}")
        return False


def main():
    """
    Main test function
    """
    if len(sys.argv) < 2:
        print("Device Info Test")
        print("Usage: python test_device_info.py <tsk_file_path>")
        print("Example: python test_device_info.py ../3AA111-01-B4")
        return
    
    filepath = sys.argv[1]
    
    print("TSK/MAP Device Information Test")
    print("Testing device info extraction and Excel output formatting")
    print("=" * 70)
    
    # Test 1: Device info extraction
    if not test_device_info_extraction(filepath):
        print("❌ Device info extraction test failed")
        return
    
    # Test 2: Statistics calculation
    if not test_statistics_calculation(filepath):
        print("❌ Statistics calculation test failed")
        return
    
    # Test 3: Excel output with device info
    if not test_excel_output_with_device_info(filepath):
        print("❌ Excel output test failed")
        return
    
    print("\n" + "=" * 70)
    print("🎉 All Device Info Tests Passed!")
    print("\nKey Features Implemented:")
    print("✅ Device name extraction (position 20-35)")
    print("✅ Lot ID extraction (position 82-99)")
    print("✅ Wafer ID extraction (position 102-103)")
    print("✅ Start/End time extraction (positions 148-159, 160-171)")
    print("✅ Test statistics calculation")
    print("✅ Excel output with device info in columns A-D")
    print("✅ Map data offset to column E to accommodate device info")
    
    print(f"\nYou can now use the main GUI:")
    print(f"cd .. && python main.py")


if __name__ == "__main__":
    main()
