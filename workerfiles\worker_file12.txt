任务流程：
针对Bump map To Tsk map Tool现在针对工具代码实际验证修改完善:
   -----首先修正我的描述：bump map中的"__"为检索关键字，不是之前提到的"--"，bump map中的"__"内容，在dummy map中对应的x，y位置不修改，维持原dummy map中的二进制数值不变。
   -----现在代码逻辑设计是没有问题的，通过实际数据分析，代码现在执行的map version=2或者3的操作逻辑，修改地方是从第1bit到第16bit。
   -----map version=4 部分需要新增代码修改逻辑，现在代码修改的不是map version=4的逻辑。我在截图已经标记了。
   -----map version=4的修改方式：
   -----根据你已经理解的dummy map和bump map的描述，开始设计生成output tsk map部分(下面的描述是map version=4的方式)：
      ---根据bump map设计的检索内容，如果bump map中对应x，y位置，读出“__”，bump map中的"__"内容，在dummy map中对应的x，y位置不修改，维持原dummy map中的二进制数值不变。（二进制确认可以复用tsk_map_processor.py的process_die_data）
	  ---根据bump map设计的检索内容，如果bump map中对应x，y位置，读出“00”，dummy map中相应x，y位置的二进制修改为（00000000000000000000000000111111）（二进制确认可以复用tsk_map_processor.py的process_die_data）
	  ---根据bump map设计的检索内容，如果bump map中对应x，y位置，读出“XX”除“00”和“__”的其他内容，dummy map中相应x，y位置的二进制修改为（00000000000000000000000000111011）（二进制确认可以复用tsk_map_processor.py的process_die_data）
	  
针对刚才的测试文件(map version=4)：
    x=0,y=0, 修改byte位置：15049 byte开始，因为检测到bump map为“__”，所以dummy map 二进制维持不变。所以是15049为00000000，15050为00000000，15051为00000000，15052为00000000
	x=79，y=0,修改byte位置：15365 byte开始，因为检测到bump map为“00”，所以dummy map 二进制改为（00000000000000000000000000111111），所以是15365为00000000，15366为00000000，15367为00000000，15368为00111111，实际上map version=4是修改10bit,
	如果是这个例子：需要修改15367的bit0和bit1，15368的8bit，bit7为大端，bit0为小端。
    x=149，y=2,修改byte位置：18085 byte开始，因为检测到bump map为“XX”，所以dummy map 二进制改为（00000000000000000000000000111011），所以是18085为00000000，18086为00000000，18087为00000000，18088为00111011，实际上map version=4是修改10bit,
	如果是这个例子：需要修改18087的bit0和bit1，18088的8bit，bit7为大端，bit0为小端。
	
根据例子依次类推，完成全部修改的代码设计。

注意：现有的map version =2和3的处理方式代码请保留。	

上述功能开发注意事项：
     --- 其他两个功能不要破坏，代码功能不要破坏
     --- GUI尽量统一，不要占用太多空间
     --- 复用代码部分，尽量做到代码统一

要求：
1，测试脚本和文件请放到test文件夹
2，生成的md说明文档，请放到temp文件夹
3，程序架构统一，框架统一，代码尽量简洁

测试文件说明：
1，可以使用test文件夹下的D97127.09作为bump map的load
2，可以使用test文件夹下的009.NNS157-09-E4作为dummy map的文件