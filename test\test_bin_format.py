#!/usr/bin/env python3
"""
Test script for bin format display in TMB files
Tests the modified generate_upper_part method
"""

import os
import sys

# Add parent directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from tmb_processor import TMBProcessor
from config_reader import Config<PERSON><PERSON><PERSON>


def test_bin_format():
    """Test bin format display"""
    print("=" * 60)
    print("Testing bin format display...")
    
    # Setup
    tmb_processor = TMBProcessor()
    config_reader = ConfigReader()
    
    # Load config
    config_file = "test/CP1_program_bin.xlsx"
    if not config_reader.read_config_file(config_file):
        print("❌ Failed to load config file")
        return False
    
    tmb_processor.set_config_reader(config_reader)
    
    # Test with sample MAP file
    map_file = "test/019.3AD416-19-F4"
    if not os.path.exists(map_file):
        print(f"❌ MAP file not found: {map_file}")
        return False
    
    # Process MAP file
    tmb_output = tmb_processor.process_map_to_tmb(map_file)
    
    if tmb_output and os.path.exists(tmb_output):
        print(f"✅ TMB file generated: {os.path.basename(tmb_output)}")
        
        # Read and analyze content
        with open(tmb_output, 'r', encoding='utf-8') as f:
            content = f.read()
        
        lines = content.split('\n')
        
        # Find header line and separator line
        header_line = None
        separator_line = None
        data_lines = []
        
        for i, line in enumerate(lines):
            if line.strip().startswith('00 01 02'):
                header_line = line
                print(f"Header line found at line {i+1}")
            elif '---' in line and len(line) > 50:
                separator_line = line
                print(f"Separator line found at line {i+1}")
            elif line.strip().startswith(('00|', '01|', '02|', '03|', '04|')):
                data_lines.append((i+1, line))
        
        # Analyze header format
        if header_line:
            print(f"\nHeader line analysis:")
            print(f"Length: {len(header_line)}")
            print(f"First 50 chars: '{header_line[:50]}'")
            
            # Check column spacing
            parts = header_line.split()
            print(f"Header parts: {parts[:10]}...")  # Show first 10 parts
        
        # Analyze separator format  
        if separator_line:
            print(f"\nSeparator line analysis:")
            print(f"Length: {len(separator_line)}")
            print(f"First 50 chars: '{separator_line[:50]}'")
        
        # Analyze data lines format
        if data_lines:
            print(f"\nData lines analysis (first 3 lines):")
            for line_num, line in data_lines[:3]:
                print(f"Line {line_num}: Length={len(line)}")
                # Show first part of the line to check format
                if '|' in line:
                    row_header, data_part = line.split('|', 1)
                    print(f"  Row header: '{row_header}|'")
                    print(f"  Data part (first 50 chars): '{data_part[:50]}'")
                    
                    # Analyze bin format in data part
                    # Look for patterns like "  1", " 71", "103"
                    data_chars = list(data_part)
                    bin_positions = []
                    i = 0
                    while i < len(data_chars) - 2:
                        three_chars = ''.join(data_chars[i:i+3])
                        if three_chars.strip().isdigit():
                            bin_positions.append((i, three_chars))
                        i += 3
                    
                    if bin_positions:
                        print(f"  Found bin positions: {bin_positions[:5]}...")  # Show first 5
        
        print(f"\nTMB file saved to: {tmb_output}")
        return True
    else:
        print("❌ Failed to generate TMB file")
        return False


def main():
    """Main test function"""
    print("Testing bin format display in TMB files")
    print("=" * 60)
    
    # Change to script directory
    script_dir = os.path.dirname(os.path.abspath(__file__))
    parent_dir = os.path.dirname(script_dir)
    os.chdir(parent_dir)
    
    print(f"Working directory: {os.getcwd()}")
    
    # Run test
    try:
        result = test_bin_format()
        if result:
            print("\n✅ Bin format test completed successfully")
        else:
            print("\n❌ Bin format test failed")
    except Exception as e:
        print(f"\n❌ Test error: {e}")


if __name__ == "__main__":
    main()
