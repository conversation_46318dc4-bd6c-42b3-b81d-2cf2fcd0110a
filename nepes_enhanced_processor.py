#!/usr/bin/env python3
"""
NEPES Enhanced Bump Map to TSK Map Processor
Enhanced version based on worker_file10.txt feedback and analysis
Correctly handles "__" positions (no modification) and supports Map Version 4

Author: Yuribytes
Company: Chipone TE development Team
"""

import os
import struct
import re
from typing import Dict, List, Tuple, Optional, Any
from tsk_map_processor import TSKMapProcessor


class NEPESEnhancedProcessor:
    """NEPES enhanced processor with correct "__" handling and Map Version 4 support"""
    
    def __init__(self):
        self.tsk_processor = TSKMapProcessor()
        self.bump_data = []
        self.header_info = {}
        self.rows = 0
        self.cols = 0
        self.total_dies = 0
        
        # TSK format variables
        self.columnsize = 0
        self.rowsize = 0
        self.map_version = 0
        self.TestResultStartPos = 0
        self.TestResultCategory = 0
        
        # Processing statistics
        self.processing_stats = {
            'total_positions': 0,
            'unchanged_positions': 0,    # "__" positions (no modification)
            'pass_positions': 0,         # "00" positions → 63
            'fail_positions': 0,         # "XX" positions → 59
            'binary_modifications': 0,
            'errors': 0
        }
    
    def load_dummy_map(self, file_path: str) -> bool:
        """Load dummy map and extract TSK format variables"""
        try:
            print(f"📖 Loading dummy map: {file_path}")
            
            # Use TSK processor to read and parse file
            success = self.tsk_processor.read_file(file_path)
            if not success:
                print("❌ Failed to read dummy map file")
                return False
            
            # Parse file header to get required variables
            header_success = self.tsk_processor.parse_file_header()
            if not header_success:
                print("❌ Failed to parse dummy map header")
                return False
            
            # Extract required variables
            self.columnsize = self.tsk_processor.columnsize
            self.rowsize = self.tsk_processor.rowsize
            self.map_version = self.tsk_processor.May_version
            self.TestResultStartPos = self.tsk_processor.TestResultStartPos
            self.TestResultCategory = self.tsk_processor.TestResultCategory
            self.total_dies = self.columnsize * self.rowsize
            
            print(f"✅ Loaded dummy map successfully:")
            print(f"   File size: {len(self.tsk_processor.filearray)} bytes")
            print(f"   Dimensions: {self.rowsize} × {self.columnsize}")
            print(f"   Total dies: {self.total_dies}")
            print(f"   Map version: {self.map_version}")
            print(f"   TestResultStartPos: {self.TestResultStartPos}")
            print(f"   TestResultCategory: {self.TestResultCategory}")
            
            return True
            
        except Exception as e:
            print(f"❌ Error loading dummy map: {e}")
            return False
    
    def parse_bump_map(self, file_path: str) -> bool:
        """Parse NEPES bump map file"""
        try:
            print(f"📖 Parsing NEPES bump map: {file_path}")
            
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
            
            # Extract header information
            self._extract_header_info(content)
            
            # Find and parse RowData lines
            row_data_lines = re.findall(r'RowData:(.*)', content)
            
            if not row_data_lines:
                print("❌ Error: No RowData found in bump map file")
                return False
            
            # Parse each row of data
            self.bump_data = []
            for i, line in enumerate(row_data_lines):
                values = [v.strip() for v in line.strip().split() if v.strip()]
                self.bump_data.append(values)
                
                if i == 0:
                    print(f"   Sample row data: {values[:10]}...")
            
            self.rows = len(self.bump_data)
            self.cols = len(self.bump_data[0]) if self.bump_data else 0
            bump_total = self.rows * self.cols
            
            print(f"✅ Parsed NEPES bump map:")
            print(f"   Dimensions: {self.rows} × {self.cols}")
            print(f"   Total positions: {bump_total}")
            print(f"   Header info: {self.header_info}")
            
            # Verify dimensions match dummy map
            if bump_total != self.total_dies:
                print(f"⚠️  Warning: Bump map total ({bump_total}) != Dummy map total ({self.total_dies})")
            
            return True
            
        except Exception as e:
            print(f"❌ Error parsing NEPES bump map: {e}")
            return False
    
    def _extract_header_info(self, content: str) -> None:
        """Extract header information from NEPES bump map"""
        header_patterns = {
            'DEVICE': r'DEVICE:(.+)',
            'LOT': r'LOT:(.+)',
            'WAFER': r'WAFER:(.+)',
            'ROWCT': r'ROWCT:(\d+)',
            'COLCT': r'COLCT:(\d+)',
            'REFPX': r'REFPX:(.+)',
            'REFPY': r'REFPY:(.+)',
            'FNLOC': r'FNLOC:(\d+)',
            'BCEQU': r'BCEQU:(.+)'
        }
        
        for key, pattern in header_patterns.items():
            match = re.search(pattern, content)
            if match:
                self.header_info[key] = match.group(1).strip()
    
    def get_binary_modification_pattern(self, bump_value: str) -> Optional[bytes]:
        """Get binary modification pattern based on bump map value"""

        # Enhanced logic: All map versions use 32-bit modification
        if bump_value == "__":
            # "__" positions: NO MODIFICATION - return None to indicate no change
            return None
        elif bump_value == "00":
            # Pass: 00000000000000000000000000111111 (63) → 0000003F
            target_binary = 0b00000000000000000000000000111111
        else:
            # Fail (XX): 00000000000000000000000000111011 (59) → 0000003B
            target_binary = 0b00000000000000000000000000111011

        # Convert to 4-byte big-endian format (0000003F / 0000003B)
        return struct.pack('>I', target_binary)

    
    def calculate_category_position(self, x: int, y: int) -> int:
        """Calculate the exact byte position for category data"""
        # Calculate die index (0-based)
        die_index = y * self.columnsize + x
        
        # Calculate category position: TestResultCategory + die_index * 4
        # Each die uses 4 bytes for category data
        category_pos = self.TestResultCategory + die_index * 4
        
        return category_pos
    
    def apply_enhanced_conversion(self) -> bool:
        """Apply enhanced NEPES conversion with correct "__" handling"""
        if not self.bump_data or not self.tsk_processor.filearray:
            print("❌ Error: Bump data or dummy map not loaded")
            return False
        
        try:
            print("🔄 Applying enhanced NEPES conversion...")
            print(f"   Map version: {self.map_version}")
            print(f"   Enhanced logic: '__' positions will NOT be modified")
            
            # Reset statistics
            self.processing_stats = {
                'total_positions': 0,
                'unchanged_positions': 0,
                'pass_positions': 0,
                'fail_positions': 0,
                'binary_modifications': 0,
                'errors': 0
            }
            
            # Process each position using TSK processor's 1-based indexing logic
            # This replicates the exact logic from tsk_map_processor.py process_die_data method
            for i in range(1, self.rowsize + 1):  # VB uses 1-based indexing
                for j in range(1, self.columnsize + 1):  # VB uses 1-based indexing
                    # Calculate die index using TSK processor logic
                    die_index = (i - 1) * self.columnsize + j - 1

                    # Check bounds for bump data (2D array)
                    row_index = i - 1  # Convert to 0-based for array access
                    col_index = j - 1  # Convert to 0-based for array access

                    if row_index >= len(self.bump_data) or col_index >= len(self.bump_data[row_index]):
                        continue  # Skip this position if out of bounds

                    # Get bump map value from 2D array
                    bump_value = self.bump_data[row_index][col_index]

                    # Calculate category position using TSK processor logic
                    # TSK processor calls get_binary(filearray, pos, pos+3) which reads filearray[pos-1:pos+4]
                    # So to match TSK logic, we need to write at pos-1
                    category_pos = self.TestResultCategory + 4 * die_index - 1

                    # Convert to 0-based coordinates for display (x = j-1, y = i-1)
                    display_x = j - 1
                    display_y = i - 1

                    # Verify position is within file bounds (need 4 bytes: category_pos to category_pos+3)
                    if category_pos + 4 <= len(self.tsk_processor.filearray):
                            self.processing_stats['total_positions'] += 1
                            
                            # Get binary modification pattern
                            binary_pattern = self.get_binary_modification_pattern(bump_value)

                            if binary_pattern is None:
                                # "__" position - NO MODIFICATION
                                self.processing_stats['unchanged_positions'] += 1
                                # Log first few unchanged positions for verification
                                if self.processing_stats['unchanged_positions'] <= 3:
                                    original_bytes = self.tsk_processor.filearray[category_pos:category_pos+4]
                                    print(f"   Unchanged position x={display_x}, y={display_y}, pos={category_pos}: {original_bytes.hex()}")
                            else:
                                # Apply 32-bit modification for all map versions
                                # Since we already verified bounds, we can safely write all 4 bytes
                                for k in range(4):
                                    self.tsk_processor.filearray[category_pos + k] = binary_pattern[k]
                                    self.processing_stats['binary_modifications'] += 1

                                # Update statistics
                                if bump_value == "00":
                                    self.processing_stats['pass_positions'] += 1
                                    # Log first few pass positions for verification
                                    if self.processing_stats['pass_positions'] <= 3:
                                        print(f"   Modified position x={display_x}, y={display_y}, pos={category_pos}: {binary_pattern.hex()} (63)")
                                else:
                                    self.processing_stats['fail_positions'] += 1
                                    # Log first few fail positions for verification
                                    if self.processing_stats['fail_positions'] <= 3:
                                        print(f"   Modified position x={display_x}, y={display_y}, pos={category_pos}: {binary_pattern.hex()} (59)")
                    else:
                        # Position is out of bounds due to file truncation
                        if bump_value == "__":
                            # This is an "__" position that should remain unchanged
                            # Since file is truncated, we can't access it, but it would remain unchanged anyway
                            self.processing_stats['unchanged_positions'] += 1
                            print(f"   ⚠️  '__' position out of bounds (file truncated by 1 byte): x={display_x}, y={display_y}, pos={category_pos}")
                            print(f"       Position would remain unchanged anyway - no data loss")
                        else:
                            # This is a real error - a position that needs modification is out of bounds
                            self.processing_stats['errors'] += 1
                            print(f"   ❌ Critical: Position out of bounds: x={display_x}, y={display_y}, pos={category_pos}, value='{bump_value}'")
                            print(f"       This position needs modification but file is truncated!")
            
            print(f"✅ Enhanced conversion completed:")
            print(f"   Total positions: {self.processing_stats['total_positions']}")
            print(f"   Unchanged positions (__): {self.processing_stats['unchanged_positions']}")
            print(f"   Pass positions (00): {self.processing_stats['pass_positions']}")
            print(f"   Fail positions (XX): {self.processing_stats['fail_positions']}")
            print(f"   Binary modifications: {self.processing_stats['binary_modifications']}")
            print(f"   Errors: {self.processing_stats['errors']}")
            
            return True
            
        except Exception as e:
            print(f"❌ Error applying enhanced conversion: {e}")
            return False
    
    def save_output_tsk_map(self, output_path: str) -> bool:
        """Save the converted TSK map"""
        try:
            print(f"💾 Saving converted TSK map: {output_path}")
            
            with open(output_path, 'wb') as f:
                f.write(self.tsk_processor.filearray)
            
            file_size = os.path.getsize(output_path)
            print(f"✅ Saved converted TSK map: {file_size} bytes")
            return True
            
        except Exception as e:
            print(f"❌ Error saving TSK map: {e}")
            return False
    
    def process_nepes_enhanced(self, bump_map_path: str, dummy_map_path: str, output_path: str) -> bool:
        """Complete NEPES enhanced processing pipeline"""
        print("🚀 Starting NEPES Enhanced Processing...")
        print("=" * 70)
        
        try:
            # Step 1: Load dummy map
            print("📋 Step 1: Loading dummy map...")
            if not self.load_dummy_map(dummy_map_path):
                return False
            
            # Step 2: Parse bump map
            print("\n📋 Step 2: Parsing bump map...")
            if not self.parse_bump_map(bump_map_path):
                return False
            
            # Step 3: Apply enhanced conversion
            print("\n📋 Step 3: Applying enhanced conversion...")
            if not self.apply_enhanced_conversion():
                return False
            
            # Step 4: Save output
            print("\n📋 Step 4: Saving output TSK map...")
            if not self.save_output_tsk_map(output_path):
                return False
            
            print("\n" + "=" * 70)
            print("🎉 NEPES Enhanced Processing completed successfully!")
            self._print_final_summary(bump_map_path, dummy_map_path, output_path)
            
            return True
            
        except Exception as e:
            print(f"❌ NEPES enhanced processing failed: {e}")
            return False
    
    def _print_final_summary(self, bump_path: str, dummy_path: str, output_path: str):
        """Print final processing summary"""
        print(f"📊 Processing Summary:")
        print(f"   Input bump map: {bump_path}")
        print(f"   Input dummy map: {dummy_path}")
        print(f"   Output TSK map: {output_path}")
        print(f"   Bump dimensions: {self.rows} × {self.cols}")
        print(f"   TSK dimensions: {self.rowsize} × {self.columnsize}")
        print(f"   Map version: {self.map_version}")
        print(f"   Unchanged positions (__): {self.processing_stats['unchanged_positions']}")
        print(f"   Pass positions (00): {self.processing_stats['pass_positions']}")
        print(f"   Fail positions (XX): {self.processing_stats['fail_positions']}")
        print(f"   Binary modifications: {self.processing_stats['binary_modifications']}")
    
    def get_processing_stats(self) -> Dict[str, Any]:
        """Get detailed processing statistics"""
        return {
            'test_house': 'NEPES Corporation (Enhanced)',
            'bump_dimensions': f"{self.rows} × {self.cols}",
            'tsk_dimensions': f"{self.rowsize} × {self.columnsize}",
            'map_version': self.map_version,
            'tsk_positions': {
                'TestResultStartPos': self.TestResultStartPos,
                'TestResultCategory': self.TestResultCategory
            },
            'header_info': self.header_info,
            'processing_stats': self.processing_stats
        }


def main():
    """Test function for NEPES enhanced processor"""
    print("🧪 NEPES Enhanced Processor - Test")
    print("=" * 50)
    
    processor = NEPESEnhancedProcessor()
    
    # Test with provided files
    bump_file = "test/D97127.09"
    dummy_file = "test/009.NNS157-09-E4"
    output_file = "test/nepes_enhanced_output.tsk"
    
    if os.path.exists(bump_file) and os.path.exists(dummy_file):
        success = processor.process_nepes_enhanced(bump_file, dummy_file, output_file)
        
        if success:
            stats = processor.get_processing_stats()
            print(f"\n📊 Final Statistics:")
            for key, value in stats.items():
                print(f"   {key}: {value}")
        
        return success
    else:
        print(f"❌ Test files not found!")
        print(f"   Bump file: {bump_file} - {'✅' if os.path.exists(bump_file) else '❌'}")
        print(f"   Dummy file: {dummy_file} - {'✅' if os.path.exists(dummy_file) else '❌'}")
        return False


if __name__ == "__main__":
    main()
