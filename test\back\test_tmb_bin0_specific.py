#!/usr/bin/env python3
"""
专门测试TMB处理器对bin0的处理
创建模拟数据来验证bin0的正确处理
"""

from tmb_processor import TMBProcessor
from tsk_map_processor import TSKMapProcessor


def create_mock_processor_with_bin0():
    """创建包含bin0数据的模拟处理器"""
    processor = TSKMapProcessor()
    
    # 设置基本参数
    processor.rowsize = 3
    processor.columnsize = 5
    
    # 创建模拟的map_data
    # [row][col][data_type] where data_type: 0 = category, 1 = color
    processor.map_data = [
        # Row 0: 包含bin0和其他bin
        [
            [0, 4],   # bin0, pass (green) - 这是关键测试点
            [1, 4],   # bin1, pass
            [0, 0],   # 未测试区域
            [2, 3],   # bin2, fail
            [0, 4]    # bin0, pass - 另一个bin0
        ],
        # Row 1: 混合数据
        [
            [1, 4],   # bin1, pass
            [0, 3],   # bin0, fail - bin0也可能fail
            [3, 3],   # bin3, fail
            [0, 0],   # 未测试区域
            [1, 4]    # bin1, pass
        ],
        # Row 2: 更多数据
        [
            [0, 0],   # 未测试区域
            [2, 3],   # bin2, fail
            [0, 4],   # bin0, pass
            [0, 0],   # 未测试区域
            [4, 4]    # bin4, pass
        ]
    ]
    
    return processor


def test_tmb_bin0_specific():
    """专门测试bin0的处理"""
    print("🧪 专门测试TMB处理器的bin0处理")
    print("=" * 50)
    
    # 创建模拟处理器
    tsk_processor = create_mock_processor_with_bin0()
    
    print("📊 模拟数据分析:")
    print("   尺寸: 3 × 5")
    
    # 分析模拟数据
    category_counts = {}
    tested_positions = 0
    untested_positions = 0
    
    for row in range(tsk_processor.rowsize):
        for col in range(tsk_processor.columnsize):
            category = tsk_processor.map_data[row][col][0]
            color = tsk_processor.map_data[row][col][1]
            
            if color != 0:  # 有测试数据
                tested_positions += 1
                category_counts[category] = category_counts.get(category, 0) + 1
            else:  # 未测试
                untested_positions += 1
    
    print(f"   总位置: {tsk_processor.rowsize * tsk_processor.columnsize}")
    print(f"   已测试: {tested_positions}")
    print(f"   未测试: {untested_positions}")
    
    print(f"\n📋 预期bin分布:")
    for cat in sorted(category_counts.keys()):
        count = category_counts[cat]
        print(f"   Bin {cat}: {count} dies")
    
    # 特别关注bin0
    expected_bin0 = category_counts.get(0, 0)
    print(f"\n🎯 预期bin0数量: {expected_bin0}")
    
    # 创建TMB处理器
    tmb_processor = TMBProcessor()
    tmb_processor.tsk_processor = tsk_processor
    
    # 生成下半部分统计
    print(f"\n🔧 生成TMB统计:")
    lower_part = tmb_processor.generate_lower_part()
    lower_lines = lower_part.split('\n')
    
    # 解析TMB统计
    tmb_category_counts = {}
    for line in lower_lines:
        if line.startswith('Cat '):
            parts = line.split(': ')
            if len(parts) == 2:
                cat_str = parts[0].replace('Cat ', '')
                cat_num = int(cat_str)
                count = int(parts[1])
                if count > 0:
                    tmb_category_counts[cat_num] = count
    
    print(f"   TMB统计结果:")
    for cat in sorted(tmb_category_counts.keys()):
        count = tmb_category_counts[cat]
        print(f"   Bin {cat}: {count} dies")
    
    # 验证bin0
    tmb_bin0 = tmb_category_counts.get(0, 0)
    print(f"\n🎯 TMB bin0数量: {tmb_bin0}")
    
    # 生成上半部分可视化
    print(f"\n📄 生成上半部分可视化:")
    upper_part = tmb_processor.generate_upper_part()
    upper_lines = upper_part.split('\n')
    
    print("   Map可视化:")
    for i, line in enumerate(upper_lines[:10]):  # 只显示前10行
        print(f"   {line}")
    
    # 验证结果
    bin0_correct = (expected_bin0 == tmb_bin0)
    all_bins_correct = True
    
    for cat, expected_count in category_counts.items():
        tmb_count = tmb_category_counts.get(cat, 0)
        if expected_count != tmb_count:
            print(f"   ❌ Bin {cat}: 预期={expected_count}, TMB={tmb_count}")
            all_bins_correct = False
    
    print(f"\n🎉 测试结果:")
    print(f"   bin0正确: {'✅' if bin0_correct else '❌'}")
    print(f"   所有bin正确: {'✅' if all_bins_correct else '❌'}")
    
    if bin0_correct and all_bins_correct:
        print(f"   ✅ TMB处理器正确处理了bin0和所有其他bin")
        print(f"   ✅ bin0不再被强制设为0")
        print(f"   ✅ 修复成功！")
    else:
        print(f"   ❌ 仍有问题需要解决")
    
    return bin0_correct and all_bins_correct


def main():
    """主函数"""
    print("TMB处理器bin0专项测试")
    print("验证bin0的正确统计和显示")
    print("=" * 60)
    
    success = test_tmb_bin0_specific()
    
    if success:
        print(f"\n🎊 专项测试通过！bin0处理完全正确")
    else:
        print(f"\n💥 专项测试失败，需要进一步调试")
    
    return success


if __name__ == "__main__":
    main()
