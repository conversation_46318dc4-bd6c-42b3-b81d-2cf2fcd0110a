#!/usr/bin/env python3
"""
Check the last position (304, 7) in bump map
"""

import re
import os

def check_last_position():
    os.chdir('test')
    
    # Read bump map
    with open('D97127.09', 'r', encoding='utf-8', errors='ignore') as f:
        content = f.read()
    
    row_data_lines = re.findall(r'RowData:(.*)', content)
    all_data = []
    
    for line in row_data_lines:
        values = [v.strip() for v in line.strip().split() if v.strip()]
        all_data.append(values)
    
    # Check position (304, 7)
    x, y = 304, 7
    print(f"检查位置 ({x}, {y}):")
    print(f"Bump map尺寸: {len(all_data)} × {len(all_data[0]) if all_data else 0}")
    
    if y < len(all_data) and x < len(all_data[y]):
        value = all_data[y][x]
        print(f"位置 ({x}, {y}) 的值: \"{value}\"")
        
        if value == '__':
            print("✅ 这是一个'__'位置，应该保持不变")
            print("❌ 但由于TSK文件边界问题，无法访问这个位置")
        else:
            print(f"这是一个\"{value}\"位置")
    else:
        print(f"❌ 位置 ({x}, {y}) 超出bump map范围")
    
    # Count all "__" positions
    underscore_count = 0
    for row in all_data:
        for val in row:
            if val == '__':
                underscore_count += 1
    
    print(f"\n总计'__'位置数: {underscore_count}")
    
    # Check if the last position is "__"
    last_row = all_data[-1]
    last_value = last_row[-1]
    print(f"最后一个位置的值: \"{last_value}\"")

if __name__ == "__main__":
    check_last_position()
