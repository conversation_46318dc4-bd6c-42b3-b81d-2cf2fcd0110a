# 数据库集成方案 - Excel配置替换为数据库驱动

## 概述

基于对当前项目Excel加载部分的分析，我为你设计了一套完整的数据库对接方案，可以替换现有的Excel配置系统，提供更强大的自动筛选和配置管理功能。

## 当前Excel系统分析

### 现有结构
```
config_reader.py - Excel配置读取器
├── 读取单元格数据 (A2, D2, F2, D5, F5, H5, J5, L5)
├── 读取bin映射 (A6:B列，bin号码->bin名称)
└── 提供统一的配置接口
```

### 现有数据结构
- **测试程序信息**: A2 (test_program_name)
- **设备信息**: D2 (device_name), F2 (vendor_name)
- **测试配置**: D5 (tester_name), F5 (probe_card_no), H5 (test_flow), J5 (wafer_size), L5 (operator_name)
- **Bin映射**: A6:B列 (bin_number -> bin_name)

## 数据库方案设计

### 1. 数据库架构

#### 主要表结构

**test_configurations** - 主配置表
```sql
CREATE TABLE test_configurations (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    config_name VARCHAR(100) NOT NULL UNIQUE,
    test_program_name VARCHAR(200),
    device_name VARCHAR(100),
    vendor_name VARCHAR(100),
    tester_name VARCHAR(100),
    probe_card_no VARCHAR(100),
    test_flow VARCHAR(50),
    wafer_size VARCHAR(50),
    operator_name VARCHAR(100),
    version VARCHAR(20) DEFAULT '1.0',
    is_active BOOLEAN DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

**bin_mappings** - Bin映射表
```sql
CREATE TABLE bin_mappings (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    config_id INTEGER,
    bin_number INTEGER NOT NULL,
    bin_name VARCHAR(200),
    bin_description TEXT,
    is_active BOOLEAN DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (config_id) REFERENCES test_configurations (id),
    UNIQUE(config_id, bin_number)
);
```

**config_filters** - 自动筛选规则表
```sql
CREATE TABLE config_filters (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    filter_name VARCHAR(100) NOT NULL,
    filter_type VARCHAR(50) NOT NULL,  -- 'device', 'test_flow', 'vendor'等
    filter_value VARCHAR(200) NOT NULL,
    priority INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

**config_usage_log** - 使用日志表
```sql
CREATE TABLE config_usage_log (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    config_id INTEGER,
    used_by VARCHAR(100),
    usage_context TEXT,
    used_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (config_id) REFERENCES test_configurations (id)
);
```

### 2. 核心组件

#### DatabaseConfigReader类
- **功能**: 替换原有的ConfigReader，提供相同的接口
- **特性**: 
  - 完全兼容现有接口
  - 支持自动配置选择
  - 提供使用统计和日志
  - 支持配置版本管理

#### ConfigDatabaseManager类
- **功能**: 数据库配置管理工具
- **特性**:
  - 配置的增删改查
  - Excel到数据库的迁移
  - JSON导入导出
  - 使用统计分析

#### 增强的TMBProcessor
- **功能**: 支持数据库配置的TMB处理器
- **特性**:
  - 向后兼容Excel配置
  - 支持数据库配置加载
  - 自动配置选择

## 主要优势

### 1. 自动筛选功能
```python
# 自动根据条件选择配置
filters = {
    'device_name': 'ICNA3509WAA-P1-P',
    'test_flow': 'CP1',
    'vendor_name': 'CHANGHOU'
}
db_reader.load_config_auto(filters)
```

### 2. 配置管理
```python
# 列出所有配置
configs = manager.list_configs_detailed()

# 导出配置到JSON
manager.export_config_to_json(config_id, "backup.json")

# 从JSON导入配置
manager.import_config_from_json("backup.json")
```

### 3. 使用统计
```python
# 获取使用统计
stats = manager.get_usage_statistics()
print("最常用配置:", stats['most_used'])
print("最近使用:", stats['recent_usage'])
```

### 4. 版本管理
- 配置版本控制
- 更新时间跟踪
- 软删除支持
- 使用历史记录

## 实施方案

### 阶段1: 数据迁移
```bash
# 迁移现有Excel配置到数据库
python config_database_manager.py migrate test/CP1_program_bin.xlsx "CP1_Test_Config"
```

### 阶段2: 渐进式替换
```python
# 现有代码保持不变
tmb_processor = TMBProcessor()
config_reader = ConfigReader()
config_reader.read_config_file("config.xlsx")
tmb_processor.set_config_reader(config_reader)

# 新代码使用数据库
tmb_processor = TMBProcessor(use_database=True)
tmb_processor.load_config_from_database(auto_filters={'test_flow': 'CP1'})
```

### 阶段3: 完全替换
```python
# 完全使用数据库配置
tmb_processor = TMBProcessor(use_database=True)
tmb_processor.load_config_from_database(config_name="CP1_Test_Config")
```

## 技术特性

### 1. 向后兼容
- 保持现有接口不变
- 支持Excel和数据库双模式
- 渐进式迁移

### 2. 性能优化
- 数据库索引优化
- 连接池管理
- 缓存机制

### 3. 数据安全
- 事务支持
- 数据备份
- 软删除机制

### 4. 扩展性
- 支持多种数据库 (SQLite, MySQL, PostgreSQL)
- 插件化架构
- RESTful API支持

## 使用示例

### 基本使用
```python
# 初始化数据库配置读取器
db_reader = DatabaseConfigReader("config.db")

# 自动选择配置
filters = {'device_name': 'ICNA3509WAA-P1-P', 'test_flow': 'CP1'}
db_reader.load_config_auto(filters)

# 使用配置
print(f"设备名称: {db_reader.get_device_name()}")
print(f"测试流程: {db_reader.get_test_flow()}")
```

### TMB处理器集成
```python
# 使用数据库配置的TMB处理器
tmb_processor = TMBProcessor(use_database=True)

# 自动加载配置
auto_filters = {'test_flow': 'CP1', 'device_name': 'ICNA3509WAA-P1-P'}
tmb_processor.load_config_from_database(auto_filters=auto_filters)

# 处理MAP文件
tmb_output = tmb_processor.process_map_to_tmb("test.map")
```

### 配置管理
```bash
# 命令行工具
python config_database_manager.py list                    # 列出配置
python config_database_manager.py export 1 config.json   # 导出配置
python config_database_manager.py import config.json     # 导入配置
python config_database_manager.py stats                  # 使用统计
```

## 部署建议

### 1. 开发环境
- 使用SQLite数据库 (config_database.db)
- 本地文件存储
- 简单部署

### 2. 生产环境
- 使用MySQL/PostgreSQL
- 网络数据库
- 集中管理

### 3. 混合环境
- 本地SQLite缓存
- 远程数据库同步
- 离线支持

## 测试验证

创建了完整的测试套件 `test/test_database_integration.py`:

1. **数据迁移测试** - 验证Excel到数据库的迁移
2. **自动选择测试** - 验证基于条件的自动配置选择
3. **TMB集成测试** - 验证数据库配置与TMB生成的集成
4. **配置管理测试** - 验证配置的增删改查操作
5. **使用统计测试** - 验证使用统计功能
6. **性能对比测试** - 对比Excel和数据库的加载性能

## 总结

这套数据库集成方案提供了：

✅ **完全向后兼容** - 不破坏现有代码
✅ **自动筛选功能** - 根据条件自动选择配置
✅ **配置管理** - 完整的配置生命周期管理
✅ **使用统计** - 配置使用情况分析
✅ **性能提升** - 数据库查询比Excel读取更快
✅ **扩展性** - 支持更复杂的配置需求
✅ **数据安全** - 事务支持和备份机制

建议采用渐进式实施，先迁移数据，然后逐步替换使用方式，最终实现完全的数据库驱动配置管理。
