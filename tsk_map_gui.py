"""
AB MAP File Processor GUI
Enhanced GUI for Amap, Bmap, and AB map compare functionality
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import os
from datetime import datetime
from tsk_map_processor import TSKMapProcessor
from excel_output import create_excel_output


class ABMapGUI:
    """Main GUI class for AB MAP file processing with three modes"""

    def __init__(self, root):
        self.root = root

        # Only set window properties if root is a Tk window (not a Frame)
        if hasattr(root, 'title'):
            self.root.title("AB MAP Tool - Amap | Bmap | AB Compare")
            self.root.geometry("850x800")
            self.root.resizable(True, True)
            self.root.minsize(800, 750)  # Set minimum window size with extra space

        # Variables for different modes
        self.mode_selection = tk.StringVar(value="Amap")
        self.config_file_path = tk.StringVar()

        # File paths for different modes
        self.amap_file_path = tk.StringVar()
        self.bmap_file_path = tk.StringVar()
        self.compare_amap_path = tk.StringVar()
        self.compare_bmap_path = tk.StringVar()

        # Common settings
        self.rotation_angle = tk.IntVar(value=0)
        self.filter_empty = tk.BooleanVar(value=True)
        self.output_folder_path = tk.StringVar()

        # Initialize processors and config reader
        self.amap_processor = None
        self.bmap_processor = None
        self.config_reader = None

        # Memory management
        self.last_memory_usage = 0.0
        self.processing_count = 0

        # Create GUI elements
        self.create_widgets()

        # Center window
        self.center_window()

        # Set up window close protocol (only for Tk windows, not Frames)
        if hasattr(self.root, 'protocol'):
            self.root.protocol("WM_DELETE_WINDOW", self.on_window_close)
    
    def center_window(self):
        """Center the window on screen (only for Tk windows, not Frames)"""
        if hasattr(self.root, 'geometry'):
            self.root.update_idletasks()
            width = self.root.winfo_width()
            height = self.root.winfo_height()
            x = (self.root.winfo_screenwidth() // 2) - (width // 2)
            y = (self.root.winfo_screenheight() // 2) - (height // 2)
            self.root.geometry(f'{width}x{height}+{x}+{y}')
    
    def create_widgets(self):
        """Create and layout GUI widgets"""

        # Main frame with extra padding
        main_frame = ttk.Frame(self.root, padding="15")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # Configure grid weights
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)

        # Configuration file selection section
        config_frame = ttk.LabelFrame(main_frame, text="Configuration File (Optional)", padding="12")
        config_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 15))
        config_frame.columnconfigure(1, weight=1)

        ttk.Label(config_frame, text="Config Excel File:").grid(row=0, column=0, sticky=tk.W, padx=(0, 8))

        config_entry = ttk.Entry(config_frame, textvariable=self.config_file_path, width=60)
        config_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 8))

        ttk.Button(config_frame, text="Browse...", command=self.browse_config_file).grid(row=0, column=2)

        # Mode selection section
        mode_frame = ttk.LabelFrame(main_frame, text="Processing Mode", padding="12")
        mode_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 15))

        ttk.Radiobutton(mode_frame, text="Amap (Single A file)", variable=self.mode_selection,
                       value="Amap", command=self.on_mode_change).grid(row=0, column=0, sticky=tk.W, padx=(0, 25))
        ttk.Radiobutton(mode_frame, text="Bmap (Single B file)", variable=self.mode_selection,
                       value="Bmap", command=self.on_mode_change).grid(row=0, column=1, sticky=tk.W, padx=(0, 25))
        ttk.Radiobutton(mode_frame, text="AB Compare (A & B files)", variable=self.mode_selection,
                       value="AB_Compare", command=self.on_mode_change).grid(row=0, column=2, sticky=tk.W)

        # File selection section (dynamic based on mode)
        self.file_frame = ttk.LabelFrame(main_frame, text="File Selection", padding="12")
        self.file_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 15))
        self.file_frame.columnconfigure(1, weight=1)

        # Create file selection widgets (will be updated based on mode)
        self.create_file_selection_widgets()
        
        # Rotation angle selection section
        rotation_frame = ttk.LabelFrame(main_frame, text="Rotation Angle", padding="12")
        rotation_frame.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 15))

        ttk.Radiobutton(rotation_frame, text="0° (Original)", variable=self.rotation_angle,
                       value=0).grid(row=0, column=0, sticky=tk.W, padx=(0, 20))
        ttk.Radiobutton(rotation_frame, text="90° (Clockwise)", variable=self.rotation_angle,
                       value=90).grid(row=0, column=1, sticky=tk.W, padx=(0, 20))
        ttk.Radiobutton(rotation_frame, text="180°", variable=self.rotation_angle,
                       value=180).grid(row=1, column=0, sticky=tk.W, padx=(0, 20))
        ttk.Radiobutton(rotation_frame, text="270° (Counter-clockwise)", variable=self.rotation_angle,
                       value=270).grid(row=1, column=1, sticky=tk.W)

        # Data filtering options
        filter_frame = ttk.LabelFrame(main_frame, text="Output Options", padding="12")
        filter_frame.grid(row=4, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 15))
        filter_frame.columnconfigure(1, weight=1)

        tk.Checkbutton(filter_frame, text="Filter empty areas (recommended for large files)",
                      variable=self.filter_empty).grid(row=0, column=0, columnspan=3, sticky=tk.W, pady=(0, 8))

        # Output folder selection
        ttk.Label(filter_frame, text="Output Folder:").grid(row=1, column=0, sticky=tk.W, padx=(0, 8))

        output_entry = ttk.Entry(filter_frame, textvariable=self.output_folder_path, width=50)
        output_entry.grid(row=1, column=1, sticky=(tk.W, tk.E), padx=(0, 8))

        ttk.Button(filter_frame, text="Browse...", command=self.browse_output_folder).grid(row=1, column=2)
        
        # File information section
        info_frame = ttk.LabelFrame(main_frame, text="File Information", padding="12")
        info_frame.grid(row=5, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 15))
        info_frame.columnconfigure(1, weight=1)

        self.info_text = tk.Text(info_frame, height=10, width=80, state=tk.DISABLED)
        self.info_text.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E))

        # Scrollbar for info text
        info_scrollbar = ttk.Scrollbar(info_frame, orient=tk.VERTICAL, command=self.info_text.yview)
        info_scrollbar.grid(row=0, column=2, sticky=(tk.N, tk.S))
        self.info_text.configure(yscrollcommand=info_scrollbar.set)

        # Add mousewheel support for File Information area (entire area)
        def _on_info_mousewheel(event):
            self.info_text.yview_scroll(int(-1*(event.delta/120)), "units")

        # Bind mousewheel to the entire info frame and all its children
        def bind_mousewheel_recursive(widget):
            widget.bind("<MouseWheel>", _on_info_mousewheel)
            for child in widget.winfo_children():
                bind_mousewheel_recursive(child)

        bind_mousewheel_recursive(info_frame)

        # Buttons section
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=6, column=0, columnspan=2, pady=(15, 0))

        ttk.Button(button_frame, text="PROCESS", command=self.process_files,
                  style="Accent.TButton").pack(side=tk.LEFT, padx=(0, 15))
        ttk.Button(button_frame, text="Clear Memory", command=self.clear_memory).pack(side=tk.LEFT, padx=(0, 15))
        ttk.Button(button_frame, text="← Back", command=self.return_to_selector).pack(side=tk.LEFT, padx=(0, 15))
        ttk.Button(button_frame, text="Exit", command=self.exit_application).pack(side=tk.LEFT)

        # Status bar
        self.status_var = tk.StringVar(value="Ready - Select processing mode and files")
        status_bar = ttk.Label(main_frame, textvariable=self.status_var, relief=tk.SUNKEN)
        status_bar.grid(row=7, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(15, 0))
    
    def create_file_selection_widgets(self):
        """Create file selection widgets based on current mode"""
        # Clear existing widgets
        for widget in self.file_frame.winfo_children():
            widget.destroy()

        mode = self.mode_selection.get()

        if mode == "Amap":
            # Single Amap file selection
            ttk.Label(self.file_frame, text="Amap File:").grid(row=0, column=0, sticky=tk.W, padx=(0, 8))
            amap_entry = ttk.Entry(self.file_frame, textvariable=self.amap_file_path, width=60)
            amap_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 8))
            ttk.Button(self.file_frame, text="Browse...",
                      command=lambda: self.browse_file("Amap")).grid(row=0, column=2)

        elif mode == "Bmap":
            # Single Bmap file selection
            ttk.Label(self.file_frame, text="Bmap File:").grid(row=0, column=0, sticky=tk.W, padx=(0, 8))
            bmap_entry = ttk.Entry(self.file_frame, textvariable=self.bmap_file_path, width=60)
            bmap_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 8))
            ttk.Button(self.file_frame, text="Browse...",
                      command=lambda: self.browse_file("Bmap")).grid(row=0, column=2)

        elif mode == "AB_Compare":
            # Dual file selection for comparison
            ttk.Label(self.file_frame, text="Amap File:").grid(row=0, column=0, sticky=tk.W, padx=(0, 8))
            amap_compare_entry = ttk.Entry(self.file_frame, textvariable=self.compare_amap_path, width=60)
            amap_compare_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 8))
            ttk.Button(self.file_frame, text="Browse...",
                      command=lambda: self.browse_file("Compare_Amap")).grid(row=0, column=2)

            ttk.Label(self.file_frame, text="Bmap File:").grid(row=1, column=0, sticky=tk.W, padx=(0, 8), pady=(8, 0))
            bmap_compare_entry = ttk.Entry(self.file_frame, textvariable=self.compare_bmap_path, width=60)
            bmap_compare_entry.grid(row=1, column=1, sticky=(tk.W, tk.E), padx=(0, 8), pady=(8, 0))
            ttk.Button(self.file_frame, text="Browse...",
                      command=lambda: self.browse_file("Compare_Bmap")).grid(row=1, column=2, pady=(8, 0))

    def on_mode_change(self):
        """Handle mode selection change"""
        self.create_file_selection_widgets()
        mode = self.mode_selection.get()
        if mode == "Amap":
            self.status_var.set("Amap mode - Select single Amap file")
        elif mode == "Bmap":
            self.status_var.set("Bmap mode - Select single Bmap file")
        elif mode == "AB_Compare":
            self.status_var.set("AB Compare mode - Select both Amap and Bmap files")

        # Clear file info when mode changes
        self.clear_info_text()

    def browse_config_file(self):
        """Open file dialog to select configuration Excel file"""
        filename = filedialog.askopenfilename(
            title="Select Configuration Excel File",
            filetypes=[
                ("Excel files", "*.xlsx"),
                ("Excel files", "*.xls"),
                ("All files", "*.*")
            ]
        )
        if filename:
            self.config_file_path.set(filename)
            self.status_var.set(f"Configuration file selected: {os.path.basename(filename)}")

    def browse_output_folder(self):
        """Open folder dialog to select output directory"""
        folder = filedialog.askdirectory(
            title="Select Output Folder"
        )
        if folder:
            self.output_folder_path.set(folder)
            self.status_var.set(f"Output folder selected: {folder}")

    def browse_file(self, file_type):
        """Open file dialog to select TSK/MAP file based on type"""
        if file_type == "Amap":
            title = "Select Amap File"
        elif file_type == "Bmap":
            title = "Select Bmap File"
        elif file_type == "Compare_Amap":
            title = "Select Amap File for Comparison"
        elif file_type == "Compare_Bmap":
            title = "Select Bmap File for Comparison"
        else:
            title = "Select TSK/MAP File"

        filename = filedialog.askopenfilename(
            title=title,
            filetypes=[
                ("All supported files", "*.*"),
                ("TSK files", "*.tsk"),
                ("MAP files", "*.map")
            ]
        )
        if filename:
            # Set the appropriate variable based on file type
            if file_type == "Amap":
                self.amap_file_path.set(filename)
            elif file_type == "Bmap":
                self.bmap_file_path.set(filename)
            elif file_type == "Compare_Amap":
                self.compare_amap_path.set(filename)
            elif file_type == "Compare_Bmap":
                self.compare_bmap_path.set(filename)

            self.update_file_info()
            self.status_var.set(f"{file_type} file selected: {os.path.basename(filename)}")
    
    def update_file_info(self):
        """Update file information display based on current mode"""
        mode = self.mode_selection.get()
        info_text = ""

        try:
            if mode == "Amap":
                filepath = self.amap_file_path.get()
                if filepath and os.path.exists(filepath):
                    info_text += self.get_single_file_info(filepath, "Amap")

            elif mode == "Bmap":
                filepath = self.bmap_file_path.get()
                if filepath and os.path.exists(filepath):
                    info_text += self.get_single_file_info(filepath, "Bmap")

            elif mode == "AB_Compare":
                amap_path = self.compare_amap_path.get()
                bmap_path = self.compare_bmap_path.get()

                if amap_path and os.path.exists(amap_path):
                    info_text += "=== AMAP FILE ===\n"
                    info_text += self.get_single_file_info(amap_path, "Amap")
                    info_text += "\n"

                if bmap_path and os.path.exists(bmap_path):
                    info_text += "=== BMAP FILE ===\n"
                    info_text += self.get_single_file_info(bmap_path, "Bmap")

                if amap_path and bmap_path and os.path.exists(amap_path) and os.path.exists(bmap_path):
                    info_text += "\n=== COMPARISON READY ===\n"
                    info_text += "Both files loaded. Ready for AB comparison processing."

            if info_text:
                self.set_info_text(info_text)
            else:
                self.clear_info_text()

        except Exception as e:
            self.set_info_text(f"Error: {str(e)}")
            self.status_var.set("Error processing file")

    def get_single_file_info(self, filepath, file_type):
        """Get information for a single file"""
        try:
            from tsk_map_processor import TSKMapProcessor
            temp_processor = TSKMapProcessor()

            if temp_processor.read_file(filepath):
                if temp_processor.parse_file_header():
                    info = temp_processor.get_file_info()

                    info_text = f"File: {os.path.basename(filepath)}\n"
                    info_text += f"Type: {file_type}\n"
                    info_text += f"Size: {len(temp_processor.filearray)} bytes\n"
                    info_text += f"Version: {info['version']}\n"
                    info_text += f"Dimensions: {info['columnsize']} x {info['rowsize']}\n"
                    info_text += f"Reference Die: ({info['RefdieX']}, {info['RefdieY']})\n"
                    info_text += f"Test Result Start: {info['TestResultStartPos']}\n"
                    info_text += f"Category Start: {info['TestResultCategory']}\n"

                    # Add Excel compatibility information
                    if not info['excel_compatible']:
                        info_text += "\n⚠️ EXCEL COMPATIBILITY WARNINGS:\n"
                        if info['exceeds_excel_cols']:
                            info_text += f"• Columns ({info['columnsize']}) exceed Excel limit (16384)\n"
                        if info['exceeds_excel_rows']:
                            info_text += f"• Rows ({info['rowsize']}) exceed Excel limit (1048576)\n"
                        info_text += "• Consider using 'Filter empty areas' option"
                    else:
                        info_text += "\n✓ Excel compatible"

                    return info_text
                else:
                    return f"Error: Could not parse {file_type} file header"
            else:
                return f"Error: Could not read {file_type} file"

        except Exception as e:
            return f"Error processing {file_type} file: {str(e)}"
    
    def set_info_text(self, text):
        """Set text in info display"""
        self.info_text.config(state=tk.NORMAL)
        self.info_text.delete(1.0, tk.END)
        self.info_text.insert(1.0, text)
        self.info_text.config(state=tk.DISABLED)
    
    def clear_info_text(self):
        """Clear info display"""
        self.info_text.config(state=tk.NORMAL)
        self.info_text.delete(1.0, tk.END)
        self.info_text.config(state=tk.DISABLED)

    def clear_memory(self):
        """Clear all processors and free memory"""
        try:
            memory_freed = 0.0
            processor_count = 0

            # Clear processors
            if self.amap_processor:
                memory_freed += self.amap_processor.get_memory_usage_mb()
                self.amap_processor.clear_memory()
                self.amap_processor = None
                processor_count += 1

            if self.bmap_processor:
                memory_freed += self.bmap_processor.get_memory_usage_mb()
                self.bmap_processor.clear_memory()
                self.bmap_processor = None
                processor_count += 1

            # Clear config reader
            self.config_reader = None

            # Update memory tracking
            self.last_memory_usage = 0.0

            # Always provide feedback to user
            if memory_freed > 0:
                print(f"AB Map Tool - Total memory cleared: ~{memory_freed:.1f} MB from {processor_count} processors")
                self.status_var.set(f"Memory cleared: {memory_freed:.1f} MB freed from {processor_count} processors")
            else:
                print("AB Map Tool - Memory cleared (no active processors)")
                self.status_var.set("Memory cleared - no active processors")

        except Exception as e:
            print(f"Warning: Error during AB Map memory cleanup: {e}")
            self.status_var.set("Memory cleanup completed with warnings")

    def auto_memory_check(self):
        """Automatically check and manage memory usage"""
        try:
            current_memory = 0.0
            processor_count = 0

            if self.amap_processor:
                current_memory += self.amap_processor.get_memory_usage_mb()
                processor_count += 1
            if self.bmap_processor:
                current_memory += self.bmap_processor.get_memory_usage_mb()
                processor_count += 1

            # Auto-clear if memory usage is high (>500MB) and processing count > 3
            if current_memory > 500 and self.processing_count > 3:
                print(f"AB Map Tool - Auto-clearing memory: {current_memory:.1f} MB in use")

                # Clear memory
                self.clear_memory()

                # Show auto-cleanup popup
                self.show_auto_cleanup_popup(current_memory, processor_count)

                return True

            return False

        except Exception as e:
            print(f"Warning: Error in AB Map auto memory check: {e}")
            return False

    def show_auto_cleanup_popup(self, memory_freed: float, processor_count: int):
        """Show popup for automatic memory cleanup"""
        try:
            # Create popup window
            popup = tk.Toplevel(self.root)
            popup.title("Auto Memory Cleanup")
            popup.geometry("380x140")
            popup.resizable(False, False)

            # Center popup on parent window
            popup.transient(self.root)

            # Center popup
            x = self.root.winfo_x() + (self.root.winfo_width() // 2) - 190
            y = self.root.winfo_y() + (self.root.winfo_height() // 2) - 70
            popup.geometry(f"380x140+{x}+{y}")

            # Create content
            main_frame = ttk.Frame(popup, padding="20")
            main_frame.pack(fill=tk.BOTH, expand=True)

            # Icon and message
            message = f"🔄 Auto Memory Cleanup Triggered\n\n{memory_freed:.1f} MB freed from {processor_count} processors\n\nMemory usage was high, automatically cleared."

            ttk.Label(main_frame, text=message, justify=tk.CENTER,
                     font=("Arial", 9)).pack(expand=True)

            # Auto-close after 2 seconds
            popup.after(2000, popup.destroy)

            # Allow manual close with Escape key
            popup.bind('<Escape>', lambda e: popup.destroy())
            popup.focus_set()

        except Exception as e:
            print(f"Warning: Error showing auto cleanup popup: {e}")
    
    def process_files(self):
        """Process files based on selected mode and create Excel output"""
        mode = self.mode_selection.get()

        # Load configuration file if provided
        if self.config_file_path.get():
            from config_reader import ConfigReader
            self.config_reader = ConfigReader()
            if not self.config_reader.read_config_file(self.config_file_path.get()):
                messagebox.showwarning("Warning", "Could not load configuration file. Proceeding without bin name mapping.")
                self.config_reader = None

        # Process based on mode
        if mode == "Amap":
            self.process_single_file("Amap", self.amap_file_path.get())
        elif mode == "Bmap":
            self.process_single_file("Bmap", self.bmap_file_path.get())
        elif mode == "AB_Compare":
            self.process_ab_compare()

    def process_single_file(self, file_type, filepath):
        """Process a single file (Amap or Bmap)"""
        if not filepath:
            messagebox.showerror("Error", f"Please select a {file_type} file first.")
            return

        if not os.path.exists(filepath):
            messagebox.showerror("Error", f"Selected {file_type} file does not exist.")
            return

        try:
            # Auto memory check before processing
            self.auto_memory_check()

            self.status_var.set(f"Processing {file_type} file...")
            self.root.update()

            # Initialize processor
            from tsk_map_processor import TSKMapProcessor
            processor = TSKMapProcessor()

            # Clear old processor if exists
            if file_type == "Amap" and self.amap_processor:
                self.amap_processor.clear_memory()
            elif file_type == "Bmap" and self.bmap_processor:
                self.bmap_processor.clear_memory()

            # Read and parse file
            if not processor.read_file(filepath):
                messagebox.showerror("Error", f"Could not read the selected {file_type} file.")
                self.status_var.set("Error reading file")
                return

            if not processor.parse_file_header():
                messagebox.showerror("Error", f"Could not parse {file_type} file header. Invalid file format?")
                self.status_var.set("Error parsing file")
                return

            # Process die data
            if not processor.process_die_data():
                messagebox.showerror("Error", f"Could not process {file_type} die data.")
                self.status_var.set("Error processing data")
                return

            # Generate output filename
            base_name = os.path.splitext(os.path.basename(filepath))[0]
            rotation = self.rotation_angle.get()
            filename = f"{base_name}_{file_type}_R{rotation}.xlsx"

            # Use selected output folder or current directory
            output_folder = self.output_folder_path.get()
            if output_folder and os.path.exists(output_folder):
                output_filename = os.path.join(output_folder, filename)
            else:
                output_filename = filename
            
            # Create Excel output
            self.status_var.set(f"Creating {file_type} Excel output...")
            self.root.update()

            # Use filtered data if option is selected
            use_filter = self.filter_empty.get()

            if self.create_excel_output_filtered(file_type, rotation, output_filename,
                                               filepath, use_filter, processor):
                success_msg = f"{file_type} Excel file created successfully:\n{output_filename}"
                if use_filter:
                    # Show active data bounds info
                    info = processor.get_file_info()
                    if 'active_bounds' in info:
                        bounds = info['active_bounds']
                        success_msg += f"\n\nFiltered to active area:"
                        success_msg += f"\n{bounds['active_rows']} x {bounds['active_cols']} cells"

                # Add config info if loaded
                if self.config_reader and self.config_reader.has_config_loaded():
                    success_msg += f"\n\nConfiguration applied:"
                    success_msg += f"\nTest Program: {self.config_reader.get_test_program_name()}"
                    success_msg += f"\nBin mappings: {len(self.config_reader.get_all_bin_mappings())} bins"

                messagebox.showinfo("Success", success_msg)
                self.status_var.set(f"{file_type} output saved: {output_filename}")

                # Store processor and update memory tracking
                if file_type == "Amap":
                    self.amap_processor = processor
                else:
                    self.bmap_processor = processor

                # Increment processing count and check memory
                self.processing_count += 1
                current_memory = processor.get_memory_usage_mb()
                if current_memory > 0:
                    print(f"Processing #{self.processing_count} complete. Memory usage: {current_memory:.1f} MB")

            else:
                messagebox.showerror("Error", f"Could not create {file_type} Excel output.")
                self.status_var.set("Error creating output")

        except Exception as e:
            messagebox.showerror("Error", f"An error occurred processing {file_type}:\n{str(e)}")
            self.status_var.set("Error occurred")
        finally:
            # Clear file array after processing to save memory (keep map_data for potential reuse)
            if processor and hasattr(processor, 'filearray'):
                processor.filearray = bytearray()
    
    def process_ab_compare(self):
        """Process AB comparison with both Amap and Bmap files"""
        amap_path = self.compare_amap_path.get()
        bmap_path = self.compare_bmap_path.get()

        if not amap_path:
            messagebox.showerror("Error", "Please select an Amap file for comparison.")
            return

        if not bmap_path:
            messagebox.showerror("Error", "Please select a Bmap file for comparison.")
            return

        if not os.path.exists(amap_path):
            messagebox.showerror("Error", "Selected Amap file does not exist.")
            return

        if not os.path.exists(bmap_path):
            messagebox.showerror("Error", "Selected Bmap file does not exist.")
            return

        try:
            # Auto memory check before processing
            self.auto_memory_check()

            self.status_var.set("Processing AB comparison...")
            self.root.update()

            # Clear old processors if they exist
            if self.amap_processor:
                self.amap_processor.clear_memory()
            if self.bmap_processor:
                self.bmap_processor.clear_memory()

            # Initialize processors for both files
            from tsk_map_processor import TSKMapProcessor
            amap_processor = TSKMapProcessor()
            bmap_processor = TSKMapProcessor()

            # Process Amap file
            if not amap_processor.read_file(amap_path):
                messagebox.showerror("Error", "Could not read the Amap file.")
                return
            if not amap_processor.parse_file_header():
                messagebox.showerror("Error", "Could not parse Amap file header.")
                return
            if not amap_processor.process_die_data():
                messagebox.showerror("Error", "Could not process Amap die data.")
                return

            # Process Bmap file
            if not bmap_processor.read_file(bmap_path):
                messagebox.showerror("Error", "Could not read the Bmap file.")
                return
            if not bmap_processor.parse_file_header():
                messagebox.showerror("Error", "Could not parse Bmap file header.")
                return
            if not bmap_processor.process_die_data():
                messagebox.showerror("Error", "Could not process Bmap die data.")
                return

            # Generate comparison output filename with timestamp
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            rotation = self.rotation_angle.get()
            filename = f"AB_map_compare_{timestamp}_R{rotation}.xlsx"

            # Use selected output folder or current directory
            output_folder = self.output_folder_path.get()
            if output_folder and os.path.exists(output_folder):
                output_filename = os.path.join(output_folder, filename)
            else:
                output_filename = filename

            # Create comparison Excel output
            self.status_var.set("Creating AB comparison Excel output...")
            self.root.update()

            if self.create_ab_comparison_excel(amap_processor, bmap_processor,
                                             amap_path, bmap_path, rotation, output_filename):
                success_msg = f"AB Comparison Excel file created successfully:\n{output_filename}"
                success_msg += f"\n\nContains six sheets:"
                success_msg += f"\n• Amap: {os.path.basename(amap_path)}"
                success_msg += f"\n• Bmap: {os.path.basename(bmap_path)}"
                success_msg += f"\n• Summary: Bin difference analysis"
                success_msg += f"\n• Correlation: Bin jump analysis matrix"
                success_msg += f"\n• Map_compare: Position comparison (Different first, Same last)"
                success_msg += f"\n• Map_compare_full: Map format display (R0 style)"

                # Add config info if loaded
                if self.config_reader and self.config_reader.has_config_loaded():
                    success_msg += f"\n\nConfiguration applied:"
                    success_msg += f"\nTest Program: {self.config_reader.get_test_program_name()}"
                    success_msg += f"\nBin mappings: {len(self.config_reader.get_all_bin_mappings())} bins"

                messagebox.showinfo("Success", success_msg)
                self.status_var.set(f"AB comparison saved: {output_filename}")

                # Store processors and update memory tracking
                self.amap_processor = amap_processor
                self.bmap_processor = bmap_processor

                # Increment processing count and show memory usage
                self.processing_count += 1
                total_memory = amap_processor.get_memory_usage_mb() + bmap_processor.get_memory_usage_mb()
                if total_memory > 0:
                    print(f"AB comparison #{self.processing_count} complete. Total memory usage: {total_memory:.1f} MB")

            else:
                messagebox.showerror("Error", "Could not create AB comparison Excel output.")
                self.status_var.set("Error creating comparison output")

        except Exception as e:
            messagebox.showerror("Error", f"An error occurred during AB comparison:\n{str(e)}")
            self.status_var.set("Error occurred")
        finally:
            # Clear file arrays after processing to save memory (keep map_data for potential reuse)
            if 'amap_processor' in locals() and amap_processor and hasattr(amap_processor, 'filearray'):
                amap_processor.filearray = bytearray()
            if 'bmap_processor' in locals() and bmap_processor and hasattr(bmap_processor, 'filearray'):
                bmap_processor.filearray = bytearray()

    def create_excel_output_filtered(self, sheet_name: str, rotation_angle: int,
                                   output_filename: str, file_path: str, use_filter: bool, processor) -> bool:
        """
        Create Excel output with optional filtering for single file
        """
        try:
            from excel_output import ExcelOutputHandler

            excel_handler = ExcelOutputHandler()

            if not excel_handler.create_workbook():
                return False

            # Get data (filtered or full)
            if use_filter:
                rotated_data = processor.get_filtered_rotated_data(rotation_angle, True)
            else:
                rotated_data = processor.get_rotated_data(rotation_angle)

            if not rotated_data:
                print("No data to write to Excel")
                return False

            # Write data to worksheet (pass processor and config_reader for device info)
            if not excel_handler.write_rotated_data(rotated_data, rotation_angle, sheet_name, processor, self.config_reader):
                return False

            # Create setup sheet if file path provided
            if file_path:
                file_paths = {sheet_name: file_path}
                excel_handler.create_setup_sheet(file_paths)

            # Save workbook
            success = excel_handler.save_workbook(output_filename)
            excel_handler.close_workbook()

            return success

        except Exception as e:
            print(f"Error creating filtered Excel output: {e}")
            return False

    def create_ab_comparison_excel(self, amap_processor, bmap_processor,
                                 amap_path, bmap_path, rotation_angle, output_filename) -> bool:
        """
        Create Excel output with both Amap and Bmap sheets for comparison
        Enhanced with Summary, Correlation, and Map_compare analysis sheets
        """
        try:
            from excel_output import ExcelOutputHandler
            from ab_comparison_analyzer import ABComparisonAnalyzer

            excel_handler = ExcelOutputHandler()

            if not excel_handler.create_workbook():
                return False

            use_filter = self.filter_empty.get()

            # Process Amap data
            if use_filter:
                amap_data = amap_processor.get_filtered_rotated_data(rotation_angle, True)
            else:
                amap_data = amap_processor.get_rotated_data(rotation_angle)

            if not amap_data:
                print("No Amap data to write to Excel")
                return False

            # Write Amap data to first sheet
            if not excel_handler.write_rotated_data(amap_data, rotation_angle, "Amap", amap_processor, self.config_reader):
                return False

            # Process Bmap data
            if use_filter:
                bmap_data = bmap_processor.get_filtered_rotated_data(rotation_angle, True)
            else:
                bmap_data = bmap_processor.get_rotated_data(rotation_angle)

            if not bmap_data:
                print("No Bmap data to write to Excel")
                return False

            # Write Bmap data to second sheet
            if not excel_handler.write_rotated_data(bmap_data, rotation_angle, "Bmap", bmap_processor, self.config_reader):
                return False

            # Create advanced comparison analysis
            self.status_var.set("Generating comparison analysis...")
            self.root.update()

            analyzer = ABComparisonAnalyzer()
            analyzer.set_data(amap_processor, bmap_processor, amap_data, bmap_data)

            # Generate Summary sheet
            summary_data = analyzer.generate_summary_analysis()
            if summary_data:
                analyzer.write_summary_sheet(excel_handler.workbook, summary_data)

            # Generate Correlation sheet
            correlation_data = analyzer.generate_correlation_analysis()
            if correlation_data:
                analyzer.write_correlation_sheet(excel_handler.workbook, correlation_data)

            # Generate Map_compare sheet (optimized with different first, same last)
            analyzer.write_map_compare_sheet(excel_handler.workbook)

            # Generate Map_compare_full sheet (map format display)
            analyzer.write_map_compare_full_sheet(excel_handler.workbook)

            # Create setup sheet with both file paths
            file_paths = {
                "Amap": amap_path,
                "Bmap": bmap_path
            }
            excel_handler.create_setup_sheet(file_paths)

            # Save workbook
            success = excel_handler.save_workbook(output_filename)
            excel_handler.close_workbook()

            # Clear analyzer data to free memory
            analyzer.clear_data()

            return success

        except Exception as e:
            print(f"Error creating AB comparison Excel output: {e}")
            return False
        finally:
            # Ensure cleanup even if error occurs
            try:
                if 'analyzer' in locals():
                    analyzer.clear_data()
                if 'excel_handler' in locals():
                    excel_handler.close_workbook()
            except:
                pass

    def show_memory_cleanup_popup(self, memory_freed: float, processor_count: int):
        """Show a temporary popup with memory cleanup information"""
        try:
            # Create popup window
            popup = tk.Toplevel(self.root)
            popup.title("Memory Cleanup")
            popup.geometry("350x120")
            popup.resizable(False, False)

            # Center popup on parent window
            popup.transient(self.root)
            popup.grab_set()

            # Center popup
            x = self.root.winfo_x() + (self.root.winfo_width() // 2) - 175
            y = self.root.winfo_y() + (self.root.winfo_height() // 2) - 60
            popup.geometry(f"350x120+{x}+{y}")

            # Create content
            main_frame = ttk.Frame(popup, padding="20")
            main_frame.pack(fill=tk.BOTH, expand=True)

            # Icon and message
            if memory_freed > 0:
                message = f"✅ Memory Cleanup Complete\n\n{memory_freed:.1f} MB freed from {processor_count} processors"
            elif processor_count > 0:
                message = f"✅ Memory Cleanup Complete\n\nProcessors cleared (no memory to free)"
            else:
                message = "✅ Application Closing\n\nThank you for using AB Map Tool!"

            ttk.Label(main_frame, text=message, justify=tk.CENTER,
                     font=("Arial", 10)).pack(expand=True)

            # Auto-close after 2 seconds
            popup.after(2000, popup.destroy)

            # Allow manual close with Escape key
            popup.bind('<Escape>', lambda e: popup.destroy())
            popup.focus_set()

        except Exception as e:
            print(f"Warning: Error showing cleanup popup: {e}")

    def on_window_close(self):
        """Handle window close event (X button)"""
        self.exit_application()

    def return_to_selector(self):
        """Return to tool selector - placeholder for frame version"""
        # This method will be overridden in the frame version
        # For standalone version, just show a message
        messagebox.showinfo("Return to Selector", "This feature is available when launched from the main application.")

    def exit_application(self):
        """Exit the application with memory cleanup and user feedback"""
        try:
            # Get memory info before clearing
            memory_freed = 0.0
            processor_count = 0

            if self.amap_processor:
                memory_freed += self.amap_processor.get_memory_usage_mb()
                processor_count += 1
            if self.bmap_processor:
                memory_freed += self.bmap_processor.get_memory_usage_mb()
                processor_count += 1

            # Clear all memory before exit
            self.clear_memory()
            print("AB Map Tool - Application memory cleared before exit")

            # Always show cleanup popup when exiting (user feedback)
            self.show_memory_cleanup_popup(memory_freed, processor_count)
            # Wait a moment for popup to show before quitting
            self.root.after(2100, self.root.quit)

        except Exception as e:
            print(f"Warning: Error during AB Map exit cleanup: {e}")
            self.root.quit()


def main():
    """Main function to run the AB Map application"""
    root = tk.Tk()

    # Configure ttk style
    style = ttk.Style()
    style.theme_use('clam')  # Use a modern theme

    ABMapGUI(root)
    root.mainloop()


if __name__ == "__main__":
    main()
