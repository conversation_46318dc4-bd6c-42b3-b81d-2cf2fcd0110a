任务流程：
针对Bump map To Tsk map Tool现在针对工具代码实际验证修改完善:
   -----首先修正我的描述：bump map中的"__"为检索关键字，不是之前提到的"--"，bump map中的"__"内容，在dummy map中对应的x，y位置不修改，维持原dummy map中的二进制数值不变。
   -----现在代码逻辑设计是没有问题的，通过实际数据分析，代码现在执行的map version=2或者3的操作逻辑，修改地方是从第1bit到第16bit。你可以通过对比dummy map的图片（图片1）可以看到。
   
   -----我们需要增加map version=4的修改方式：
   -----根据你已经理解的dummy map和bump map的描述，开始设计生成output tsk map部分(下面的描述是map version=4的方式)：
      ---根据bump map设计的检索内容，如果bump map中对应x，y位置，读出“__”，bump map中的"__"内容，在dummy map中对应的x，y位置不修改，维持原dummy map中的二进制数值不变。（二进制确认可以复用tsk_map_processor.py的process_die_data）
	  ---根据bump map设计的检索内容，如果bump map中对应x，y位置，读出“00”，dummy map中相应x，y位置的二进制修改为（00000000000000000000000000111111）（二进制确认可以复用tsk_map_processor.py的process_die_data）
	  ---根据bump map设计的检索内容，如果bump map中对应x，y位置，读出“XX”除“00”和“__”的其他内容，dummy map中相应x，y位置的二进制修改为（00000000000000000000000000111011）（二进制确认可以复用tsk_map_processor.py的process_die_data）
	  
现在举个例子，你可以根据例子来理解上面的描述：
    1， test内容下的dummy map为:009.NNS157-09-E4,解析的columnsize*rowsize=305*8=2440。TestResultCategory = 15049。
	2， test内容下的bump map为D97127.09,解析的columnsize*rowsize=305*8=2440，X=0,y=0位置为"--"这样dummy map中的第15049 byte位置，二进制读值不变，4bytes(00000000000000000000000000000000)。
	    x=79,y=0解析出"00",这样dummy map中的第15365 byte位置，需要修改为4bytes(00000000000000000000000000111111)
		x=149,y=2解析出“0C”，这样dummy map中的第18085 byte位置，需要修改为4bytes(00000000000000000000000000111011)
	3,  依次遍历整个columnsize*rowsize修改，完成全部dummy map新的生成，最中生成tsk map二进制文件用于后续测试。

上述功能开发注意事项：
     --- 其他两个功能不要破坏，代码功能不要破坏
     --- GUI尽量统一，不要占用太多空间
     --- 复用代码部分，尽量做到代码统一

要求：
1，测试脚本和文件请放到test文件夹
2，生成的md说明文档，请放到temp文件夹
3，程序架构统一，框架统一，代码尽量简洁

测试文件说明：
1，可以使用test文件夹下的D97127.09作为bump map的load
2，可以使用test文件夹下的009.NNS157-09-E4作为dummy map的文件