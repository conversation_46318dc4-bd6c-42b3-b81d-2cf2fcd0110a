#!/usr/bin/env python3
"""
Bump Map Tool Frame - Overlay Bump and Dummy maps for different test houses
Frame-based version for main application controller
Author: Yuribytes
Company: Chipone TE development Team
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import os
from typing import Dict, List
from nepes_enhanced_processor import NEPESEnhancedProcessor


class BumpMapToolFrame:
    """Bump Map Tool as a frame for the main application"""
    
    def __init__(self, parent, app_controller):
        self.parent = parent
        self.app_controller = app_controller
        self.main_frame = None
        
        # Variables
        self.bump_map_file_path = tk.StringVar()
        self.dummy_map_file_path = tk.StringVar()
        self.output_file_path = tk.StringVar()
        self.selected_test_house = tk.StringVar(value="")
        self.status_var = tk.StringVar(value="Ready - Select bump and dummy map files")
        self.detected_map_version = tk.IntVar(value=0)  # Auto-detected from dummy map
        self.map_version_info = tk.StringVar(value="Map Version: Not detected")

        # Processing components
        self.bump_processor = None
        self.processing_count = 0
        self.last_memory_usage = 0.0
        
        # Test house configurations (按优先级排序)
        # 优先显示的6个测试厂
        priority_test_houses = [
            ("ChipMOS", "ChipMOS Technologies"),
            ("Unisem", "Unisem Group"),
            ("TongFu", "TongFu Microelectronics"),
            ("NEPES", "NEPES Corporation"),
            ("Chipbond", "Chipbond Technology Corporation"),
            ("Powertech", "Powertech Technology")  # 替换重复的ChipMOS
        ]

        # 其他测试厂（按字母顺序排列，排除已在优先级中的）
        other_test_houses = [
            ("ASEN", "ASEN Semiconductors"),
            ("Amkor", "Amkor Technology"),
            ("ASE_Group", "ASE Group (Advanced Semiconductor Engineering)"),
            ("Carsem", "Carsem Semiconductor"),
            ("ChipPAC", "ChipPAC (STATS ChipPAC)"),
            ("Custom", "Custom Test House Format"),
            ("Formosa", "Formosa Advanced Technologies"),
            ("JCET", "Jiangsu Changjiang Electronics Technology"),
            ("KYEC", "King Yuan Electronics"),
            ("Lingsen", "Lingsen Precision Industries"),
            ("Signetics", "Signetics Corporation"),
            ("SPIL", "Siliconware Precision Industries"),
            ("Tianshui", "Tianshui Huatian Technology"),
            ("UTAC", "United Test and Assembly Center")
        ]

        # 合并测试厂列表（优先级在前）
        self.test_houses = {}
        for test_id, test_name in priority_test_houses + other_test_houses:
            self.test_houses[test_id] = test_name

        # 用于显示选中状态的变量
        self.selected_test_house_display = tk.StringVar(value="No test house selected")
        
        # Initialize processor
        self.bump_processor = None
        
        # Memory management
        self.processing_count = 0
        self.last_memory_usage = 0.0
        
        self.create_widgets()
    
    def create_widgets(self):
        """Create the Bump Map Tool interface"""
        # Main frame
        self.main_frame = ttk.Frame(self.parent, padding="15")
        
        # Title section
        title_frame = ttk.Frame(self.main_frame)
        title_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 20))

        title_label = ttk.Label(title_frame, text="Bump Map To TSK Map Tool",
                               font=("Arial", 14, "bold"))
        title_label.grid(row=0, column=0, sticky=tk.W)
        
        # File selection section
        files_frame = ttk.LabelFrame(self.main_frame, text="File Selection", padding="10")
        files_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 15))
        files_frame.columnconfigure(1, weight=1)
        
        # Bump map file selection
        ttk.Label(files_frame, text="Bump Map File:").grid(row=0, column=0, sticky=tk.W, padx=(0, 5))
        bump_entry = ttk.Entry(files_frame, textvariable=self.bump_map_file_path, width=60)
        bump_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 5))
        ttk.Button(files_frame, text="Browse...", 
                  command=self.browse_bump_map_file).grid(row=0, column=2)
        
        # Dummy map file selection
        ttk.Label(files_frame, text="Dummy Map File:").grid(row=1, column=0, sticky=tk.W, padx=(0, 5), pady=(10, 0))
        dummy_entry = ttk.Entry(files_frame, textvariable=self.dummy_map_file_path, width=60)
        dummy_entry.grid(row=1, column=1, sticky=(tk.W, tk.E), padx=(0, 5), pady=(10, 0))
        ttk.Button(files_frame, text="Browse...",
                  command=self.browse_dummy_map_file).grid(row=1, column=2, pady=(10, 0))
        
        # Test house selection section
        test_house_frame = ttk.LabelFrame(self.main_frame, text="Test House Selection", padding="10")
        test_house_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 15))
        test_house_frame.columnconfigure(0, weight=3)  # Left side for selection (更大权重)
        test_house_frame.columnconfigure(1, weight=1)  # Right side for display (较小权重)

        # Instructions
        instruction_label = ttk.Label(test_house_frame,
                                     text="Select the test house format for proper bump and dummy map alignment:",
                                     font=("Arial", 9))
        instruction_label.grid(row=0, column=0, columnspan=2, sticky=tk.W, pady=(0, 10))

        # Create left and right sections
        left_frame = ttk.Frame(test_house_frame)
        left_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 15))

        right_frame = ttk.Frame(test_house_frame)
        right_frame.grid(row=1, column=1, sticky=(tk.W, tk.E, tk.N, tk.S))

        # Test house selection with scrollable frame (left side)
        self.create_test_house_selection(left_frame)

        # Selected test house display (right side) - 紧凑版本
        self.create_selection_display(right_frame)

        # Map version auto-detection display
        version_frame = ttk.LabelFrame(self.main_frame, text="Map Version (Auto-Detected)", padding="10")
        version_frame.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 15))

        version_info_label = ttk.Label(version_frame, textvariable=self.map_version_info,
                                      font=("Arial", 10, "bold"), foreground="blue")
        version_info_label.grid(row=0, column=0, sticky=tk.W)

        version_note = ttk.Label(version_frame,
                                text="Map version is automatically detected from dummy map file",
                                font=("Arial", 8), foreground="gray")
        version_note.grid(row=1, column=0, sticky=tk.W, pady=(5, 0))

        # Output section
        output_frame = ttk.LabelFrame(self.main_frame, text="Output Settings", padding="10")
        output_frame.grid(row=4, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 15))
        output_frame.columnconfigure(1, weight=1)
        
        ttk.Label(output_frame, text="Output File:").grid(row=0, column=0, sticky=tk.W, padx=(0, 5))
        output_entry = ttk.Entry(output_frame, textvariable=self.output_file_path, width=60)
        output_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 5))
        ttk.Button(output_frame, text="Browse...", 
                  command=self.browse_output_file).grid(row=0, column=2)
        
        # Processing controls (按照Full Map Tool的布局模式)
        controls_frame = ttk.Frame(self.main_frame)
        controls_frame.grid(row=5, column=0, columnspan=2, pady=(0, 15))

        # 按钮布局：Process Maps | Clear Memory | Back | Exit
        ttk.Button(controls_frame, text="Process Maps",
                  command=self.process_maps, style="Accent.TButton").pack(side=tk.LEFT, padx=(0, 15))
        ttk.Button(controls_frame, text="Clear Memory",
                  command=self.clear_memory).pack(side=tk.LEFT, padx=(0, 15))
        ttk.Button(controls_frame, text="← Back",
                  command=self.return_to_selector).pack(side=tk.LEFT, padx=(0, 15))
        ttk.Button(controls_frame, text="Exit",
                  command=self.exit_application).pack(side=tk.LEFT)
        
        # Status bar
        status_frame = ttk.Frame(self.main_frame)
        status_frame.grid(row=6, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(10, 0))
        status_frame.columnconfigure(0, weight=1)
        
        status_label = ttk.Label(status_frame, textvariable=self.status_var, 
                                relief=tk.SUNKEN, anchor=tk.W)
        status_label.grid(row=0, column=0, sticky=(tk.W, tk.E))
        
        # Configure main frame grid weights
        self.main_frame.columnconfigure(0, weight=1)
        self.main_frame.columnconfigure(1, weight=1)
    
    def create_test_house_selection(self, parent_frame):
        """Create scrollable test house selection area - 优化布局"""
        # Create canvas and scrollbar for scrollable area
        canvas_frame = ttk.Frame(parent_frame)
        canvas_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        canvas_frame.columnconfigure(0, weight=1)
        canvas_frame.rowconfigure(0, weight=1)

        # 增加canvas宽度以容纳两栏内容
        canvas = tk.Canvas(canvas_frame, height=150, width=450)  # 明确设置宽度
        scrollbar = ttk.Scrollbar(canvas_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # Add test house radio buttons - 优化两栏布局
        row = 0
        col = 0
        for test_house_id, test_house_name in self.test_houses.items():
            radio_button = ttk.Radiobutton(scrollable_frame,
                                          text=test_house_name,
                                          variable=self.selected_test_house,
                                          value=test_house_id)
            radio_button.grid(row=row, column=col, sticky=tk.W, padx=5, pady=2)

            col += 1
            if col >= 2:  # 2 columns layout
                col = 0
                row += 1

        # 确保scrollable_frame有足够的列宽度
        scrollable_frame.columnconfigure(0, weight=1, minsize=200)
        scrollable_frame.columnconfigure(1, weight=1, minsize=200)

        # Bind mousewheel to entire test house selection area
        def _on_mousewheel(event):
            canvas.yview_scroll(int(-1*(event.delta/120)), "units")

        # Bind mousewheel to the entire canvas frame and all its children
        def bind_mousewheel_recursive(widget):
            widget.bind("<MouseWheel>", _on_mousewheel)
            for child in widget.winfo_children():
                bind_mousewheel_recursive(child)

        bind_mousewheel_recursive(canvas_frame)

        # 绑定选择变化事件
        self.selected_test_house.trace('w', self.on_test_house_selection_changed)

    def create_selection_display(self, parent_frame):
        """创建右侧选中测试厂显示区域 - 紧凑版本"""
        # 选中状态显示框 - 减少padding
        display_frame = ttk.LabelFrame(parent_frame, text="Selected", padding="5")
        display_frame.pack(fill=tk.BOTH, expand=True)

        # 显示选中的测试厂 - 更紧凑的布局
        self.selection_label = ttk.Label(display_frame,
                                        textvariable=self.selected_test_house_display,
                                        font=("Arial", 9, "bold"),  # 稍小字体
                                        foreground="blue",
                                        wraplength=150,  # 减少换行宽度
                                        justify=tk.CENTER)
        self.selection_label.pack(pady=(5, 5))

        # 添加简化的说明文字
        info_label = ttk.Label(display_frame,
                              text="Format for alignment",
                              font=("Arial", 7),  # 更小字体
                              foreground="gray",
                              justify=tk.CENTER)
        info_label.pack()

    def on_test_house_selection_changed(self, *args):
        """测试厂选择变化时的回调函数"""
        selected_id = self.selected_test_house.get()
        if selected_id and selected_id in self.test_houses:
            selected_name = self.test_houses[selected_id]
            self.selected_test_house_display.set(f"✓ {selected_name}")
            self.status_var.set(f"Test house selected: {selected_name}")
        else:
            self.selected_test_house_display.set("No test house selected")
            self.status_var.set("Ready - Select bump and dummy map files")
    
    def browse_bump_map_file(self):
        """Browse for bump map file"""
        filename = filedialog.askopenfilename(
            title="Select Bump Map File",
            filetypes=[
                ("All supported files", "*.*"),
                ("MAP files", "*.map"),
                ("TSK files", "*.tsk"),
                ("Text files", "*.txt")
            ]
        )
        if filename:
            self.bump_map_file_path.set(filename)
            self.status_var.set(f"Bump map file selected: {os.path.basename(filename)}")
    
    def browse_dummy_map_file(self):
        """Browse for dummy map file and auto-detect map version"""
        filename = filedialog.askopenfilename(
            title="Select Dummy Map File",
            filetypes=[
                ("All supported files", "*.*"),
                ("MAP files", "*.map"),
                ("TSK files", "*.tsk"),
                ("Text files", "*.txt")
            ]
        )
        if filename:
            self.dummy_map_file_path.set(filename)

            # Auto-detect map version
            self.auto_detect_map_version(filename)

            # Auto-generate output filename
            self.auto_generate_output_path(filename)

            self.status_var.set(f"Dummy map file selected: {os.path.basename(filename)}")
    
    def browse_output_file(self):
        """Browse for output file location"""
        # Get the dummy map file extension to maintain format
        dummy_file = self.dummy_map_file_path.get()
        if dummy_file:
            _, ext = os.path.splitext(dummy_file)
            default_ext = ext if ext else ".map"
        else:
            default_ext = ".map"

        # 按照要求调整格式优先级：All files → .map → .tsk
        filename = filedialog.asksaveasfilename(
            title="Save Output File As",
            defaultextension=default_ext,
            filetypes=[
                ("All files", "*.*"),
                ("MAP files", "*.map"),
                ("TSK files", "*.tsk")
            ]
        )
        if filename:
            self.output_file_path.set(filename)
            self.status_var.set(f"Output file set: {os.path.basename(filename)}")

    def auto_detect_map_version(self, dummy_file_path):
        """Auto-detect map version from dummy map file"""
        try:
            from tsk_map_processor import TSKMapProcessor

            processor = TSKMapProcessor()
            success = processor.read_file(dummy_file_path)

            if success:
                header_success = processor.parse_file_header()
                if header_success:
                    version = processor.May_version
                    self.detected_map_version.set(version)

                    version_text = f"Map Version: {version}"
                    if version == 4:
                        version_text += " (Enhanced - NEPES Compatible)"
                    elif version in [2, 3]:
                        version_text += " (Standard)"

                    self.map_version_info.set(version_text)
                    print(f"✅ Auto-detected Map Version: {version}")
                    return version
                else:
                    self.map_version_info.set("Map Version: Detection failed (header parse error)")
            else:
                self.map_version_info.set("Map Version: Detection failed (file read error)")

        except Exception as e:
            self.map_version_info.set(f"Map Version: Detection failed ({str(e)})")
            print(f"❌ Map version detection error: {e}")

        return None

    def auto_generate_output_path(self, dummy_file_path):
        """Auto-generate output path based on bump map filename - 统一使用lot-01格式（适用于所有map version）"""
        try:
            # 获取bump文件路径
            bump_file_path = self.bump_map_file_path.get()
            if not bump_file_path:
                print("⚠️ Bump file not selected, using dummy-based naming")
                # 如果没有bump文件，使用dummy文件名
                dummy_dir = os.path.dirname(dummy_file_path)
                dummy_name = os.path.splitext(os.path.basename(dummy_file_path))[0]
                dummy_ext = os.path.splitext(dummy_file_path)[1]
                output_name = f"{dummy_name}_processed{dummy_ext}"
                output_path = os.path.join(dummy_dir, output_name)
            else:
                # 使用新的lot-01格式（与map version=4一致）- 不包含文件扩展名
                dummy_dir = os.path.dirname(dummy_file_path)

                # 获取bump文件的basename（不使用splitext，因为.09等不是真正的扩展名）
                bump_basename = os.path.basename(bump_file_path)

                # 将点号替换为连字符 (例如: D97127.09 -> D97127-09)
                output_base = bump_basename.replace('.', '-')

                # 创建输出文件名: 只使用bump map名字，格式为lot-01（不包含扩展名）
                output_name = output_base
                output_path = os.path.join(dummy_dir, output_name)

            self.output_file_path.set(output_path)
            print(f"✅ Auto-generated output path: {output_name}")

        except Exception as e:
            print(f"❌ Output path generation error: {e}")

    def process_maps(self):
        """Process bump and dummy maps with enhanced NEPES support"""
        # Validate inputs
        if not self.bump_map_file_path.get():
            messagebox.showerror("Error", "Please select a bump map file")
            return

        if not self.dummy_map_file_path.get():
            messagebox.showerror("Error", "Please select a dummy map file")
            return

        if not self.selected_test_house.get():
            messagebox.showerror("Error", "Please select a test house format")
            return

        if not self.output_file_path.get():
            messagebox.showerror("Error", "Please specify an output file location")
            return

        try:
            self.status_var.set("Initializing processing...")

            # Get auto-detected map version
            map_version = self.detected_map_version.get()

            if map_version == 0:
                messagebox.showerror("Error", "Map version not detected. Please select a valid dummy map file.")
                return

            # Check if NEPES test house is selected and use enhanced processor
            if self.selected_test_house.get() == "NEPES":
                self.status_var.set(f"Using NEPES Enhanced Processor (Map Version {map_version})...")

                # Initialize NEPES Enhanced Processor
                self.bump_processor = NEPESEnhancedProcessor()

                # Process with enhanced logic
                success = self.bump_processor.process_nepes_enhanced(
                    bump_map_path=self.bump_map_file_path.get(),
                    dummy_map_path=self.dummy_map_file_path.get(),
                    output_path=self.output_file_path.get()
                )

                if success:
                    # Get processing statistics
                    stats = self.bump_processor.get_processing_stats()

                    # Show success message with statistics
                    success_msg = (
                        f"✅ NEPES Enhanced Processing Completed!\n\n"
                        f"📊 Processing Statistics:\n"
                        f"• Map Version: {map_version}\n"
                        f"• Unchanged positions (__): {stats['processing_stats']['unchanged_positions']}\n"
                        f"• Pass positions (00): {stats['processing_stats']['pass_positions']}\n"
                        f"• Fail positions (XX): {stats['processing_stats']['fail_positions']}\n"
                        f"• Total binary modifications: {stats['processing_stats']['binary_modifications']}\n\n"
                        f"📁 Output saved to: {os.path.basename(self.output_file_path.get())}"
                    )

                    messagebox.showinfo("Processing Complete", success_msg)
                    self.status_var.set("NEPES Enhanced processing completed successfully")

                    # Update processing count
                    self.processing_count += 1

                else:
                    messagebox.showerror("Processing Error", "NEPES Enhanced processing failed. Check console for details.")
                    self.status_var.set("NEPES Enhanced processing failed")

            else:
                # For other test houses, show info message (no processing)
                selected_house = self.selected_test_house.get()
                info_msg = (
                    f"Selected Configuration:\n"
                    f"• Test House: {selected_house}\n"
                    f"• Map Version: {map_version} (Auto-detected)\n\n"
                    f"ℹ️ Processing Logic:\n"
                    f"Enhanced processing is currently implemented for NEPES Corporation only.\n\n"
                    f"For {selected_house}:\n"
                    f"• Processing logic is not yet implemented\n"
                    f"• Will be added in future updates\n"
                    f"• Current focus is on NEPES format optimization\n\n"
                    f"Please select 'NEPES Corporation' to use the enhanced processing features."
                )

                messagebox.showinfo("Processing Info", info_msg)
                self.status_var.set(f"Ready - {selected_house} selected (processing not implemented)")

        except Exception as e:
            error_msg = f"Processing error: {str(e)}"
            messagebox.showerror("Error", error_msg)
            self.status_var.set("Processing failed - Check inputs and try again")
            print(f"Bump Map Processing Error: {e}")
    
    def clear_memory(self):
        """Clear memory and reset state - 复用Full Map Tool的内存清理逻辑"""
        try:
            memory_freed = 0.0
            processor_count = 0

            # Clear bump processor (similar to Full Map Tool's processors)
            if self.bump_processor:
                if hasattr(self.bump_processor, 'get_memory_usage_mb'):
                    memory_freed += self.bump_processor.get_memory_usage_mb()
                if hasattr(self.bump_processor, 'clear_memory'):
                    self.bump_processor.clear_memory()
                processor_count = 1
                self.bump_processor = None

            # Reset counters
            self.processing_count = 0
            self.last_memory_usage = 0.0

            # Always provide feedback to user (consistent with Full Map Tool)
            if memory_freed > 0:
                print(f"Bump Map Tool - Total memory cleared: ~{memory_freed:.1f} MB from {processor_count} processors")
                self.status_var.set(f"Memory cleared: {memory_freed:.1f} MB freed from {processor_count} processors")
            else:
                print("Bump Map Tool - Memory cleared (no active processors)")
                self.status_var.set("Memory cleared - no active processors")

        except Exception as e:
            print(f"Warning: Error during Bump Map memory cleanup: {e}")
            self.status_var.set("Memory cleanup completed with warnings")
    
    def return_to_selector(self):
        """Return to tool selector with automatic memory cleanup (同步Full Map Tool功能)"""
        try:
            # Get memory info before clearing (for accurate reporting)
            memory_before_clear = 0.0
            processor_count = 1 if self.bump_processor else 0

            if self.bump_processor and hasattr(self.bump_processor, 'get_memory_usage_mb'):
                memory_before_clear = self.bump_processor.get_memory_usage_mb()

            # Clear memory automatically
            self.clear_memory()
            print(f"Bump Map Tool - Memory cleared when returning to main menu: {memory_before_clear:.1f} MB freed")

            # Verify memory was actually cleared
            memory_after_clear = 0.0
            if self.bump_processor and hasattr(self.bump_processor, 'get_memory_usage_mb'):
                memory_after_clear = self.bump_processor.get_memory_usage_mb()

            # Calculate actual memory freed
            actual_memory_freed = memory_before_clear - memory_after_clear
            print(f"Bump Map Tool - Actual memory freed: {actual_memory_freed:.1f} MB (before: {memory_before_clear:.1f} MB, after: {memory_after_clear:.1f} MB)")

            # Clear state when returning
            self.bump_map_file_path.set("")
            self.dummy_map_file_path.set("")
            self.output_file_path.set("")
            self.selected_test_house.set("")
            self.status_var.set("Ready - Select bump and dummy map files")

            # Show 1-second cleanup popup with actual freed memory
            self.show_return_cleanup_popup(actual_memory_freed, processor_count)

            # Return to selector after popup
            self.app_controller.root.after(1100, self.app_controller.show_tool_selector)

        except Exception as e:
            print(f"Warning: Error during Bump Map return cleanup: {e}")
            # Still return to selector even if cleanup fails
            self.app_controller.show_tool_selector()

    def show_return_cleanup_popup(self, memory_freed: float, processor_count: int):
        """Show 1-second cleanup popup when returning to main menu (同步Full Map Tool功能)"""
        try:
            # Create popup window
            popup = tk.Toplevel(self.app_controller.root)
            popup.title("Memory Cleanup")
            popup.geometry("350x120")
            popup.resizable(False, False)

            # Center popup on parent window
            popup.transient(self.app_controller.root)
            popup.grab_set()

            # Center popup
            x = self.app_controller.root.winfo_x() + (self.app_controller.root.winfo_width() // 2) - 175
            y = self.app_controller.root.winfo_y() + (self.app_controller.root.winfo_height() // 2) - 60
            popup.geometry(f"350x120+{x}+{y}")

            # Create content
            main_frame = ttk.Frame(popup, padding="20")
            main_frame.pack(fill=tk.BOTH, expand=True)

            # Icon and message
            if memory_freed > 0:
                message = f"🔄 Returning to Main Menu\n\n{memory_freed:.1f} MB freed from {processor_count} processors"
            elif processor_count > 0:
                message = f"🔄 Returning to Main Menu\n\nProcessors cleared (no memory to free)"
            else:
                message = "🔄 Returning to Main Menu\n\nBump Map Tool cleared"

            ttk.Label(main_frame, text=message, justify=tk.CENTER,
                     font=("Arial", 10)).pack(expand=True)

            # Auto-close after 1 second
            popup.after(1000, popup.destroy)

            # Allow manual close with Escape key
            popup.bind('<Escape>', lambda e: popup.destroy())
            popup.focus_set()

        except Exception as e:
            print(f"Warning: Error showing Bump Map return cleanup popup: {e}")

    def exit_application(self):
        """Exit application with cleanup - 复用Full Map Tool的退出逻辑"""
        try:
            # Get memory info before clearing (similar to Full Map Tool)
            memory_freed = 0.0
            processor_count = 0

            # Calculate memory usage from bump processor
            if self.bump_processor and hasattr(self.bump_processor, 'get_memory_usage_mb'):
                memory_freed += self.bump_processor.get_memory_usage_mb()
                processor_count = 1

            # Clear all memory before exit
            self.clear_memory()
            print("Bump Map Tool - Application memory cleared before exit")

            # Always show cleanup popup when exiting (user feedback) - matching Full Map Tool
            self.show_memory_cleanup_popup(memory_freed, processor_count)
            # Wait a moment for popup to show before quitting
            self.app_controller.root.after(2100, self.app_controller.root.quit)

        except Exception as e:
            print(f"Warning: Error during Bump Map exit cleanup: {e}")
            self.app_controller.root.quit()

    def show_memory_cleanup_popup(self, memory_freed: float, processor_count: int):
        """Show memory cleanup popup - 复用Full Map Tool的弹窗逻辑"""
        try:
            # Create popup window
            popup = tk.Toplevel(self.app_controller.root)
            popup.title("Memory Cleanup")
            popup.geometry("350x120")
            popup.resizable(False, False)

            # Center popup on parent window
            popup.transient(self.app_controller.root)
            popup.grab_set()

            # Center popup
            x = self.app_controller.root.winfo_x() + (self.app_controller.root.winfo_width() // 2) - 175
            y = self.app_controller.root.winfo_y() + (self.app_controller.root.winfo_height() // 2) - 60
            popup.geometry(f"350x120+{x}+{y}")

            # Create content
            main_frame = ttk.Frame(popup, padding="20")
            main_frame.pack(fill=tk.BOTH, expand=True)

            # Icon and message
            if memory_freed > 0:
                message = f"✅ Memory Cleanup Complete\n\n{memory_freed:.1f} MB freed from {processor_count} processors"
            elif processor_count > 0:
                message = f"✅ Memory Cleanup Complete\n\nProcessors cleared (no memory to free)"
            else:
                message = "✅ Application Closing\n\nThank you for using Bump Map Tool!"

            ttk.Label(main_frame, text=message, justify=tk.CENTER,
                     font=("Arial", 10)).pack(expand=True)

            # Auto-close after 2 seconds
            popup.after(2000, popup.destroy)

            # Allow manual close with Escape key
            popup.bind('<Escape>', lambda e: popup.destroy())
            popup.focus_set()

        except Exception as e:
            print(f"Warning: Error showing Bump Map cleanup popup: {e}")

    def has_unsaved_work(self):
        """检查是否有未保存的工作"""
        return (self.bump_map_file_path.get() or
                self.dummy_map_file_path.get() or
                self.processing_count > 0)

    def show(self):
        """Show the Bump Map Tool frame"""
        if self.main_frame:
            self.main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

    def hide(self):
        """Hide the Bump Map Tool frame"""
        if self.main_frame:
            self.main_frame.grid_remove()


def main():
    """Test function for standalone running"""
    root = tk.Tk()
    root.title("Bump Map Tool Test")
    root.geometry("800x700")
    
    # Mock app controller for testing
    class MockController:
        def __init__(self, root):
            self.root = root
        def return_to_selector_with_confirmation(self, tool_name, has_unsaved):
            print(f"Would return to selector from {tool_name}, unsaved: {has_unsaved}")
            return True
    
    controller = MockController(root)
    bump_tool = BumpMapToolFrame(root, controller)
    bump_tool.show()
    
    root.mainloop()


if __name__ == "__main__":
    main()
